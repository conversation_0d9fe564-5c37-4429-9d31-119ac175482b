{"name": "governance-app", "version": "1.0.0", "description": "Governance App with PostgreSQL", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run start", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "start": "npm run start:backend", "start:backend": "cd backend && npm start", "start:frontend": "cd frontend && npm run preview", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "db:start": "docker-compose up database -d", "db:stop": "docker-compose stop database", "db:reset": "docker-compose down database && docker volume rm governance-app_postgres_data && docker-compose up database -d", "migrate:export": "cd migration-scripts && node export-from-supabase.js", "migrate:import": "cd migration-scripts && node import-to-postgres.js", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "clean": "rm -rf node_modules backend/node_modules frontend/node_modules backend/dist frontend/dist"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0"}}