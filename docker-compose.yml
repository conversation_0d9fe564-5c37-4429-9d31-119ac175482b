# Removed Docker configuration

version: '3.8'
services:
  database:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: governance_app
      POSTGRES_USER: governance_user
      POSTGRES_PASSWORD: governance_password
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U governance_user -d governance_app"]
      interval: 10s
      timeout: 5s
      retries: 5

  backend:
    build: ./backend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=**************************************************************/governance_app
    depends_on:
      database:
        condition: service_healthy
    restart: unless-stopped

  frontend:
    build: ./frontend
    ports:
      - "3001:3001"
    depends_on:
      - backend
    restart: unless-stopped

volumes:
  postgres_data: