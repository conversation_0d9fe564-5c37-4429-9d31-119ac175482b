-- Migration 001: Initialize Vendor Management Schema
-- Creates normalized tables for vendor data with proper constraints and relationships

-- Drop existing objects if they exist (for clean re-runs)
DROP TABLE IF EXISTS directors CASCADE;
DROP TABLE IF EXISTS shareholders CASCADE;
DROP TABLE IF EXISTS documents CASCADE;
DROP TABLE IF EXISTS reviews CASCADE;
DROP TABLE IF EXISTS risk_assessments CASCADE;
DROP TABLE IF EXISTS contracts CASCADE;
DROP TABLE IF EXISTS vendors CASCADE;
DROP TABLE IF EXISTS lead_managers CASCADE;

-- Drop enums if they exist
DROP TYPE IF EXISTS vendor_type CASCADE;
DROP TYPE IF EXISTS service_type CASCADE;
DROP TYPE IF EXISTS document_type CASCADE;
DROP TYPE IF EXISTS risk_level CASCADE;

-- Create enums for categorical data
CREATE TYPE vendor_type AS ENUM (
    'technology_provider',
    'professional_services',
    'financial_services',
    'consulting',
    'outsourcing',
    'software_vendor',
    'infrastructure',
    'other'
);

CREATE TYPE service_type AS ENUM (
    'it_services',
    'financial_advisory',
    'legal_services',
    'audit_services',
    'hr_services',
    'facilities_management',
    'security_services',
    'other'
);

CREATE TYPE document_type AS ENUM (
    'incorporation_doc',
    'share_register',
    'insurance_certificate',
    'compliance_certificate',
    'contract',
    'other'
);

CREATE TYPE risk_level AS ENUM (
    'very_low',
    'low', 
    'medium',
    'high',
    'very_high'
);

-- Lookup table for lead managers
CREATE TABLE lead_managers (
    manager_id SERIAL PRIMARY KEY,
    manager_name VARCHAR(100) NOT NULL UNIQUE,
    email VARCHAR(255),
    department VARCHAR(100),
    active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Main vendors table
CREATE TABLE vendors (
    vendor_id SERIAL PRIMARY KEY,
    company_name VARCHAR(255) NOT NULL UNIQUE,
    legal_name VARCHAR(255),
    company_address TEXT,
    business_type VARCHAR(100),
    website VARCHAR(500),
    vendor_type vendor_type NOT NULL,
    service_type service_type,
    lead_manager_id INTEGER REFERENCES lead_managers(manager_id) ON UPDATE CASCADE,
    countries_of_operation TEXT[], -- Array of country codes/names
    incorporation_date DATE,
    registration_number VARCHAR(100),
    vendor_questionnaire TEXT,
    certifications TEXT,
    edd_required BOOLEAN NOT NULL DEFAULT false,
    active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Contracts table
CREATE TABLE contracts (
    contract_id SERIAL PRIMARY KEY,
    vendor_id INTEGER NOT NULL REFERENCES vendors(vendor_id) ON DELETE CASCADE ON UPDATE CASCADE,
    active BOOLEAN NOT NULL DEFAULT true,
    start_date DATE,
    renewal_date DATE,
    end_date DATE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT valid_contract_dates CHECK (
        start_date IS NULL OR end_date IS NULL OR start_date <= end_date
    )
);

-- Risk assessments table
CREATE TABLE risk_assessments (
    assessment_id SERIAL PRIMARY KEY,
    vendor_id INTEGER NOT NULL REFERENCES vendors(vendor_id) ON DELETE CASCADE ON UPDATE CASCADE,
    assessment_date DATE NOT NULL DEFAULT CURRENT_DATE,
    operational_risk NUMERIC(3,1) CHECK (operational_risk >= 0 AND operational_risk <= 5),
    confidential_data_handling NUMERIC(3,1) CHECK (confidential_data_handling >= 0 AND confidential_data_handling <= 5),
    regulatory_risk NUMERIC(3,1) CHECK (regulatory_risk >= 0 AND regulatory_risk <= 5),
    policies NUMERIC(3,1) CHECK (policies >= 0 AND policies <= 5),
    service_level_agreement NUMERIC(3,1) CHECK (service_level_agreement >= 0 AND service_level_agreement <= 5),
    business_continuity_plan NUMERIC(3,1) CHECK (business_continuity_plan >= 0 AND business_continuity_plan <= 5),
    external_audits NUMERIC(3,1) CHECK (external_audits >= 0 AND external_audits <= 5),
    customer_verification NUMERIC(3,1) CHECK (customer_verification >= 0 AND customer_verification <= 5),
    concentration_risk NUMERIC(3,1) CHECK (concentration_risk >= 0 AND concentration_risk <= 5),
    outsourcing_risk NUMERIC(3,1) CHECK (outsourcing_risk >= 0 AND outsourcing_risk <= 5),
    overall_risk NUMERIC(3,1) CHECK (overall_risk >= 0 AND overall_risk <= 5),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Reviews table
CREATE TABLE reviews (
    review_id SERIAL PRIMARY KEY,
    vendor_id INTEGER NOT NULL REFERENCES vendors(vendor_id) ON DELETE CASCADE ON UPDATE CASCADE,
    last_review_date DATE,
    next_review_date DATE,
    review_notes TEXT,
    completed BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT valid_review_dates CHECK (
        last_review_date IS NULL OR next_review_date IS NULL OR last_review_date <= next_review_date
    )
);

-- Documents table
CREATE TABLE documents (
    document_id SERIAL PRIMARY KEY,
    vendor_id INTEGER NOT NULL REFERENCES vendors(vendor_id) ON DELETE CASCADE ON UPDATE CASCADE,
    doc_type document_type NOT NULL,
    document_name VARCHAR(255),
    file_path VARCHAR(500),
    url VARCHAR(500),
    expiry_date DATE,
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    notes TEXT,
    
    CONSTRAINT document_location_check CHECK (
        file_path IS NOT NULL OR url IS NOT NULL
    )
);

-- Shareholders table
CREATE TABLE shareholders (
    shareholder_id SERIAL PRIMARY KEY,
    vendor_id INTEGER NOT NULL REFERENCES vendors(vendor_id) ON DELETE CASCADE ON UPDATE CASCADE,
    name VARCHAR(255) NOT NULL,
    address TEXT,
    nationality VARCHAR(100),
    id_reference VARCHAR(100),
    proof_of_address_path VARCHAR(500),
    ownership_percentage NUMERIC(5,2) CHECK (ownership_percentage >= 0 AND ownership_percentage <= 100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Directors table
CREATE TABLE directors (
    director_id SERIAL PRIMARY KEY,
    vendor_id INTEGER NOT NULL REFERENCES vendors(vendor_id) ON DELETE CASCADE ON UPDATE CASCADE,
    name VARCHAR(255) NOT NULL,
    address TEXT,
    nationality VARCHAR(100),
    id_reference VARCHAR(100),
    proof_of_address_path VARCHAR(500),
    position VARCHAR(100),
    appointment_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_vendors_company_name ON vendors(company_name);
CREATE INDEX idx_vendors_vendor_type ON vendors(vendor_type);
CREATE INDEX idx_vendors_lead_manager ON vendors(lead_manager_id);
CREATE INDEX idx_contracts_vendor_id ON contracts(vendor_id);
CREATE INDEX idx_contracts_active ON contracts(active);
CREATE INDEX idx_risk_assessments_vendor_id ON risk_assessments(vendor_id);
CREATE INDEX idx_risk_assessments_date ON risk_assessments(assessment_date DESC);
CREATE INDEX idx_reviews_vendor_id ON reviews(vendor_id);
CREATE INDEX idx_reviews_next_date ON reviews(next_review_date);
CREATE INDEX idx_documents_vendor_id ON documents(vendor_id);
CREATE INDEX idx_documents_expiry ON documents(expiry_date);
CREATE INDEX idx_shareholders_vendor_id ON shareholders(vendor_id);
CREATE INDEX idx_directors_vendor_id ON directors(vendor_id);

-- GIN index for array columns
CREATE INDEX idx_vendors_countries_gin ON vendors USING GIN(countries_of_operation);

-- Insert default lead managers (can be updated via ETL)
INSERT INTO lead_managers (manager_name, email, department) VALUES
    ('John Smith', '<EMAIL>', 'Procurement'),
    ('Sarah Johnson', '<EMAIL>', 'IT'),
    ('Michael Brown', '<EMAIL>', 'Legal'),
    ('Emily Davis', '<EMAIL>', 'Finance'),
    ('David Wilson', '<EMAIL>', 'Operations');

-- Add update timestamp triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_vendors_updated_at BEFORE UPDATE ON vendors
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_contracts_updated_at BEFORE UPDATE ON contracts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_reviews_updated_at BEFORE UPDATE ON reviews
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_shareholders_updated_at BEFORE UPDATE ON shareholders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_directors_updated_at BEFORE UPDATE ON directors
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column(); 