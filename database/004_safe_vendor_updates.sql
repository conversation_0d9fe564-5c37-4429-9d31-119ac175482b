-- Migration 004: Safe Vendor Schema Updates
-- <PERSON><PERSON><PERSON> handles view dependencies when updating enums

-- First, drop the view that depends on the columns
DROP VIEW IF EXISTS vw_vendor_overview CASCADE;

-- Add annual_cost column if it doesn't exist (this part succeeded before)
ALTER TABLE vendors ADD COLUMN IF NOT EXISTS annual_cost DECIMAL(12,2) CHECK (annual_cost >= 0);

-- Add continuous column to contracts table (this part succeeded before)  
ALTER TABLE contracts ADD COLUMN IF NOT EXISTS continuous BOOLEAN DEFAULT FALSE;

-- Create new enum types
DROP TYPE IF EXISTS new_vendor_type CASCADE;
CREATE TYPE new_vendor_type AS ENUM (
    'critical',
    'high', 
    'moderate',
    'low'
);

DROP TYPE IF EXISTS new_service_type CASCADE;
CREATE TYPE new_service_type AS ENUM (
    'it_infrastructure',
    'software_development',
    'aml_services',
    'marketing_tools',
    'cybersecurity',
    'communication',
    'finances'
);

-- Add new columns with the new types
ALTER TABLE vendors ADD COLUMN IF NOT EXISTS risk_status new_vendor_type;
ALTER TABLE vendors ADD COLUMN IF NOT EXISTS new_service_type new_service_type;

-- Update data to match new enum values
UPDATE vendors SET risk_status = 
    CASE 
        WHEN vendor_type::text = 'technology_provider' THEN 'moderate'::new_vendor_type
        WHEN vendor_type::text = 'professional_services' THEN 'low'::new_vendor_type
        WHEN vendor_type::text = 'financial_services' THEN 'high'::new_vendor_type
        WHEN vendor_type::text = 'consulting' THEN 'low'::new_vendor_type
        WHEN vendor_type::text = 'outsourcing' THEN 'high'::new_vendor_type
        WHEN vendor_type::text = 'software_vendor' THEN 'moderate'::new_vendor_type
        WHEN vendor_type::text = 'infrastructure' THEN 'moderate'::new_vendor_type
        ELSE 'low'::new_vendor_type
    END
WHERE risk_status IS NULL;

UPDATE vendors SET new_service_type = 
    CASE 
        WHEN service_type::text = 'it_services' THEN 'it_infrastructure'::new_service_type
        WHEN service_type::text = 'financial_advisory' THEN 'finances'::new_service_type
        WHEN service_type::text = 'legal_services' THEN 'finances'::new_service_type
        WHEN service_type::text = 'audit_services' THEN 'finances'::new_service_type
        WHEN service_type::text = 'hr_services' THEN 'communication'::new_service_type
        WHEN service_type::text = 'facilities_management' THEN 'it_infrastructure'::new_service_type
        WHEN service_type::text = 'security_services' THEN 'cybersecurity'::new_service_type
        ELSE 'it_infrastructure'::new_service_type
    END
WHERE new_service_type IS NULL;

-- Drop old columns
ALTER TABLE vendors DROP COLUMN IF EXISTS vendor_type;
ALTER TABLE vendors DROP COLUMN IF EXISTS service_type;

-- Rename new columns
ALTER TABLE vendors RENAME COLUMN risk_status TO vendor_type;
ALTER TABLE vendors RENAME COLUMN new_service_type TO service_type;

-- Drop old enum types and rename new ones
DROP TYPE IF EXISTS vendor_type CASCADE;
DROP TYPE IF EXISTS service_type CASCADE;
ALTER TYPE new_vendor_type RENAME TO vendor_type;
ALTER TYPE new_service_type RENAME TO service_type;

-- Set constraints
ALTER TABLE vendors ALTER COLUMN vendor_type SET NOT NULL;
ALTER TABLE vendors ALTER COLUMN service_type SET NOT NULL;
ALTER TABLE vendors ALTER COLUMN vendor_type SET DEFAULT 'low'::vendor_type;
ALTER TABLE vendors ALTER COLUMN service_type SET DEFAULT 'it_infrastructure'::service_type;

-- Recreate the view with updated schema
CREATE VIEW vw_vendor_overview AS
SELECT 
    v.vendor_id,
    v.company_name,
    v.website,
    v.vendor_type,
    v.service_type,
    lm.manager_name as lead_manager,
    lm.email as lead_manager_email,
    v.countries_of_operation,
    v.incorporation_date,
    v.registration_number,
    v.edd_required,
    v.active as vendor_active,
    v.annual_cost,
    -- Current contract details (most recent active contract)
    c.contract_id,
    c.active as contract_active,
    c.start_date as contract_start_date,
    c.renewal_date as contract_renewal_date,
    c.end_date as contract_end_date,
    c.notes as contract_notes,
    c.continuous as contract_continuous,
    -- Contract status indicators
    CASE 
        WHEN c.continuous = true AND c.active = true THEN 'continuous'
        WHEN c.end_date < CURRENT_DATE THEN 'expired'
        WHEN c.renewal_date < CURRENT_DATE + INTERVAL '90 days' THEN 'renewal_due'
        WHEN c.active = true THEN 'active'
        ELSE 'inactive'
    END as contract_status,
    v.created_at,
    v.updated_at
FROM vendors v
LEFT JOIN lead_managers lm ON v.lead_manager_id = lm.manager_id
LEFT JOIN LATERAL (
    SELECT *
    FROM contracts c2
    WHERE c2.vendor_id = v.vendor_id
    ORDER BY c2.active DESC, c2.created_at DESC
    LIMIT 1
) c ON true; 