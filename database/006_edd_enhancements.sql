-- Migration 006: Enhanced EDD Functionality
-- Adds comprehensive shareholder and senior management tables for EDD compliance

-- Drop existing tables if they exist (for clean re-runs)
DROP TABLE IF EXISTS shareholder_documents CASCADE;
DROP TABLE IF EXISTS senior_management CASCADE;
DROP TABLE IF EXISTS corporate_documents CASCADE;
DROP TABLE IF EXISTS individual_shareholders CASCADE;
DROP TABLE IF EXISTS corporate_shareholders CASCADE;

-- Drop old shareholders table and recreate with better structure
DROP TABLE IF EXISTS shareholders CASCADE;

-- Create enum for shareholder types
DROP TYPE IF EXISTS shareholder_type CASCADE;
CREATE TYPE shareholder_type AS ENUM ('individual', 'corporate');

-- Create enum for document types for EDD
DROP TYPE IF EXISTS edd_document_type CASCADE;
CREATE TYPE edd_document_type AS ENUM (
    'id_document',
    'proof_of_address', 
    'corporate_registry',
    'share_registry',
    'director_registry',
    'other'
);

-- Main shareholders table with enhanced fields
CREATE TABLE shareholders (
    shareholder_id SERIAL PRIMARY KEY,
    vendor_id INTEGER NOT NULL REFERENCES vendors(vendor_id) ON DELETE CASCADE ON UPDATE CASCADE,
    shareholder_type shareholder_type NOT NULL,
    shareholding_percentage NUMERIC(5,2) CHECK (shareholding_percentage >= 0 AND shareholding_percentage <= 100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Individual shareholders table
CREATE TABLE individual_shareholders (
    individual_shareholder_id SERIAL PRIMARY KEY,
    shareholder_id INTEGER NOT NULL REFERENCES shareholders(shareholder_id) ON DELETE CASCADE ON UPDATE CASCADE,
    name VARCHAR(255) NOT NULL,
    date_of_birth DATE,
    address TEXT,
    nationality VARCHAR(100),
    country_of_residency VARCHAR(100),
    occupation VARCHAR(255),
    email VARCHAR(255),
    phone_number VARCHAR(50),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Corporate shareholders table
CREATE TABLE corporate_shareholders (
    corporate_shareholder_id SERIAL PRIMARY KEY,
    shareholder_id INTEGER NOT NULL REFERENCES shareholders(shareholder_id) ON DELETE CASCADE ON UPDATE CASCADE,
    legal_name VARCHAR(255) NOT NULL,
    brand_name VARCHAR(255),
    registration_number VARCHAR(100),
    country_of_registration VARCHAR(100),
    legal_address TEXT,
    corporate_address TEXT,
    business_type VARCHAR(255),
    email VARCHAR(255),
    phone_number VARCHAR(50),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Senior management table (for corporate shareholders)
CREATE TABLE senior_management (
    senior_management_id SERIAL PRIMARY KEY,
    corporate_shareholder_id INTEGER NOT NULL REFERENCES corporate_shareholders(corporate_shareholder_id) ON DELETE CASCADE ON UPDATE CASCADE,
    name VARCHAR(255) NOT NULL,
    role VARCHAR(255) NOT NULL,
    date_of_birth DATE,
    address TEXT,
    nationality VARCHAR(100),
    country_of_residency VARCHAR(100),
    occupation VARCHAR(255),
    email VARCHAR(255),
    phone_number VARCHAR(50),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enhanced documents table for EDD
CREATE TABLE shareholder_documents (
    document_id SERIAL PRIMARY KEY,
    vendor_id INTEGER NOT NULL REFERENCES vendors(vendor_id) ON DELETE CASCADE ON UPDATE CASCADE,
    shareholder_id INTEGER REFERENCES shareholders(shareholder_id) ON DELETE CASCADE ON UPDATE CASCADE,
    senior_management_id INTEGER REFERENCES senior_management(senior_management_id) ON DELETE CASCADE ON UPDATE CASCADE,
    doc_type edd_document_type NOT NULL,
    document_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500),
    url VARCHAR(500),
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    notes TEXT,
    
    CONSTRAINT document_location_check CHECK (
        file_path IS NOT NULL OR url IS NOT NULL
    ),
    -- Must be associated with either a shareholder or senior management, but not both
    CONSTRAINT document_association_check CHECK (
        (shareholder_id IS NOT NULL AND senior_management_id IS NULL) OR
        (shareholder_id IS NULL AND senior_management_id IS NOT NULL)
    )
);

-- Corporate documents table (for corporate shareholders only)
CREATE TABLE corporate_documents (
    corporate_document_id SERIAL PRIMARY KEY,
    corporate_shareholder_id INTEGER NOT NULL REFERENCES corporate_shareholders(corporate_shareholder_id) ON DELETE CASCADE ON UPDATE CASCADE,
    doc_type edd_document_type NOT NULL,
    document_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500),
    url VARCHAR(500),
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    notes TEXT,
    
    CONSTRAINT corporate_document_location_check CHECK (
        file_path IS NOT NULL OR url IS NOT NULL
    )
);

-- Create indexes for performance
CREATE INDEX idx_shareholders_vendor_id ON shareholders(vendor_id);
CREATE INDEX idx_shareholders_type ON shareholders(shareholder_type);
CREATE INDEX idx_individual_shareholders_shareholder_id ON individual_shareholders(shareholder_id);
CREATE INDEX idx_corporate_shareholders_shareholder_id ON corporate_shareholders(shareholder_id);
CREATE INDEX idx_senior_management_corporate_id ON senior_management(corporate_shareholder_id);
CREATE INDEX idx_shareholder_documents_vendor_id ON shareholder_documents(vendor_id);
CREATE INDEX idx_shareholder_documents_shareholder_id ON shareholder_documents(shareholder_id);
CREATE INDEX idx_shareholder_documents_senior_management_id ON shareholder_documents(senior_management_id);
CREATE INDEX idx_corporate_documents_corporate_id ON corporate_documents(corporate_shareholder_id);

-- Add update timestamp triggers
CREATE TRIGGER update_shareholders_updated_at BEFORE UPDATE ON shareholders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_individual_shareholders_updated_at BEFORE UPDATE ON individual_shareholders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_corporate_shareholders_updated_at BEFORE UPDATE ON corporate_shareholders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_senior_management_updated_at BEFORE UPDATE ON senior_management
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create views for easier data retrieval

-- View: Complete shareholder information
CREATE VIEW vw_complete_shareholders AS
SELECT 
    s.shareholder_id,
    s.vendor_id,
    s.shareholder_type,
    s.shareholding_percentage,
    -- Individual shareholder details
    i.name as individual_name,
    i.date_of_birth as individual_dob,
    i.address as individual_address,
    i.nationality as individual_nationality,
    i.country_of_residency as individual_country_residency,
    i.occupation as individual_occupation,
    i.email as individual_email,
    i.phone_number as individual_phone,
    i.notes as individual_notes,
    -- Corporate shareholder details
    c.legal_name as corporate_legal_name,
    c.brand_name as corporate_brand_name,
    c.registration_number as corporate_registration,
    c.country_of_registration as corporate_country_registration,
    c.legal_address as corporate_legal_address,
    c.corporate_address as corporate_address,
    c.business_type as corporate_business_type,
    c.email as corporate_email,
    c.phone_number as corporate_phone,
    c.notes as corporate_notes,
    s.created_at,
    s.updated_at
FROM shareholders s
LEFT JOIN individual_shareholders i ON s.shareholder_id = i.shareholder_id
LEFT JOIN corporate_shareholders c ON s.shareholder_id = c.shareholder_id;

-- View: Senior management with corporate shareholder info
CREATE VIEW vw_senior_management_details AS
SELECT 
    sm.senior_management_id,
    sm.corporate_shareholder_id,
    cs.shareholder_id,
    s.vendor_id,
    cs.legal_name as corporate_name,
    sm.name,
    sm.role,
    sm.date_of_birth,
    sm.address,
    sm.nationality,
    sm.country_of_residency,
    sm.occupation,
    sm.email,
    sm.phone_number,
    sm.notes,
    sm.created_at,
    sm.updated_at
FROM senior_management sm
JOIN corporate_shareholders cs ON sm.corporate_shareholder_id = cs.corporate_shareholder_id
JOIN shareholders s ON cs.shareholder_id = s.shareholder_id;

-- View: Vendor EDD summary
CREATE VIEW vw_vendor_edd_summary AS
SELECT 
    v.vendor_id,
    v.company_name,
    v.edd_required,
    COUNT(s.shareholder_id) as total_shareholders,
    COUNT(s.shareholder_id) FILTER (WHERE s.shareholder_type = 'individual') as individual_shareholders,
    COUNT(s.shareholder_id) FILTER (WHERE s.shareholder_type = 'corporate') as corporate_shareholders,
    COUNT(sm.senior_management_id) as total_senior_management,
    COUNT(sd.document_id) as total_documents
FROM vendors v
LEFT JOIN shareholders s ON v.vendor_id = s.vendor_id
LEFT JOIN corporate_shareholders cs ON s.shareholder_id = cs.shareholder_id
LEFT JOIN senior_management sm ON cs.corporate_shareholder_id = sm.corporate_shareholder_id
LEFT JOIN shareholder_documents sd ON v.vendor_id = sd.vendor_id
WHERE v.edd_required = true
GROUP BY v.vendor_id, v.company_name, v.edd_required; 