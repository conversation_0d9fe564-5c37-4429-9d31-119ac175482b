-- Holidays/Vacation Management
-- Add holiday tracking for employees

-- Create holidays table
CREATE TABLE IF NOT EXISTS holidays (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    member_id UUID NOT NULL REFERENCES members(id) ON DELETE CASCADE,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    reason TEXT,
    days_taken INTEGER GENERATED ALWAYS AS (
        CASE 
            WHEN start_date IS NOT NULL AND end_date IS NOT NULL 
            THEN (end_date - start_date) + 1
            ELSE 0
        END
    ) STORED,
    status VARCHAR(20) DEFAULT 'approved' CHECK (status IN ('pending', 'approved', 'rejected')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT valid_holiday_dates CHECK (start_date <= end_date)
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_holidays_member_id ON holidays(member_id);
CREATE INDEX IF NOT EXISTS idx_holidays_start_date ON holidays(start_date);
CREATE INDEX IF NOT EXISTS idx_holidays_end_date ON holidays(end_date);

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_holidays_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_holidays_updated_at
    BEFORE UPDATE ON holidays
    FOR EACH ROW
    EXECUTE FUNCTION update_holidays_updated_at();

-- Add function to calculate remaining vacation days for a member
CREATE OR REPLACE FUNCTION calculate_remaining_vacation_days(member_uuid UUID, holiday_year INTEGER DEFAULT EXTRACT(YEAR FROM CURRENT_DATE))
RETURNS INTEGER AS $$
DECLARE
    yearly_allowance INTEGER;
    days_used INTEGER;
BEGIN
    -- Get the member's yearly holiday allowance
    SELECT yearly_holiday_allowance INTO yearly_allowance
    FROM members 
    WHERE id = member_uuid;
    
    -- If no allowance is set, return 0
    IF yearly_allowance IS NULL THEN
        RETURN 0;
    END IF;
    
    -- Calculate days used in the specified year
    SELECT COALESCE(SUM(days_taken), 0) INTO days_used
    FROM holidays 
    WHERE member_id = member_uuid 
      AND status = 'approved'
      AND EXTRACT(YEAR FROM start_date) = holiday_year;
    
    -- Return remaining days
    RETURN yearly_allowance - days_used;
END;
$$ LANGUAGE plpgsql;

-- Add function to get holiday summary for a member
CREATE OR REPLACE FUNCTION get_member_holiday_summary(member_uuid UUID, holiday_year INTEGER DEFAULT EXTRACT(YEAR FROM CURRENT_DATE))
RETURNS TABLE (
    member_id UUID,
    yearly_allowance INTEGER,
    days_used INTEGER,
    remaining_days INTEGER,
    holiday_count INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        m.id as member_id,
        m.yearly_holiday_allowance as yearly_allowance,
        COALESCE(SUM(h.days_taken), 0)::INTEGER as days_used,
        (m.yearly_holiday_allowance - COALESCE(SUM(h.days_taken), 0))::INTEGER as remaining_days,
        COUNT(h.id)::INTEGER as holiday_count
    FROM members m
    LEFT JOIN holidays h ON m.id = h.member_id 
        AND h.status = 'approved'
        AND EXTRACT(YEAR FROM h.start_date) = holiday_year
    WHERE m.id = member_uuid
    GROUP BY m.id, m.yearly_holiday_allowance;
END;
$$ LANGUAGE plpgsql;
