-- Performance Reports Enhancement
-- Add performance tracking to employees/members

-- Create performance_reports table
CREATE TABLE IF NOT EXISTS performance_reports (
    id SERIAL PRIMARY KEY,
    member_id UUID NOT NULL REFERENCES members(id) ON DELETE CASCADE,
    review_date DATE NOT NULL,
    next_review_date DATE NOT NULL,
    kpi_data JSONB DEFAULT '[]'::jsonb,
    review_notes TEXT,
    report_document_url VARCHAR(500),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'overdue')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add performance tracking fields to members table
ALTER TABLE members 
ADD COLUMN IF NOT EXISTS last_performance_review DATE,
ADD COLUMN IF NOT EXISTS next_performance_review DATE,
ADD COLUMN IF NOT EXISTS performance_review_count INTEGER DEFAULT 0;

-- Create index for performance reports
CREATE INDEX IF NOT EXISTS idx_performance_reports_member_id ON performance_reports(member_id);
CREATE INDEX IF NOT EXISTS idx_performance_reports_next_review ON performance_reports(next_review_date);
CREATE INDEX IF NOT EXISTS idx_members_next_performance_review ON members(next_performance_review);

-- Create function to automatically calculate next review date
CREATE OR REPLACE FUNCTION calculate_next_review_date(review_date DATE)
RETURNS DATE AS $$
BEGIN
    RETURN review_date + INTERVAL '6 months';
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update next review date when a performance report is completed
CREATE OR REPLACE FUNCTION update_member_review_dates()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.status = 'completed' THEN
        UPDATE members 
        SET 
            last_performance_review = NEW.review_date,
            next_performance_review = NEW.next_review_date,
            performance_review_count = performance_review_count + 1,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = NEW.member_id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_member_review_dates
    AFTER UPDATE ON performance_reports
    FOR EACH ROW
    EXECUTE FUNCTION update_member_review_dates();

-- Create trigger to set initial next review date when a new member is added
CREATE OR REPLACE FUNCTION set_initial_review_date()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.joining_date IS NOT NULL AND NEW.next_performance_review IS NULL THEN
        NEW.next_performance_review := calculate_next_review_date(NEW.joining_date);
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_set_initial_review_date
    BEFORE INSERT ON members
    FOR EACH ROW
    EXECUTE FUNCTION set_initial_review_date();