-- Migration 003: Update Vendor Schema for Risk Status and Service Types
-- Adds annual_cost field, updates enums, and adds continuous contract support

-- Add annual_cost column to vendors table
ALTER TABLE vendors ADD COLUMN IF NOT EXISTS annual_cost DECIMAL(12,2) CHECK (annual_cost >= 0);

-- Add continuous column to contracts table  
ALTER TABLE contracts ADD COLUMN IF NOT EXISTS continuous BOOLEAN DEFAULT FALSE;

-- Update vendor_type enum to risk status values
-- First create the new enum
DROP TYPE IF EXISTS risk_status CASCADE;
CREATE TYPE risk_status AS ENUM (
    'critical',
    'high', 
    'moderate',
    'low'
);

-- Drop and recreate updated service_type enum
DROP TYPE IF EXISTS service_type_new CASCADE;
CREATE TYPE service_type_new AS ENUM (
    'it_infrastructure',
    'software_development',
    'aml_services',
    'marketing_tools',
    'cybersecurity',
    'communication',
    'finances'
);

-- Add temporary columns with new types
ALTER TABLE vendors ADD COLUMN vendor_type_new risk_status;
ALTER TABLE vendors ADD COLUMN service_type_new service_type_new;

-- Update existing data to match new enum values
UPDATE vendors SET vendor_type_new = 
    CASE 
        WHEN vendor_type = 'technology_provider' THEN 'moderate'::risk_status
        WHEN vendor_type = 'professional_services' THEN 'low'::risk_status
        WHEN vendor_type = 'financial_services' THEN 'high'::risk_status
        WHEN vendor_type = 'consulting' THEN 'low'::risk_status
        WHEN vendor_type = 'outsourcing' THEN 'high'::risk_status
        WHEN vendor_type = 'software_vendor' THEN 'moderate'::risk_status
        WHEN vendor_type = 'infrastructure' THEN 'moderate'::risk_status
        ELSE 'low'::risk_status
    END;

UPDATE vendors SET service_type_new = 
    CASE 
        WHEN service_type = 'it_services' THEN 'it_infrastructure'::service_type_new
        WHEN service_type = 'financial_advisory' THEN 'finances'::service_type_new
        WHEN service_type = 'legal_services' THEN 'finances'::service_type_new
        WHEN service_type = 'audit_services' THEN 'finances'::service_type_new
        WHEN service_type = 'hr_services' THEN 'communication'::service_type_new
        WHEN service_type = 'facilities_management' THEN 'it_infrastructure'::service_type_new
        WHEN service_type = 'security_services' THEN 'cybersecurity'::service_type_new
        ELSE 'it_infrastructure'::service_type_new
    END;

-- Drop old columns and rename new ones
ALTER TABLE vendors DROP COLUMN vendor_type;
ALTER TABLE vendors DROP COLUMN service_type;
ALTER TABLE vendors RENAME COLUMN vendor_type_new TO vendor_type;
ALTER TABLE vendors RENAME COLUMN service_type_new TO service_type;

-- Drop old enum types
DROP TYPE IF EXISTS vendor_type CASCADE;
DROP TYPE IF EXISTS service_type CASCADE;

-- Rename new enum types
ALTER TYPE risk_status RENAME TO vendor_type;
ALTER TYPE service_type_new RENAME TO service_type;

-- Add NOT NULL constraints
ALTER TABLE vendors ALTER COLUMN vendor_type SET NOT NULL;
ALTER TABLE vendors ALTER COLUMN service_type SET NOT NULL;

-- Update the constraints and defaults
ALTER TABLE vendors ALTER COLUMN vendor_type SET DEFAULT 'low'::vendor_type;
ALTER TABLE vendors ALTER COLUMN service_type SET DEFAULT 'it_infrastructure'::service_type; 