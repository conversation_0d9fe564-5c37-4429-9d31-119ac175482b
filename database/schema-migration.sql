-- Schema Migration: Align PostgreSQL with Supabase Schema
-- This adds missing columns and adjusts existing schema to match Supabase

-- Add missing columns to departments
ALTER TABLE departments ADD COLUMN IF NOT EXISTS description TEXT;

-- Add missing columns to members (committee_id is legacy but keep for migration)
ALTER TABLE members ADD COLUMN IF NOT EXISTS committee_id UUID;

-- Policies table adjustments
ALTER TABLE policies ADD COLUMN IF NOT EXISTS description TEXT;
ALTER TABLE policies ADD COLUMN IF NOT EXISTS tags JSONB;
-- Add name column and copy from policy_name, then we can handle mapping
ALTER TABLE policies ADD COLUMN IF NOT EXISTS name VARCHAR(255);
UPDATE policies SET name = policy_name WHERE name IS NULL;

-- Trainings table adjustments  
ALTER TABLE trainings ADD COLUMN IF NOT EXISTS training_name VARCHAR(255);
ALTER TABLE trainings ADD COLUMN IF NOT EXISTS quiz_template TEXT;
ALTER TABLE trainings ADD COLUMN IF NOT EXISTS relevant_policy TEXT;
ALTER TABLE trainings ADD COLUMN IF NOT EXISTS training_material TEXT;
-- Copy name to training_name for compatibility
UPDATE trainings SET training_name = name WHERE training_name IS NULL;

-- Meetings table adjustments
ALTER TABLE meetings DROP COLUMN IF EXISTS title;
ALTER TABLE meetings DROP COLUMN IF EXISTS time;
ALTER TABLE meetings DROP COLUMN IF EXISTS location;
ALTER TABLE meetings DROP COLUMN IF EXISTS agenda;
ALTER TABLE meetings DROP COLUMN IF EXISTS notes;
ALTER TABLE meetings ADD COLUMN IF NOT EXISTS meeting_type VARCHAR(255);
ALTER TABLE meetings ADD COLUMN IF NOT EXISTS agenda_link TEXT;
ALTER TABLE meetings ADD COLUMN IF NOT EXISTS minutes_link TEXT;
ALTER TABLE meetings ADD COLUMN IF NOT EXISTS topics_discussed TEXT;

-- Training sessions adjustments
ALTER TABLE training_sessions ADD COLUMN IF NOT EXISTS auto_generated BOOLEAN DEFAULT FALSE;

-- Committee memberships adjustments  
ALTER TABLE committee_memberships ADD COLUMN IF NOT EXISTS committee_member_since DATE;

-- Attendance table adjustments
ALTER TABLE attendance ADD COLUMN IF NOT EXISTS training_session_id UUID;
-- Copy session_id to training_session_id for compatibility
UPDATE attendance SET training_session_id = session_id WHERE training_session_id IS NULL; 