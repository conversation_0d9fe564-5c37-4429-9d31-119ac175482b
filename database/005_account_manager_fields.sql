-- Add account manager fields to vendors table
ALTER TABLE vendors 
ADD COLUMN account_manager_assigned BOOLEAN DEFAULT TRUE,
ADD COLUMN account_manager_name VARCHAR(255),
ADD COLUMN account_manager_email VARCHAR(255),
ADD COLUMN account_manager_phone VARCHAR(50);

-- Drop and recreate the vendor overview view to include account manager fields and review information
DROP VIEW IF EXISTS vw_vendor_overview;

CREATE VIEW vw_vendor_overview AS
SELECT 
  v.vendor_id,
  v.company_name,
  v.website,
  v.vendor_type,
  v.service_type,
  COALESCE(lm.manager_name, '') as lead_manager,
  COALESCE(lm.email, '') as lead_manager_email,
  v.countries_of_operation,
  v.incorporation_date,
  v.registration_number,
  v.edd_required,
  v.active as vendor_active,
  v.annual_cost,
  v.account_manager_assigned,
  v.account_manager_name,
  v.account_manager_email,
  v.account_manager_phone,
  v.created_at,
  v.updated_at,
  -- Contract information
  c.contract_id,
  COALESCE(c.active, false) as contract_active,
  c.start_date as contract_start_date,
  c.renewal_date as contract_renewal_date,
  c.end_date as contract_end_date,
  c.notes as contract_notes,
  COALESCE(c.continuous, false) as contract_continuous,
  CASE 
    WHEN c.active = true AND c.continuous = true THEN 'continuous'
    WHEN c.active = true AND c.end_date IS NOT NULL AND c.end_date > CURRENT_DATE THEN 'active'
    WHEN c.active = true AND c.end_date IS NOT NULL AND c.end_date <= CURRENT_DATE THEN 'expired'
    WHEN c.active = true THEN 'active'
    ELSE 'inactive'
  END as contract_status,
  -- Review information
  r.review_id,
  r.last_review_date,
  r.next_review_date,
  r.review_notes,
  r.completed as review_completed
FROM vendors v
LEFT JOIN lead_managers lm ON v.lead_manager_id = lm.manager_id
LEFT JOIN contracts c ON v.vendor_id = c.vendor_id
LEFT JOIN LATERAL (
  SELECT review_id, last_review_date, next_review_date, review_notes, completed
  FROM reviews r2 
  WHERE r2.vendor_id = v.vendor_id 
  ORDER BY r2.created_at DESC 
  LIMIT 1
) r ON true;

-- Update existing vendors to have account_manager_assigned = true by default
UPDATE vendors SET account_manager_assigned = TRUE WHERE account_manager_assigned IS NULL; 