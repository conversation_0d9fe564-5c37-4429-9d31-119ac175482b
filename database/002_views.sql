-- Migration 002: Create Views for Vendor Management UI
-- Provides read-only views that group related slices of data for different UI sections

-- Drop existing views if they exist
DROP VIEW IF EXISTS vw_upcoming_reviews;
DROP VIEW IF EXISTS vw_docs_expiring;
DROP VIEW IF EXISTS vw_vendor_ownership;
DROP VIEW IF EXISTS vw_vendor_risk_summary;
DROP VIEW IF EXISTS vw_vendor_overview;

-- View 1: Vendor Overview with Current Contract
-- Combines vendor info with their most recent active contract
CREATE VIEW vw_vendor_overview AS
SELECT 
    v.vendor_id,
    v.company_name,
    v.website,
    v.vendor_type,
    v.service_type,
    lm.manager_name as lead_manager,
    lm.email as lead_manager_email,
    v.countries_of_operation,
    v.incorporation_date,
    v.registration_number,
    v.edd_required,
    v.active as vendor_active,
    v.annual_cost,
    -- Current contract details (most recent active contract)
    c.contract_id,
    c.active as contract_active,
    c.start_date as contract_start_date,
    c.renewal_date as contract_renewal_date,
    c.end_date as contract_end_date,
    c.notes as contract_notes,
    c.continuous as contract_continuous,
    -- Contract status indicators
    CASE 
        WHEN c.continuous = true AND c.active = true THEN 'continuous'
        WHEN c.end_date < CURRENT_DATE THEN 'expired'
        WHEN c.renewal_date < CURRENT_DATE + INTERVAL '90 days' THEN 'renewal_due'
        WHEN c.active = true THEN 'active'
        ELSE 'inactive'
    END as contract_status,
    v.created_at,
    v.updated_at
FROM vendors v
LEFT JOIN lead_managers lm ON v.lead_manager_id = lm.manager_id
LEFT JOIN LATERAL (
    SELECT *
    FROM contracts c2
    WHERE c2.vendor_id = v.vendor_id
    ORDER BY c2.active DESC, c2.created_at DESC
    LIMIT 1
) c ON true;

-- View 2: Vendor Risk Summary with Latest Assessment
-- Shows vendors with their most recent risk assessment scores
CREATE VIEW vw_vendor_risk_summary AS
SELECT 
    v.vendor_id,
    v.company_name,
    v.vendor_type,
    lm.manager_name as lead_manager,
    -- Latest risk assessment
    ra.assessment_id,
    ra.assessment_date,
    ra.operational_risk,
    ra.confidential_data_handling,
    ra.physical_security_risk,
    ra.outsourcing_risk,
    ra.geopolitical_risk,
    ra.information_security_risk,
    ra.subcontractor_risk,
    ra.legal_risk,
    ra.overall_risk_score,
    -- Risk level categorization
    CASE 
        WHEN ra.overall_risk_score >= 4.0 THEN 'very_high'
        WHEN ra.overall_risk_score >= 3.0 THEN 'high'
        WHEN ra.overall_risk_score >= 2.0 THEN 'medium'
        WHEN ra.overall_risk_score >= 1.0 THEN 'low'
        ELSE 'very_low'
    END as risk_category,
    -- Assessment age in days
    CURRENT_DATE - ra.assessment_date as days_since_assessment,
    -- Flag for stale assessments (older than 1 year)
    CASE 
        WHEN ra.assessment_date < CURRENT_DATE - INTERVAL '1 year' THEN true
        WHEN ra.assessment_date IS NULL THEN true
        ELSE false
    END as assessment_stale
FROM vendors v
LEFT JOIN lead_managers lm ON v.lead_manager_id = lm.manager_id
LEFT JOIN LATERAL (
    SELECT *
    FROM risk_assessments ra2
    WHERE ra2.vendor_id = v.vendor_id
    ORDER BY ra2.assessment_date DESC
    LIMIT 1
) ra ON true
WHERE v.active = true;

-- View 3: Vendor Ownership Details
-- Combines vendor info with shareholders and directors
CREATE VIEW vw_vendor_ownership AS
SELECT 
    v.vendor_id,
    v.company_name,
    v.registration_number,
    -- Shareholder information
    s.shareholder_id,
    s.name as shareholder_name,
    s.address as shareholder_address,
    s.nationality as shareholder_nationality,
    s.ownership_percentage,
    -- Director information
    d.director_id,
    d.name as director_name,
    d.address as director_address,
    d.nationality as director_nationality,
    d.position as director_position,
    d.appointment_date,
    -- Type indicator
    CASE 
        WHEN s.shareholder_id IS NOT NULL AND d.director_id IS NOT NULL THEN 'both'
        WHEN s.shareholder_id IS NOT NULL THEN 'shareholder'
        WHEN d.director_id IS NOT NULL THEN 'director'
        ELSE 'none'
    END as person_type
FROM vendors v
LEFT JOIN shareholders s ON v.vendor_id = s.vendor_id
LEFT JOIN directors d ON v.vendor_id = d.vendor_id
WHERE v.active = true
    AND (s.shareholder_id IS NOT NULL OR d.director_id IS NOT NULL);

-- View 4: Documents Expiring Soon
-- Shows documents that are expiring within the next 90 days
CREATE VIEW vw_docs_expiring AS
SELECT 
    v.vendor_id,
    v.company_name,
    lm.manager_name as lead_manager,
    lm.email as lead_manager_email,
    d.document_id,
    d.doc_type,
    d.document_name,
    d.expiry_date,
    -- Days until expiry
    d.expiry_date - CURRENT_DATE as days_until_expiry,
    -- Urgency level
    CASE 
        WHEN d.expiry_date < CURRENT_DATE THEN 'expired'
        WHEN d.expiry_date < CURRENT_DATE + INTERVAL '30 days' THEN 'urgent'
        WHEN d.expiry_date < CURRENT_DATE + INTERVAL '60 days' THEN 'warning'
        ELSE 'notice'
    END as urgency_level,
    d.uploaded_at,
    d.notes
FROM documents d
JOIN vendors v ON d.vendor_id = v.vendor_id
LEFT JOIN lead_managers lm ON v.lead_manager_id = lm.manager_id
WHERE d.expiry_date IS NOT NULL 
    AND d.expiry_date <= CURRENT_DATE + INTERVAL '90 days'
    AND v.active = true
ORDER BY d.expiry_date ASC;

-- View 5: Upcoming Reviews
-- Shows vendors with reviews due in the next 60 days
CREATE VIEW vw_upcoming_reviews AS
SELECT 
    v.vendor_id,
    v.company_name,
    v.vendor_type,
    lm.manager_name as lead_manager,
    lm.email as lead_manager_email,
    r.review_id,
    r.last_review_date,
    r.next_review_date,
    r.completed,
    -- Days until review
    r.next_review_date - CURRENT_DATE as days_until_review,
    -- Review status
    CASE 
        WHEN r.next_review_date < CURRENT_DATE THEN 'overdue'
        WHEN r.next_review_date < CURRENT_DATE + INTERVAL '14 days' THEN 'urgent'
        WHEN r.next_review_date < CURRENT_DATE + INTERVAL '30 days' THEN 'due_soon'
        ELSE 'scheduled'
    END as review_status,
    -- Latest risk score for context
    latest_risk.overall_risk_score,
    latest_risk.assessment_date as latest_risk_date
FROM reviews r
JOIN vendors v ON r.vendor_id = v.vendor_id
LEFT JOIN lead_managers lm ON v.lead_manager_id = lm.manager_id
LEFT JOIN LATERAL (
    SELECT overall_risk_score, assessment_date
    FROM risk_assessments ra
    WHERE ra.vendor_id = v.vendor_id
    ORDER BY ra.assessment_date DESC
    LIMIT 1
) latest_risk ON true
WHERE r.next_review_date IS NOT NULL 
    AND r.next_review_date <= CURRENT_DATE + INTERVAL '60 days'
    AND r.completed = false
    AND v.active = true
ORDER BY r.next_review_date ASC;

-- Additional utility views

-- View: Vendor Statistics Summary
-- Provides high-level statistics for dashboard
CREATE VIEW vw_vendor_stats AS
SELECT 
    COUNT(*) as total_vendors,
    COUNT(*) FILTER (WHERE active = true) as active_vendors,
    COUNT(*) FILTER (WHERE edd_required = true) as edd_required_vendors,
    COUNT(DISTINCT vendor_type) as vendor_types_count,
    COUNT(DISTINCT unnest(countries_of_operation)) as countries_count
FROM vendors;

-- View: Risk Distribution
-- Shows distribution of vendors by risk category
CREATE VIEW vw_risk_distribution AS
SELECT 
    CASE 
        WHEN ra.overall_risk_score >= 4.0 THEN 'very_high'
        WHEN ra.overall_risk_score >= 3.0 THEN 'high'
        WHEN ra.overall_risk_score >= 2.0 THEN 'medium'
        WHEN ra.overall_risk_score >= 1.0 THEN 'low'
        WHEN ra.overall_risk_score IS NOT NULL THEN 'very_low'
        ELSE 'not_assessed'
    END as risk_category,
    COUNT(*) as vendor_count
FROM vendors v
LEFT JOIN LATERAL (
    SELECT overall_risk_score
    FROM risk_assessments ra2
    WHERE ra2.vendor_id = v.vendor_id
    ORDER BY ra2.assessment_date DESC
    LIMIT 1
) ra ON true
WHERE v.active = true
GROUP BY 
    CASE 
        WHEN ra.overall_risk_score >= 4.0 THEN 'very_high'
        WHEN ra.overall_risk_score >= 3.0 THEN 'high'
        WHEN ra.overall_risk_score >= 2.0 THEN 'medium'
        WHEN ra.overall_risk_score >= 1.0 THEN 'low'
        WHEN ra.overall_risk_score IS NOT NULL THEN 'very_low'
        ELSE 'not_assessed'
    END
ORDER BY 
    CASE 
        WHEN risk_category = 'very_high' THEN 1
        WHEN risk_category = 'high' THEN 2
        WHEN risk_category = 'medium' THEN 3
        WHEN risk_category = 'low' THEN 4
        WHEN risk_category = 'very_low' THEN 5
        ELSE 6
    END; 