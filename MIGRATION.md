# 🚀 Supabase to PostgreSQL Migration Guide

This guide will help you migrate your existing Supabase data to your new local PostgreSQL database.

## 📋 Prerequisites

1. **Supabase credentials**: You need your Supabase URL and Anon Key
2. **Local PostgreSQL running**: Your governance app database should be running
3. **Backup**: Consider backing up your current data

## 🔧 Setup

### Step 1: Configure Supabase Credentials

Create a `.env` file in the `migration-scripts` directory with your Supabase credentials:

```bash
# Create migration environment file
cat > migration-scripts/.env << 'EOF'
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key-here
EOF
```

**OR** you can use the existing environment variables from your project:
- `VITE_SUPABASE_URL`
- `VITE_SUPABASE_ANON_KEY`

### Step 2: Verify Database Connection

Make sure your PostgreSQL database is running:
```bash
npm run db:start  # If using Docker
# OR check that your backend is connected to the database
curl http://localhost:3030/api/health
```

## 🔄 Migration Process

### Step 1: Export from Supabase

```bash
npm run migrate:export
```

This will:
- Connect to your Supabase instance
- Export data from all tables
- Create a file named `supabase-export-YYYY-MM-DD.json`

### Step 2: Import to PostgreSQL

```bash
npm run migrate:import  
```

This will:
- Find the latest export file
- Clear existing data in PostgreSQL
- Import all your Supabase data
- Preserve relationships and data integrity

## 📊 What Gets Migrated

The migration includes all tables:
- **departments**: Company departments
- **members**: Employee/member records  
- **committees**: Committee information
- **committee_memberships**: Committee membership relations
- **meetings**: Meeting records
- **policies**: Policy documents
- **policy_versions**: Policy version history
- **trainings**: Training programs
- **training_sessions**: Training session records
- **attendance**: Training attendance records
- **vendor**: Vendor/supplier information

## ⚠️ Important Notes

- **Data Replacement**: The import process will **replace all existing data** in PostgreSQL
- **Backup First**: Make sure to backup any important data before running import
- **ID Preservation**: Original UUIDs from Supabase will be preserved
- **JSON Fields**: Complex fields (like arrays, objects) are properly handled

## 🔍 Verification

After migration, verify your data:

```bash
# Check record counts
curl http://localhost:3030/api/policies | jq 'length'
curl http://localhost:3030/api/members | jq 'length'  
curl http://localhost:3030/api/committees | jq 'length'

# Browse the app
open http://localhost:3001
```

## 🆘 Troubleshooting

### Export Issues
- **"Missing Supabase credentials"**: Check your `.env` file or environment variables
- **"Connection failed"**: Verify your Supabase URL and key are correct
- **"No data exported"**: Check that your Supabase tables have data

### Import Issues  
- **"No export file found"**: Run the export step first
- **"Connection failed"**: Make sure PostgreSQL is running and accessible
- **"Import errors"**: Check data format compatibility between Supabase and PostgreSQL

## 🎯 Quick Migration

For a complete migration in one go:

```bash
# Set up credentials first (if needed)
echo "SUPABASE_URL=https://your-project.supabase.co" > migration-scripts/.env
echo "SUPABASE_ANON_KEY=your-anon-key" >> migration-scripts/.env

# Run migration
npm run migrate:export && npm run migrate:import
```

## ✅ Success!

After successful migration, your governance app will have all your original Supabase data in the new PostgreSQL database, and you can continue using the app as before! 