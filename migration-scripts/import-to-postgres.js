import fs from 'fs';
import pkg from 'pg';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const { Pool } = pkg;
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

dotenv.config({ path: join(__dirname, '../backend/.env') });

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://governance_user:governance_password@localhost:5433/governance_app',
});

async function findExportFile() {
  const files = fs.readdirSync('.')
    .filter(file => file.startsWith('supabase-export-') && file.endsWith('.json'))
    .sort()
    .reverse(); // Latest first
  
  if (files.length === 0) {
    throw new Error('No export file found. Please run "npm run migrate:export" first.');
  }
  
  return files[0];
}

async function clearExistingData() {
  console.log('🧹 Clearing existing data...');
  
  const tables = [
    'attendance',
    'training_sessions',
    'policy_versions',
    'committee_memberships',
    'meetings',
    'trainings',
    'vendor',
    'policies',
    'committees',
    'members',
    'departments'
  ];
  
  for (const table of tables) {
    try {
      await pool.query(`DELETE FROM ${table}`);
      console.log(`✅ Cleared ${table}`);
    } catch (err) {
      console.warn(`⚠️ Warning clearing ${table}:`, err.message);
    }
  }
}

async function importTable(tableName, data) {
  if (!data || data.length === 0) {
    console.log(`⭕ Skipping ${tableName} (no data)`);
    return;
  }
  
  console.log(`📥 Importing ${tableName} (${data.length} records)...`);
  
  // Column mapping for different schemas
  const columnMappings = {
    policies: {
      'name': 'policy_name',  // Map Supabase 'name' to PostgreSQL 'policy_name'
    }
  };
  
  try {
    // Transform data for column mapping
    const transformedData = data.map(record => {
      const transformed = { ...record };
      if (columnMappings[tableName]) {
        for (const [supabaseCol, pgCol] of Object.entries(columnMappings[tableName])) {
          if (transformed[supabaseCol] !== undefined) {
            transformed[pgCol] = transformed[supabaseCol];
            delete transformed[supabaseCol];
          }
        }
      }
      return transformed;
    });
    
    // Get column names from first transformed record
    const columns = Object.keys(transformedData[0]);
    const placeholders = columns.map((_, i) => `$${i + 1}`).join(', ');
    const columnList = columns.join(', ');
    
    const query = `
      INSERT INTO ${tableName} (${columnList})
      VALUES (${placeholders})
      ON CONFLICT (id) DO UPDATE SET
        ${columns.filter(col => col !== 'id').map(col => `${col} = EXCLUDED.${col}`).join(', ')}
    `;
    
    let successCount = 0;
    let errorCount = 0;
    
    for (const record of transformedData) {
      try {
        const values = columns.map(col => {
          const value = record[col];
          // Handle JSON fields
          if (typeof value === 'object' && value !== null) {
            return JSON.stringify(value);
          }
          return value;
        });
        
        await pool.query(query, values);
        successCount++;
      } catch (err) {
        errorCount++;
        console.warn(`⚠️ Error importing record to ${tableName}:`, err.message);
        // Continue with next record
      }
    }
    
    console.log(`✅ ${tableName}: ${successCount} imported, ${errorCount} errors`);
  } catch (err) {
    console.error(`❌ Error importing ${tableName}:`, err.message);
  }
}

async function importData() {
  let filename;
  
  try {
    filename = await findExportFile();
    console.log(`📂 Using export file: ${filename}\n`);
  } catch (err) {
    console.error('❌', err.message);
    process.exit(1);
  }
  
  const exportData = JSON.parse(fs.readFileSync(filename, 'utf8'));
  
  console.log(`🚀 Starting import from ${filename}`);
  console.log(`📅 Export date: ${exportData.exportedAt}\n`);
  
  // Import in dependency order
  const importOrder = [
    'departments',
    'members',
    'committees', 
    'committee_memberships',
    'policies',
    'policy_versions',
    'trainings',
    'training_sessions',
    'meetings',
    'attendance',
    'vendor'
  ];
  
  for (const table of importOrder) {
    if (exportData.tables[table]) {
      await importTable(table, exportData.tables[table]);
    }
  }
  
  console.log('\n📊 Import Summary:');
  for (const [table, data] of Object.entries(exportData.tables)) {
    console.log(`  ${table}: ${data.length} records available`);
  }
}

async function testConnection() {
  console.log('🔍 Testing PostgreSQL connection...');
  try {
    const result = await pool.query('SELECT NOW()');
    console.log('✅ Connection successful');
    return true;
  } catch (err) {
    console.error('❌ Connection failed:', err.message);
    return false;
  }
}

async function main() {
  if (!(await testConnection())) {
    console.log('💡 Please check your database connection and try again.');
    process.exit(1);
  }
  
  console.log('⚠️  This will replace all existing data in your PostgreSQL database.');
  console.log('⚠️  Make sure to backup any important data first.\n');
  
  // In a real scenario, you might want to add a confirmation prompt here
  // For now, we'll proceed automatically
  
  await clearExistingData();
  console.log('');
  await importData();
  
  await pool.end();
  console.log('\n🎉 Migration complete!');
}

main().catch(console.error); 