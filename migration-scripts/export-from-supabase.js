import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

dotenv.config({ path: join(__dirname, '.env') });

const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_ANON_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials. Please set SUPABASE_URL and SUPABASE_ANON_KEY environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

const tables = [
  'departments',
  'members', 
  'committees',
  'committee_memberships',
  'meetings',
  'policies',
  'policy_versions',
  'trainings',
  'training_sessions',
  'attendance',
  'vendor'
];

async function exportData() {
  console.log('🚀 Starting Supabase data export...\n');
  const exportData = {
    exportedAt: new Date().toISOString(),
    tables: {}
  };
  
  for (const table of tables) {
    try {
      console.log(`📥 Exporting ${table}...`);
      const { data, error } = await supabase.from(table).select('*');
      
      if (error) {
        console.error(`❌ Error exporting ${table}:`, error.message);
        continue;
      }
      
      exportData.tables[table] = data || [];
      console.log(`✅ Exported ${data?.length || 0} records from ${table}`);
    } catch (err) {
      console.error(`❌ Error exporting ${table}:`, err.message);
    }
  }
  
  // Write to file
  const filename = `supabase-export-${new Date().toISOString().split('T')[0]}.json`;
  fs.writeFileSync(filename, JSON.stringify(exportData, null, 2));
  console.log(`\n💾 Export complete: ${filename}`);
  
  // Summary
  console.log('\n📊 Export Summary:');
  for (const [table, data] of Object.entries(exportData.tables)) {
    console.log(`  ${table}: ${data.length} records`);
  }
}

async function testConnection() {
  console.log('🔍 Testing Supabase connection...');
  try {
    const { data, error } = await supabase.from('policies').select('count').limit(1);
    if (error) throw error;
    console.log('✅ Connection successful');
    return true;
  } catch (err) {
    console.error('❌ Connection failed:', err.message);
    return false;
  }
}

// Main execution
async function main() {
  if (await testConnection()) {
    await exportData();
  } else {
    console.log('💡 Please check your Supabase credentials and try again.');
    process.exit(1);
  }
}

main().catch(console.error); 