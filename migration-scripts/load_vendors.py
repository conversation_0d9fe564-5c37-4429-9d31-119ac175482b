#!/usr/bin/env python3
"""
Vendor Data Import Script

This script imports vendor data from an Excel file into the PostgreSQL database.
Handles complete vendor profiles including risk assessments, contracts, and shareholder/director information.

Usage:
    python load_vendors.py path/to/vendors.xlsx [--dry-run]
    
Requirements:
    - pip install pandas openpyxl psycopg2-binary python-dotenv
    - Set up DATABASE_URL environment variable or create .env file
"""

import pandas as pd
import psycopg2
import psycopg2.extras
import logging
import argparse
import os
import sys
from datetime import datetime, date
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('vendor_import.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class VendorImporter:
    """Handles importing vendor data from Excel to PostgreSQL"""
    
    def __init__(self, db_config: Dict[str, str], excel_file_path: str):
        """Initialize importer with database config and Excel file path"""
        self.db_config = db_config
        self.excel_file_path = Path(excel_file_path)
        self.conn = None
        self.cursor = None
        
        # Validate file exists
        if not self.excel_file_path.exists():
            raise FileNotFoundError(f"Excel file not found: {excel_file_path}")
        
        # Type mappings (could be extended from config)
        self.vendor_type_mapping = {
            'technology provider': 'technology',
            'tech provider': 'technology',
            'software provider': 'technology',
            'consultant': 'consulting',
            'consulting': 'consulting',
            'service provider': 'services',
            'services': 'services',
            'outsourcing': 'outsourcing',
            'supplier': 'supplier',
            'vendor': 'supplier',
            'financial': 'financial',
            'finance': 'financial',
            'legal': 'legal',
            'compliance': 'compliance',
            'audit': 'audit',
            'other': 'other'
        }
        
        self.service_type_mapping = {
            'software': 'software',
            'hardware': 'hardware',
            'cloud': 'cloud_services',
            'consulting': 'consulting',
            'support': 'support',
            'maintenance': 'maintenance',
            'development': 'development',
            'legal': 'legal_services',
            'financial': 'financial_services',
            'audit': 'audit_services',
            'compliance': 'compliance_services',
            'data': 'data_services',
            'security': 'security_services',
            'other': 'other'
        }

    def connect_db(self):
        """Establish database connection"""
        try:
            self.conn = psycopg2.connect(**self.db_config)
            self.cursor = self.conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            logger.info("Connected to database successfully")
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            raise

    def disconnect_db(self):
        """Close database connection"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
        logger.info("Database connection closed")

    def read_excel_data(self) -> pd.DataFrame:
        """Read vendor data from Excel file"""
        try:
            # Read from the "List Vendors" sheet with headers in row 1 (0-indexed)
            df = pd.read_excel(
                self.excel_file_path, 
                sheet_name='List Vendors', 
                header=1  # Row 1 contains the headers
            )
            
            # Clean up column names - strip whitespace
            df.columns = [str(col).strip() for col in df.columns]
            
            # Remove completely empty rows
            df = df.dropna(how='all')
            
            logger.info(f"Read {len(df)} vendor records from Excel")
            logger.info(f"Columns found: {list(df.columns)}")
            
            return df
            
        except Exception as e:
            logger.error(f"Failed to read Excel file: {e}")
            raise

    def normalize_value(self, value: Any, data_type: str = 'str') -> Any:
        """Normalize and clean individual values"""
        if pd.isna(value) or value == '' or str(value).lower() == 'nan':
            return None
            
        if data_type == 'bool':
            if isinstance(value, bool):
                return value
            if isinstance(value, str):
                return value.lower() in ['true', 'yes', '1', 'active', 'y', 'on']
            return bool(value)
            
        elif data_type == 'date':
            if isinstance(value, datetime):
                return value.date()
            elif isinstance(value, date):
                return value
            elif isinstance(value, str):
                # Try different date formats
                for fmt in ['%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y', '%Y-%m-%d %H:%M:%S']:
                    try:
                        return datetime.strptime(str(value), fmt).date()
                    except:
                        continue
                return None
            return None
            
        elif data_type == 'float':
            try:
                return float(value)
            except:
                return None
                
        elif data_type == 'array':
            if isinstance(value, str):
                # Split on common delimiters and clean
                import re
                items = re.split(r'[,;|]+', value)
                return [item.strip() for item in items if item.strip()]
            return None
            
        else:  # string
            return str(value).strip() if value else None

    def get_or_create_lead_manager(self, manager_name: str) -> Optional[int]:
        """Get or create lead manager and return ID"""
        if not manager_name:
            return None
            
        # Check if manager exists
        self.cursor.execute(
            "SELECT manager_id FROM lead_managers WHERE manager_name = %s",
            (manager_name,)
        )
        result = self.cursor.fetchone()
        
        if result:
            return result['manager_id']
        
        # Create new manager
        email = f"{manager_name.lower().replace(' ', '.')}@company.com"
        self.cursor.execute(
            """INSERT INTO lead_managers (manager_name, email, department) 
               VALUES (%s, %s, %s) RETURNING manager_id""",
            (manager_name, email, "Unknown")
        )
        return self.cursor.fetchone()['manager_id']

    def insert_vendor(self, row: pd.Series) -> int:
        """Insert vendor record and return vendor_id"""
        
        # Map vendor type
        vendor_type_str = self.normalize_value(row.get('Type of Service Provider', ''))
        vendor_type = 'other'
        if vendor_type_str:
            vendor_type = self.vendor_type_mapping.get(vendor_type_str.lower(), 'other')
        
        # Map service type
        service_type_str = self.normalize_value(row.get('Type of Service', ''))
        service_type = 'other'
        if service_type_str:
            service_type = self.service_type_mapping.get(service_type_str.lower(), 'other')
        
        vendor_data = {
            'company_name': self.normalize_value(row.get('Company Name')),
            'legal_name': self.normalize_value(row.get('Legal Name')),
            'company_address': self.normalize_value(row.get('Company Address')),
            'business_type': self.normalize_value(row.get('Business Type')),
            'website': self.normalize_value(row.get('Website')),
            'vendor_type': vendor_type,
            'service_type': service_type,
            'countries_of_operation': self.normalize_value(row.get('Cpuntries of Operation'), 'array'),
            'incorporation_date': self.normalize_value(row.get('Date of Incorporation'), 'date'),
            'registration_number': self.normalize_value(row.get('Registration Number')),
            'vendor_questionnaire': self.normalize_value(row.get('Vendor Questionaire')),
            'certifications': self.normalize_value(row.get('Certifications')),
            'edd_required': self.normalize_value(row.get('EDD Required'), 'bool'),
        }
        
        # Get lead manager ID
        lead_manager_name = self.normalize_value(row.get('Lead Manager'))
        vendor_data['lead_manager_id'] = self.get_or_create_lead_manager(lead_manager_name)
        
        # Insert vendor
        insert_query = """
            INSERT INTO vendors (
                company_name, legal_name, company_address, business_type, website, 
                vendor_type, service_type, lead_manager_id, countries_of_operation, 
                incorporation_date, registration_number, vendor_questionnaire, 
                certifications, edd_required
            ) VALUES (
                %(company_name)s, %(legal_name)s, %(company_address)s, %(business_type)s, 
                %(website)s, %(vendor_type)s, %(service_type)s, %(lead_manager_id)s, 
                %(countries_of_operation)s, %(incorporation_date)s, %(registration_number)s, 
                %(vendor_questionnaire)s, %(certifications)s, %(edd_required)s
            ) RETURNING vendor_id
        """
        
        self.cursor.execute(insert_query, vendor_data)
        vendor_id = self.cursor.fetchone()['vendor_id']
        logger.info(f"Inserted vendor: {vendor_data['company_name']} (ID: {vendor_id})")
        return vendor_id

    def insert_contract(self, vendor_id: int, row: pd.Series):
        """Insert contract record for vendor"""
        contract_data = {
            'vendor_id': vendor_id,
            'active': self.normalize_value(row.get('Contract Active'), 'bool'),
            'start_date': self.normalize_value(row.get('Contract Start Date'), 'date'),
            'renewal_date': self.normalize_value(row.get('Contract Renewal Date'), 'date'),
            'end_date': self.normalize_value(row.get('End Date'), 'date'),
        }
        
        # Only insert if we have some contract data
        if any(v is not None for k, v in contract_data.items() if k != 'vendor_id'):
            insert_query = """
                INSERT INTO contracts (vendor_id, active, start_date, renewal_date, end_date)
                VALUES (%(vendor_id)s, %(active)s, %(start_date)s, %(renewal_date)s, %(end_date)s)
            """
            self.cursor.execute(insert_query, contract_data)

    def insert_risk_assessment(self, vendor_id: int, row: pd.Series):
        """Insert risk assessment record for vendor"""
        risk_data = {
            'vendor_id': vendor_id,
            'assessment_date': self.normalize_value(row.get('Risk Assessment Date'), 'date'),
            'operational_risk': self.normalize_value(row.get('Operational Risk'), 'float'),
            'confidential_data_handling': self.normalize_value(row.get('Confidential Data Handling'), 'float'),
            'regulatory_risk': self.normalize_value(row.get('Regulatory Risk'), 'float'),
            'policies': self.normalize_value(row.get('Policies'), 'float'),
            'service_level_agreement': self.normalize_value(row.get('Service Level Agreement'), 'float'),
            'business_continuity_plan': self.normalize_value(row.get('Business Continuity Plan'), 'float'),
            'external_audits': self.normalize_value(row.get('External Audits'), 'float'),
            'customer_verification': self.normalize_value(row.get('Customer Verification'), 'float'),
            'concentration_risk': self.normalize_value(row.get('Concentration Risk'), 'float'),
            'outsourcing_risk': self.normalize_value(row.get('Outsourcing Risk'), 'float'),
            'overall_risk': self.normalize_value(row.get('Overall Risk'), 'float'),
        }
        
        # Only insert if we have some risk data
        if any(v is not None for k, v in risk_data.items() if k not in ['vendor_id', 'assessment_date']):
            insert_query = """
                INSERT INTO risk_assessments (
                    vendor_id, assessment_date, operational_risk, confidential_data_handling,
                    regulatory_risk, policies, service_level_agreement, business_continuity_plan,
                    external_audits, customer_verification, concentration_risk, outsourcing_risk,
                    overall_risk
                ) VALUES (
                    %(vendor_id)s, %(assessment_date)s, %(operational_risk)s, %(confidential_data_handling)s,
                    %(regulatory_risk)s, %(policies)s, %(service_level_agreement)s, %(business_continuity_plan)s,
                    %(external_audits)s, %(customer_verification)s, %(concentration_risk)s, %(outsourcing_risk)s,
                    %(overall_risk)s
                )
            """
            self.cursor.execute(insert_query, risk_data)

    def insert_review(self, vendor_id: int, row: pd.Series):
        """Insert review record for vendor"""
        review_data = {
            'vendor_id': vendor_id,
            'last_review_date': self.normalize_value(row.get('Last Vendor Review'), 'date'),
            'next_review_date': self.normalize_value(row.get('Next Vendor Review'), 'date'),
        }
        
        # Only insert if we have review data
        if any(v is not None for k, v in review_data.items() if k != 'vendor_id'):
            insert_query = """
                INSERT INTO reviews (vendor_id, last_review_date, next_review_date)
                VALUES (%(vendor_id)s, %(last_review_date)s, %(next_review_date)s)
            """
            self.cursor.execute(insert_query, review_data)

    def insert_documents(self, vendor_id: int, row: pd.Series):
        """Insert document records for vendor"""
        docs = [
            ('incorporation_doc', row.get('Incorporation Documents')),
            ('share_register', row.get('Share Register')),
        ]
        
        for doc_type, doc_value in docs:
            if doc_value and not pd.isna(doc_value):
                document_data = {
                    'vendor_id': vendor_id,
                    'doc_type': doc_type,
                    'document_name': str(doc_value),
                    'file_path': str(doc_value) if not str(doc_value).startswith('http') else None,
                    'url': str(doc_value) if str(doc_value).startswith('http') else None,
                }
                
                insert_query = """
                    INSERT INTO documents (vendor_id, doc_type, document_name, file_path, url)
                    VALUES (%(vendor_id)s, %(doc_type)s, %(document_name)s, %(file_path)s, %(url)s)
                """
                self.cursor.execute(insert_query, document_data)

    def insert_shareholders_and_directors(self, vendor_id: int, row: pd.Series):
        """Insert shareholder and director records for vendor"""
        
        # Process shareholders
        shareholder_names = self.normalize_value(row.get('Shareholder Names'), 'array')
        shareholder_addresses = self.normalize_value(row.get('Shareholder Address'), 'array')
        shareholder_nationalities = self.normalize_value(row.get('Shareholder Nationality'), 'array')
        shareholder_ids = self.normalize_value(row.get('Shareholder IDs'), 'array')
        shareholder_proofs = self.normalize_value(row.get('Shareholder Proof of Address'), 'array')
        
        if shareholder_names:
            max_shareholders = len(shareholder_names)
            for i in range(max_shareholders):
                shareholder_data = {
                    'vendor_id': vendor_id,
                    'name': shareholder_names[i] if i < len(shareholder_names) else None,
                    'address': shareholder_addresses[i] if shareholder_addresses and i < len(shareholder_addresses) else None,
                    'nationality': shareholder_nationalities[i] if shareholder_nationalities and i < len(shareholder_nationalities) else None,
                    'id_reference': shareholder_ids[i] if shareholder_ids and i < len(shareholder_ids) else None,
                    'proof_of_address_path': shareholder_proofs[i] if shareholder_proofs and i < len(shareholder_proofs) else None,
                }
                
                if shareholder_data['name']:
                    insert_query = """
                        INSERT INTO shareholders (vendor_id, name, address, nationality, id_reference, proof_of_address_path)
                        VALUES (%(vendor_id)s, %(name)s, %(address)s, %(nationality)s, %(id_reference)s, %(proof_of_address_path)s)
                    """
                    self.cursor.execute(insert_query, shareholder_data)
        
        # Process directors
        director_names = self.normalize_value(row.get('Director Names'), 'array')
        director_addresses = self.normalize_value(row.get('Director Address'), 'array')
        director_nationalities = self.normalize_value(row.get('Director Nationality'), 'array')
        director_ids = self.normalize_value(row.get('Director IDs'), 'array')
        director_proofs = self.normalize_value(row.get('Director Proof of Address'), 'array')
        
        if director_names:
            max_directors = len(director_names)
            for i in range(max_directors):
                director_data = {
                    'vendor_id': vendor_id,
                    'name': director_names[i] if i < len(director_names) else None,
                    'address': director_addresses[i] if director_addresses and i < len(director_addresses) else None,
                    'nationality': director_nationalities[i] if director_nationalities and i < len(director_nationalities) else None,
                    'id_reference': director_ids[i] if director_ids and i < len(director_ids) else None,
                    'proof_of_address_path': director_proofs[i] if director_proofs and i < len(director_proofs) else None,
                }
                
                if director_data['name']:
                    insert_query = """
                        INSERT INTO directors (vendor_id, name, address, nationality, id_reference, proof_of_address_path)
                        VALUES (%(vendor_id)s, %(name)s, %(address)s, %(nationality)s, %(id_reference)s, %(proof_of_address_path)s)
                    """
                    self.cursor.execute(insert_query, director_data)

    def process_vendor_row(self, row: pd.Series):
        """Process a single vendor row and insert all related data"""
        try:
            # Skip empty rows
            if pd.isna(row.get('Company Name')):
                return
                
            # Insert vendor and get ID
            vendor_id = self.insert_vendor(row)
            
            # Insert related data
            self.insert_contract(vendor_id, row)
            self.insert_risk_assessment(vendor_id, row)
            self.insert_review(vendor_id, row)
            self.insert_documents(vendor_id, row)
            self.insert_shareholders_and_directors(vendor_id, row)
            
        except Exception as e:
            logger.error(f"Error processing vendor row: {e}")
            logger.error(f"Row data: {dict(row)}")
            raise

    def run_import(self, dry_run: bool = False):
        """Main import process"""
        try:
            # Connect to database
            self.connect_db()
            
            # Read Excel data
            vendors_df = self.read_excel_data()
            
            # Process each vendor
            processed_count = 0
            error_count = 0
            
            for index, row in vendors_df.iterrows():
                try:
                    if dry_run:
                        logger.info(f"DRY RUN: Would process vendor: {row.get('Company Name', 'Unknown')}")
                    else:
                        self.process_vendor_row(row)
                    processed_count += 1
                    
                except Exception as e:
                    error_count += 1
                    logger.error(f"Failed to process row {index}: {e}")
                    continue
            
            # Commit or rollback
            if not dry_run:
                if error_count == 0:
                    self.conn.commit()
                    logger.info(f"Successfully imported {processed_count} vendors")
                else:
                    self.conn.rollback()
                    logger.error(f"Import failed. {error_count} errors encountered. All changes rolled back.")
            else:
                logger.info(f"DRY RUN completed. Would process {processed_count} vendors")
                
        except Exception as e:
            if self.conn:
                self.conn.rollback()
            logger.error(f"Import failed: {e}")
            raise
        finally:
            self.disconnect_db()


def get_db_config() -> Dict[str, str]:
    """Get database configuration from environment variables"""
    db_url = os.getenv('DATABASE_URL')
    
    if db_url:
        # Parse DATABASE_URL format: postgresql://user:pass@host:port/dbname
        import re
        match = re.match(r'postgresql://([^:]+):([^@]+)@([^:]+):(\d+)/(.+)', db_url)
        if match:
            user, password, host, port, dbname = match.groups()
            return {
                'host': host,
                'port': int(port),
                'database': dbname,
                'user': user,
                'password': password
            }
    
    # Fallback to individual environment variables
    return {
        'host': os.getenv('DB_HOST', 'localhost'),
        'port': int(os.getenv('DB_PORT', 5432)),
        'database': os.getenv('DB_NAME', 'governance'),
        'user': os.getenv('DB_USER', 'postgres'),
        'password': os.getenv('DB_PASSWORD', '')
    }


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Import vendor data from Excel to PostgreSQL')
    parser.add_argument('excel_file', help='Path to Excel file containing vendor data')
    parser.add_argument('--dry-run', action='store_true', help='Perform a dry run without making changes')
    
    args = parser.parse_args()
    
    try:
        # Get database configuration
        db_config = get_db_config()
        
        # Create importer and run
        importer = VendorImporter(db_config, args.excel_file)
        importer.run_import(dry_run=args.dry_run)
        
        logger.info("Import process completed successfully")
        
    except Exception as e:
        logger.error(f"Import failed: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main() 