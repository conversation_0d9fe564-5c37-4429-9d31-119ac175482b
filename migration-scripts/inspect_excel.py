#!/usr/bin/env python3
"""
Excel File Inspector - Helps identify the correct header row and structure
"""

import pandas as pd
import sys

def inspect_excel_file(excel_path):
    """Inspect Excel file to understand its structure"""
    
    print(f"Inspecting Excel file: {excel_path}")
    print("=" * 80)
    
    # Get sheet names
    xl_file = pd.ExcelFile(excel_path)
    print(f"Sheet names: {xl_file.sheet_names}")
    print()
    
    # Examine each sheet
    for sheet_name in xl_file.sheet_names:
        print(f"Sheet: {sheet_name}")
        print("-" * 40)
        
        # Read first 10 rows without header to see structure
        df_raw = pd.read_excel(excel_path, sheet_name=sheet_name, header=None, nrows=10)
        
        print("First 10 rows:")
        for idx, row in df_raw.iterrows():
            row_data = [str(x)[:30] + "..." if len(str(x)) > 30 else str(x) for x in row.values]
            print(f"Row {idx}: {row_data[:8]}")  # Show first 8 columns
        
        print()
        
        # Try to find header row by looking for text that looks like column names
        for header_row in range(10):
            try:
                df_test = pd.read_excel(excel_path, sheet_name=sheet_name, header=header_row, nrows=5)
                columns = [str(col) for col in df_test.columns]
                
                # Check if this looks like a header row (contains known vendor column names)
                vendor_keywords = ['company', 'name', 'vendor', 'type', 'service', 'contract', 'risk', 'address']
                if any(keyword in str(col).lower() for col in columns for keyword in vendor_keywords):
                    print(f"Potential header row {header_row}: {columns[:8]}")
                
            except Exception as e:
                continue
        
        print("\n" + "=" * 80 + "\n")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python3 inspect_excel.py path/to/excel/file.xlsx")
        sys.exit(1)
    
    excel_path = sys.argv[1]
    inspect_excel_file(excel_path) 