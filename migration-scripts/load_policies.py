#!/usr/bin/env python3
"""
Policy Data ETL Script
Imports policy data from Excel spreadsheet into PostgreSQL database.

Usage:
    python load_policies.py --excel-file path/to/policies.xlsx --sheet-name "Policies"

Expected Excel columns:
- policy_name (required)
- current_version 
- link (document URL)
- last_review (date)
- next_review (date)
- author (required)
- description (optional)
"""

import os
import sys
import pandas as pd
import psycopg2
from psycopg2.extras import RealDictCursor
import argparse
import logging
from datetime import datetime
import uuid

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('policy_import.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def clean_column_names(df):
    """Clean and standardize column names"""
    df.columns = df.columns.str.strip().str.lower().str.replace(' ', '_').str.replace('-', '_')
    
    # Handle common variations
    column_mapping = {
        'policy': 'policy_name',
        'name': 'policy_name',
        'version': 'current_version',
        'document_link': 'link',
        'document_url': 'link',
        'url': 'link',
        'last_reviewed': 'last_review',
        'previous_review': 'last_review',
        'next_reviewed': 'next_review',
        'due_date': 'next_review',
        'review_date': 'next_review',
        'owner': 'author',
        'responsible': 'author',
        'desc': 'description',
    }
    
    df.rename(columns=column_mapping, inplace=True)
    return df

def parse_date(date_value):
    """Parse various date formats"""
    if pd.isna(date_value) or date_value == '':
        return None
    
    if isinstance(date_value, datetime):
        return date_value.date()
    
    # Try different date formats
    date_formats = [
        '%d/%m/%Y', '%m/%d/%Y', '%Y-%m-%d', 
        '%d-%m-%Y', '%Y/%m/%d', '%d.%m.%Y'
    ]
    
    date_str = str(date_value).strip()
    for fmt in date_formats:
        try:
            return datetime.strptime(date_str, fmt).date()
        except ValueError:
            continue
    
    logger.warning(f"Could not parse date: {date_value}")
    return None

def validate_policy_data(row):
    """Validate required fields"""
    errors = []
    
    if not row.get('policy_name') or pd.isna(row['policy_name']):
        errors.append("Policy name is required")
    
    if not row.get('author') or pd.isna(row['author']):
        errors.append("Author is required")
    
    return errors

def import_policies(excel_file, sheet_name="Policies", start_row=0):
    """Import policies from Excel file"""
    
    # Database connection
    try:
        conn = psycopg2.connect(
            host=os.getenv('DB_HOST', 'localhost'),
            port=os.getenv('DB_PORT', '5432'),
            database=os.getenv('DB_NAME', 'governance_app'),
            user=os.getenv('DB_USER', 'postgres'),
            password=os.getenv('DB_PASSWORD', '')
        )
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        logger.info("Connected to database successfully")
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        sys.exit(1)
    
    try:
        # Read Excel file
        logger.info(f"Reading Excel file: {excel_file}, Sheet: {sheet_name}")
        df = pd.read_excel(excel_file, sheet_name=sheet_name, skiprows=start_row)
        
        # Clean column names
        df = clean_column_names(df)
        logger.info(f"Columns found: {list(df.columns)}")
        
        # Remove empty rows
        df = df.dropna(how='all')
        logger.info(f"Processing {len(df)} rows")
        
        # Process each row
        successful_imports = 0
        failed_imports = 0
        
        for index, row in df.iterrows():
            try:
                # Validate data
                validation_errors = validate_policy_data(row)
                if validation_errors:
                    logger.error(f"Row {index + 1}: Validation errors: {', '.join(validation_errors)}")
                    failed_imports += 1
                    continue
                
                # Prepare policy data
                policy_data = {
                    'id': str(uuid.uuid4()),
                    'policy_name': str(row['policy_name']).strip(),
                    'current_version': str(row.get('current_version', '1.0')).strip() if pd.notna(row.get('current_version')) else '1.0',
                    'link': str(row['link']).strip() if pd.notna(row.get('link')) else None,
                    'last_review': parse_date(row.get('last_review')),
                    'next_review': parse_date(row.get('next_review')),
                    'author': str(row['author']).strip(),
                    'description': str(row['description']).strip() if pd.notna(row.get('description')) else None
                }
                
                # Insert policy
                insert_sql = """
                    INSERT INTO policies (id, policy_name, current_version, link, last_review, next_review, author)
                    VALUES (%(id)s, %(policy_name)s, %(current_version)s, %(link)s, %(last_review)s, %(next_review)s, %(author)s)
                    ON CONFLICT (policy_name) DO UPDATE SET
                        current_version = EXCLUDED.current_version,
                        link = EXCLUDED.link,
                        last_review = EXCLUDED.last_review,
                        next_review = EXCLUDED.next_review,
                        author = EXCLUDED.author,
                        updated_at = NOW()
                """
                
                cursor.execute(insert_sql, policy_data)
                successful_imports += 1
                logger.info(f"Row {index + 1}: Imported policy '{policy_data['policy_name']}'")
                
            except Exception as e:
                logger.error(f"Row {index + 1}: Error importing policy: {e}")
                failed_imports += 1
                continue
        
        # Commit transaction
        conn.commit()
        logger.info(f"Import completed: {successful_imports} successful, {failed_imports} failed")
        
    except Exception as e:
        logger.error(f"Import failed: {e}")
        conn.rollback()
        sys.exit(1)
    
    finally:
        cursor.close()
        conn.close()

def main():
    parser = argparse.ArgumentParser(description='Import policies from Excel to PostgreSQL')
    parser.add_argument('--excel-file', required=True, help='Path to Excel file')
    parser.add_argument('--sheet-name', default='Policies', help='Sheet name (default: Policies)')
    parser.add_argument('--start-row', type=int, default=0, help='Row to start reading from (default: 0)')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.excel_file):
        logger.error(f"Excel file not found: {args.excel_file}")
        sys.exit(1)
    
    import_policies(args.excel_file, args.sheet_name, args.start_row)

if __name__ == "__main__":
    main() 