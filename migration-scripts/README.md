# Vendor Management Database Migration

This directory contains the database schema and ETL tools for migrating vendor data from Excel to a normalized PostgreSQL database.

## Files

- `001_init.sql` - Creates normalized database schema with tables, enums, indexes, and constraints
- `002_views.sql` - Creates read-only views for the application UI
- `load_vendors.py` - Python ETL script to import Excel data into the database
- `requirements.txt` - Python dependencies for the ETL script

## Prerequisites

1. PostgreSQL database server running
2. Python 3.8+ installed
3. Excel vendor register file available

## Setup Instructions

### 1. Install Python Dependencies

```bash
cd migration-scripts
pip install -r requirements.txt
```

### 2. Run Database Migrations

Execute the SQL migration files in order:

```bash
# Connect to your PostgreSQL database and run:
psql -h localhost -U postgres -d governance_app -f ../database/001_init.sql
psql -h localhost -U postgres -d governance_app -f ../database/002_views.sql
```

### 3. Configure Environment

Set environment variables for database connection:

```bash
export DB_HOST=localhost
export DB_PORT=5432
export DB_NAME=governance_app
export DB_USER=postgres
export DB_PASSWORD=your_password
export EXCEL_FILE=path/to/vendor_register.xlsx
```

### 4. Run ETL Script

```bash
python load_vendors.py
```

## Excel File Format

The ETL script expects an Excel file with:

- **Sheet 1: "List Vendors"** - Main vendor data (header row = row 2, first 28 data rows)
- **Sheet 2: "Data Types"** - Lookup lists (optional)

### Expected Columns

The script handles these column patterns (case-insensitive):

**Core Vendor Info:**
- company_name
- website  
- type_of_service_provider
- type_of_service
- lead_manager
- countries_of_operation (or cpuntries_of_operation)
- date_of_incorporation
- registration_number
- edd_required

**Contract Details:**
- contract_active
- contract_start_date
- contract_renewal_date
- end_date

**Risk Assessment:**
- risk_assessment_date
- operational_risk
- confidential_data_handling
- physical_security_risk
- outsourcing_risk
- geopolitical_risk
- information_security_risk
- subcontractor_risk
- legal_risk

**Reviews:**
- last_vendor_review
- next_vendor_review

**Documents:**
- incorporation_documents
- share_register

**Ownership (patterns like Shareholder_1_Name, Director_2_Address):**
- Shareholder_X_Name
- Shareholder_X_Address
- Shareholder_X_Nationality
- Shareholder_X_ID
- Shareholder_X_Proof_of_Address
- Director_X_Name
- Director_X_Address
- Director_X_Nationality
- Director_X_ID
- Director_X_Proof_of_Address

## Database Schema

### Tables Created

1. **vendors** - Main vendor information
2. **contracts** - Contract details per vendor
3. **risk_assessments** - Risk scoring history
4. **reviews** - Review scheduling and tracking
5. **documents** - Compliance document tracking
6. **shareholders** - Ownership information
7. **directors** - Director information
8. **lead_managers** - Manager lookup table

### Views Created

1. **vw_vendor_overview** - Vendor + current contract info
2. **vw_vendor_risk_summary** - Vendor + latest risk assessment
3. **vw_vendor_ownership** - Vendor + shareholders + directors
4. **vw_docs_expiring** - Documents expiring in next 90 days
5. **vw_upcoming_reviews** - Reviews due in next 60 days
6. **vw_vendor_stats** - High-level statistics
7. **vw_risk_distribution** - Risk category distribution

## Data Validation

The ETL script includes:

- Column name cleaning and standardization
- Data type validation and conversion
- Enum value mapping
- Transaction-based processing (rollback on errors)
- Comprehensive logging

## Troubleshooting

### Common Issues

1. **Column not found**: Check Excel column names match expected patterns
2. **Enum value errors**: Update type mappings in the script for new values
3. **Date parsing errors**: Ensure dates are in DD/MM/YYYY or YYYY-MM-DD format
4. **Connection errors**: Verify database credentials and connectivity

### Logs

The script provides detailed logging to help identify issues:

```bash
python load_vendors.py 2>&1 | tee etl.log
```

## Post-Migration

After successful migration:

1. Verify data using the views: `SELECT * FROM vw_vendor_overview LIMIT 5`
2. Check statistics: `SELECT * FROM vw_vendor_stats`
3. Update application connection strings to use new schema
4. Test frontend vendor management features

## Rollback

To rollback the migration:

```sql
-- Drop all tables and views
DROP VIEW IF EXISTS vw_upcoming_reviews, vw_docs_expiring, vw_vendor_ownership, vw_vendor_risk_summary, vw_vendor_overview, vw_vendor_stats, vw_risk_distribution CASCADE;
DROP TABLE IF EXISTS directors, shareholders, documents, reviews, risk_assessments, contracts, vendors, lead_managers CASCADE;
DROP TYPE IF EXISTS vendor_type, service_type, document_type, risk_level CASCADE;
``` 