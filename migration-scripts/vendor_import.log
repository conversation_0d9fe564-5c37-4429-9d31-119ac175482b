2025-07-10 11:12:39,192 - ERROR - Database connection failed: connection to server at "localhost" (::1), port 5432 failed: fe_sendauth: no password supplied

2025-07-10 11:12:39,193 - ERROR - Import failed: connection to server at "localhost" (::1), port 5432 failed: fe_sendauth: no password supplied

2025-07-10 11:12:39,193 - INFO - Database connection closed
2025-07-10 11:12:39,193 - ERROR - Import failed: connection to server at "localhost" (::1), port 5432 failed: fe_sendauth: no password supplied

2025-07-10 11:13:28,531 - INFO - Connected to database successfully
2025-07-10 11:13:29,123 - INFO - Read 29 vendor records from Excel
2025-07-10 11:13:29,123 - INFO - Columns found: ['Unnamed: 0', 'Unnamed: 1', 'Unnamed: 2', 'Unnamed: 3', 'Unnamed: 4', 'Unnamed: 5', 'Unnamed: 6', 'Unnamed: 7', 'Unnamed: 8', 'Unnamed: 9', 'Unnamed: 10', 'Unnamed: 11', 'Weight', 'Unnamed: 13', 1.5, 1, 0.8, 0.7, '1.1', '0.7.1', '0.7.2', 0.5, '0.8.1', 0.4, 'Unnamed: 24', 'Unnamed: 25', 'Unnamed: 26', 'Unnamed: 27', 'Unnamed: 28', 'Unnamed: 29', 'Unnamed: 30', 'Unnamed: 31', 'Unnamed: 32', 'Unnamed: 33', 'Unnamed: 34', 'Unnamed: 35', 'Unnamed: 36', 'Unnamed: 37', 'Unnamed: 38', 'Unnamed: 39', 'Unnamed: 40', 'Unnamed: 41', 'Unnamed: 42', 'Unnamed: 43', 'Unnamed: 44', 'Unnamed: 45', 'Unnamed: 46', 'Unnamed: 47']
2025-07-10 11:13:29,125 - INFO - DRY RUN: Would process vendor: Unknown
2025-07-10 11:13:29,125 - INFO - DRY RUN: Would process vendor: Unknown
2025-07-10 11:13:29,125 - INFO - DRY RUN: Would process vendor: Unknown
2025-07-10 11:13:29,125 - INFO - DRY RUN: Would process vendor: Unknown
2025-07-10 11:13:29,125 - INFO - DRY RUN: Would process vendor: Unknown
2025-07-10 11:13:29,125 - INFO - DRY RUN: Would process vendor: Unknown
2025-07-10 11:13:29,125 - INFO - DRY RUN: Would process vendor: Unknown
2025-07-10 11:13:29,126 - INFO - DRY RUN: Would process vendor: Unknown
2025-07-10 11:13:29,126 - INFO - DRY RUN: Would process vendor: Unknown
2025-07-10 11:13:29,126 - INFO - DRY RUN: Would process vendor: Unknown
2025-07-10 11:13:29,126 - INFO - DRY RUN: Would process vendor: Unknown
2025-07-10 11:13:29,126 - INFO - DRY RUN: Would process vendor: Unknown
2025-07-10 11:13:29,126 - INFO - DRY RUN: Would process vendor: Unknown
2025-07-10 11:13:29,126 - INFO - DRY RUN: Would process vendor: Unknown
2025-07-10 11:13:29,126 - INFO - DRY RUN: Would process vendor: Unknown
2025-07-10 11:13:29,126 - INFO - DRY RUN: Would process vendor: Unknown
2025-07-10 11:13:29,126 - INFO - DRY RUN: Would process vendor: Unknown
2025-07-10 11:13:29,126 - INFO - DRY RUN: Would process vendor: Unknown
2025-07-10 11:13:29,126 - INFO - DRY RUN: Would process vendor: Unknown
2025-07-10 11:13:29,126 - INFO - DRY RUN: Would process vendor: Unknown
2025-07-10 11:13:29,126 - INFO - DRY RUN: Would process vendor: Unknown
2025-07-10 11:13:29,127 - INFO - DRY RUN: Would process vendor: Unknown
2025-07-10 11:13:29,127 - INFO - DRY RUN: Would process vendor: Unknown
2025-07-10 11:13:29,127 - INFO - DRY RUN: Would process vendor: Unknown
2025-07-10 11:13:29,127 - INFO - DRY RUN: Would process vendor: Unknown
2025-07-10 11:13:29,127 - INFO - DRY RUN: Would process vendor: Unknown
2025-07-10 11:13:29,127 - INFO - DRY RUN: Would process vendor: Unknown
2025-07-10 11:13:29,127 - INFO - DRY RUN: Would process vendor: Unknown
2025-07-10 11:13:29,127 - INFO - DRY RUN: Would process vendor: Unknown
2025-07-10 11:13:29,127 - INFO - DRY RUN completed. Would process 29 vendors
2025-07-10 11:13:29,127 - INFO - Database connection closed
2025-07-10 11:13:29,127 - INFO - Import process completed successfully
2025-07-10 11:14:36,273 - INFO - Connected to database successfully
2025-07-10 11:14:36,561 - INFO - Read 28 vendor records from Excel
2025-07-10 11:14:36,561 - INFO - Columns found: ['Company Name', 'Contract Active', 'Type of Service Provider', 'EDD Required', 'Website', 'Type of Service', 'Lead Manager', 'Contract Start Date', 'Contract Renewal Date', 'Last Vendor Review', 'Next Vendor Review', 'End Date', 'Unnamed: 12', 'Risk Assessment Date', 'Operational Risk', 'Confidential Data Handling', 'Regulatory Risk', 'Policies', 'Service Level Agreement', 'Business Continuity Plan', 'External Audits', 'Customer Verification', 'Concentration Risk', 'Outsourcing Risk', 'Overall Risk', 'Unnamed: 25', 'Vendor Questionaire ', 'Certifications', 'Unnamed: 28', 'Legal Name', 'Company Address', 'Business Type', 'Cpuntries of Operation', 'Date of Incorporation', 'Registration Number', 'Incorporation Documents', 'Share Register', 'Shareholder Names', 'Shareholder Address', 'Shareholder Proof of Address', 'Shareholder Nationality', 'Shareholder IDs', 'Director Register', 'Director Names', 'Director Address', 'Director Proof of Address', 'Director Nationality', 'Director IDs']
2025-07-10 11:14:36,566 - INFO - DRY RUN: Would process vendor: GoDaddy
2025-07-10 11:14:36,568 - INFO - DRY RUN: Would process vendor: Modulus Global Inc.
2025-07-10 11:14:36,568 - INFO - DRY RUN: Would process vendor: Onfido Limited
2025-07-10 11:14:36,568 - INFO - DRY RUN: Would process vendor: Mattermost
2025-07-10 11:14:36,569 - INFO - DRY RUN: Would process vendor: FreshDesk
2025-07-10 11:14:36,569 - INFO - DRY RUN: Would process vendor: Amazon Web Services (AWS)
2025-07-10 11:14:36,569 - INFO - DRY RUN: Would process vendor: Xero
2025-07-10 11:14:36,569 - INFO - DRY RUN: Would process vendor: Trading View
2025-07-10 11:14:36,570 - INFO - DRY RUN: Would process vendor: Dropbox
2025-07-10 11:14:36,570 - INFO - DRY RUN: Would process vendor: COMBATEAFRAUDE TECNOLOGIA DA INFORMAÇÃO SA (“CAF’)
2025-07-10 11:14:36,570 - INFO - DRY RUN: Would process vendor: B2C2
2025-07-10 11:14:36,570 - INFO - DRY RUN: Would process vendor: Coinbase
2025-07-10 11:14:36,570 - INFO - DRY RUN: Would process vendor: Twingate
2025-07-10 11:14:36,570 - INFO - DRY RUN: Would process vendor: Bitwarden
2025-07-10 11:14:36,570 - INFO - DRY RUN: Would process vendor: Asana
2025-07-10 11:14:36,570 - INFO - DRY RUN: Would process vendor: Azure Active Directory (Microsoft Entra ID)
2025-07-10 11:14:36,570 - INFO - DRY RUN: Would process vendor: Cloudflare
2025-07-10 11:14:36,571 - INFO - DRY RUN: Would process vendor: Sentry
2025-07-10 11:14:36,572 - INFO - DRY RUN: Would process vendor: Bitdefender
2025-07-10 11:14:36,572 - INFO - DRY RUN: Would process vendor: Solidus Labs
2025-07-10 11:14:36,572 - INFO - DRY RUN: Would process vendor: Chainalysis
2025-07-10 11:14:36,572 - INFO - DRY RUN: Would process vendor: Kandji
2025-07-10 11:14:36,572 - INFO - DRY RUN: Would process vendor: Sumsub
2025-07-10 11:14:36,572 - INFO - DRY RUN: Would process vendor: Hexnode
2025-07-10 11:14:36,572 - INFO - DRY RUN: Would process vendor: Fireblocks Ltd.
2025-07-10 11:14:36,572 - INFO - DRY RUN: Would process vendor: Notabene
2025-07-10 11:14:36,572 - INFO - DRY RUN: Would process vendor: Shift Markets Group Inc.
2025-07-10 11:14:36,573 - INFO - DRY RUN: Would process vendor: Secureframe
2025-07-10 11:14:36,573 - INFO - DRY RUN completed. Would process 28 vendors
2025-07-10 11:14:36,573 - INFO - Database connection closed
2025-07-10 11:14:36,573 - INFO - Import process completed successfully
2025-07-10 11:15:18,749 - INFO - Connected to database successfully
2025-07-10 11:15:18,966 - INFO - Read 28 vendor records from Excel
2025-07-10 11:15:18,966 - INFO - Columns found: ['Company Name', 'Contract Active', 'Type of Service Provider', 'EDD Required', 'Website', 'Type of Service', 'Lead Manager', 'Contract Start Date', 'Contract Renewal Date', 'Last Vendor Review', 'Next Vendor Review', 'End Date', 'Unnamed: 12', 'Risk Assessment Date', 'Operational Risk', 'Confidential Data Handling', 'Regulatory Risk', 'Policies', 'Service Level Agreement', 'Business Continuity Plan', 'External Audits', 'Customer Verification', 'Concentration Risk', 'Outsourcing Risk', 'Overall Risk', 'Unnamed: 25', 'Vendor Questionaire', 'Certifications', 'Unnamed: 28', 'Legal Name', 'Company Address', 'Business Type', 'Cpuntries of Operation', 'Date of Incorporation', 'Registration Number', 'Incorporation Documents', 'Share Register', 'Shareholder Names', 'Shareholder Address', 'Shareholder Proof of Address', 'Shareholder Nationality', 'Shareholder IDs', 'Director Register', 'Director Names', 'Director Address', 'Director Proof of Address', 'Director Nationality', 'Director IDs']
2025-07-10 11:15:18,968 - INFO - DRY RUN: Would process vendor: GoDaddy
2025-07-10 11:15:18,968 - INFO - DRY RUN: Would process vendor: Modulus Global Inc.
2025-07-10 11:15:18,968 - INFO - DRY RUN: Would process vendor: Onfido Limited
2025-07-10 11:15:18,968 - INFO - DRY RUN: Would process vendor: Mattermost
2025-07-10 11:15:18,969 - INFO - DRY RUN: Would process vendor: FreshDesk
2025-07-10 11:15:18,969 - INFO - DRY RUN: Would process vendor: Amazon Web Services (AWS)
2025-07-10 11:15:18,969 - INFO - DRY RUN: Would process vendor: Xero
2025-07-10 11:15:18,969 - INFO - DRY RUN: Would process vendor: Trading View
2025-07-10 11:15:18,969 - INFO - DRY RUN: Would process vendor: Dropbox
2025-07-10 11:15:18,969 - INFO - DRY RUN: Would process vendor: COMBATEAFRAUDE TECNOLOGIA DA INFORMAÇÃO SA (“CAF’)
2025-07-10 11:15:18,969 - INFO - DRY RUN: Would process vendor: B2C2
2025-07-10 11:15:18,969 - INFO - DRY RUN: Would process vendor: Coinbase
2025-07-10 11:15:18,969 - INFO - DRY RUN: Would process vendor: Twingate
2025-07-10 11:15:18,969 - INFO - DRY RUN: Would process vendor: Bitwarden
2025-07-10 11:15:18,969 - INFO - DRY RUN: Would process vendor: Asana
2025-07-10 11:15:18,969 - INFO - DRY RUN: Would process vendor: Azure Active Directory (Microsoft Entra ID)
2025-07-10 11:15:18,969 - INFO - DRY RUN: Would process vendor: Cloudflare
2025-07-10 11:15:18,969 - INFO - DRY RUN: Would process vendor: Sentry
2025-07-10 11:15:18,969 - INFO - DRY RUN: Would process vendor: Bitdefender
2025-07-10 11:15:18,969 - INFO - DRY RUN: Would process vendor: Solidus Labs
2025-07-10 11:15:18,969 - INFO - DRY RUN: Would process vendor: Chainalysis
2025-07-10 11:15:18,969 - INFO - DRY RUN: Would process vendor: Kandji
2025-07-10 11:15:18,969 - INFO - DRY RUN: Would process vendor: Sumsub
2025-07-10 11:15:18,969 - INFO - DRY RUN: Would process vendor: Hexnode
2025-07-10 11:15:18,970 - INFO - DRY RUN: Would process vendor: Fireblocks Ltd.
2025-07-10 11:15:18,970 - INFO - DRY RUN: Would process vendor: Notabene
2025-07-10 11:15:18,970 - INFO - DRY RUN: Would process vendor: Shift Markets Group Inc.
2025-07-10 11:15:18,970 - INFO - DRY RUN: Would process vendor: Secureframe
2025-07-10 11:15:18,970 - INFO - DRY RUN completed. Would process 28 vendors
2025-07-10 11:15:18,970 - INFO - Database connection closed
2025-07-10 11:15:18,970 - INFO - Import process completed successfully
2025-07-10 11:16:00,837 - INFO - Connected to database successfully
2025-07-10 11:16:01,033 - INFO - Read 28 vendor records from Excel
2025-07-10 11:16:01,033 - INFO - Columns found: ['Company Name', 'Contract Active', 'Type of Service Provider', 'EDD Required', 'Website', 'Type of Service', 'Lead Manager', 'Contract Start Date', 'Contract Renewal Date', 'Last Vendor Review', 'Next Vendor Review', 'End Date', 'Unnamed: 12', 'Risk Assessment Date', 'Operational Risk', 'Confidential Data Handling', 'Regulatory Risk', 'Policies', 'Service Level Agreement', 'Business Continuity Plan', 'External Audits', 'Customer Verification', 'Concentration Risk', 'Outsourcing Risk', 'Overall Risk', 'Unnamed: 25', 'Vendor Questionaire', 'Certifications', 'Unnamed: 28', 'Legal Name', 'Company Address', 'Business Type', 'Cpuntries of Operation', 'Date of Incorporation', 'Registration Number', 'Incorporation Documents', 'Share Register', 'Shareholder Names', 'Shareholder Address', 'Shareholder Proof of Address', 'Shareholder Nationality', 'Shareholder IDs', 'Director Register', 'Director Names', 'Director Address', 'Director Proof of Address', 'Director Nationality', 'Director IDs']
2025-07-10 11:16:01,033 - ERROR - Import failed: 'psycopg2.extensions.connection' object has no attribute 'begin'
2025-07-10 11:16:01,033 - INFO - Database connection closed
2025-07-10 11:16:01,033 - ERROR - Import failed: 'psycopg2.extensions.connection' object has no attribute 'begin'
2025-07-10 11:16:26,399 - INFO - Connected to database successfully
2025-07-10 11:16:26,599 - INFO - Read 28 vendor records from Excel
2025-07-10 11:16:26,599 - INFO - Columns found: ['Company Name', 'Contract Active', 'Type of Service Provider', 'EDD Required', 'Website', 'Type of Service', 'Lead Manager', 'Contract Start Date', 'Contract Renewal Date', 'Last Vendor Review', 'Next Vendor Review', 'End Date', 'Unnamed: 12', 'Risk Assessment Date', 'Operational Risk', 'Confidential Data Handling', 'Regulatory Risk', 'Policies', 'Service Level Agreement', 'Business Continuity Plan', 'External Audits', 'Customer Verification', 'Concentration Risk', 'Outsourcing Risk', 'Overall Risk', 'Unnamed: 25', 'Vendor Questionaire', 'Certifications', 'Unnamed: 28', 'Legal Name', 'Company Address', 'Business Type', 'Cpuntries of Operation', 'Date of Incorporation', 'Registration Number', 'Incorporation Documents', 'Share Register', 'Shareholder Names', 'Shareholder Address', 'Shareholder Proof of Address', 'Shareholder Nationality', 'Shareholder IDs', 'Director Register', 'Director Names', 'Director Address', 'Director Proof of Address', 'Director Nationality', 'Director IDs']
2025-07-10 11:16:26,624 - ERROR - Error processing vendor row: relation "lead_managers" does not exist
LINE 1: SELECT manager_id FROM lead_managers WHERE manager_name = 'D...
                               ^

2025-07-10 11:16:26,624 - ERROR - Row data: {'Company Name': 'GoDaddy', 'Contract Active': 'Yes', 'Type of Service Provider': 'Non-Customer Data IT Providers', 'EDD Required': 'No', 'Website': 'www.godaddy.com', 'Type of Service': 'Domain hosting', 'Lead Manager': 'David Miller', 'Contract Start Date': Timestamp('2021-11-01 00:00:00'), 'Contract Renewal Date': 'Quarterly', 'Last Vendor Review': datetime.datetime(2025, 3, 5, 0, 0), 'Next Vendor Review': nan, 'End Date': 'Continuous', 'Unnamed: 12': nan, 'Risk Assessment Date': nan, 'Operational Risk': 'Operations barely affected ', 'Confidential Data Handling': 'Corporate Data', 'Regulatory Risk': 'Not Regulated (not Required)', 'Policies': 'No Policies Required', 'Service Level Agreement': 'Available', 'Business Continuity Plan': 'Not Required', 'External Audits': 'Not Required', 'Customer Verification': 'No Customer Verification', 'Concentration Risk': 'Several Alternatives readily available ', 'Outsourcing Risk': 'Service is fully outsourced', 'Overall Risk': 'Medium', 'Unnamed: 25': nan, 'Vendor Questionaire': nan, 'Certifications': nan, 'Unnamed: 28': nan, 'Legal Name': nan, 'Company Address': nan, 'Business Type': nan, 'Cpuntries of Operation': nan, 'Date of Incorporation': NaT, 'Registration Number': nan, 'Incorporation Documents': nan, 'Share Register': nan, 'Shareholder Names': nan, 'Shareholder Address': nan, 'Shareholder Proof of Address': nan, 'Shareholder Nationality': nan, 'Shareholder IDs': nan, 'Director Register': nan, 'Director Names': nan, 'Director Address': nan, 'Director Proof of Address': nan, 'Director Nationality': nan, 'Director IDs': nan}
2025-07-10 11:16:26,624 - ERROR - Failed to process row 0: relation "lead_managers" does not exist
LINE 1: SELECT manager_id FROM lead_managers WHERE manager_name = 'D...
                               ^

2025-07-10 11:16:26,627 - ERROR - Error processing vendor row: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,628 - ERROR - Row data: {'Company Name': 'Modulus Global Inc.', 'Contract Active': 'No', 'Type of Service Provider': 'Critical IT Infrastructure', 'EDD Required': 'Yes', 'Website': nan, 'Type of Service': 'Software License Agreement Exchange Platform', 'Lead Manager': 'David Miller', 'Contract Start Date': Timestamp('2022-02-11 00:00:00'), 'Contract Renewal Date': 'Not Renewed', 'Last Vendor Review': datetime.datetime(2022, 6, 1, 0, 0), 'Next Vendor Review': nan, 'End Date': 'No longer in use (June 2023)', 'Unnamed: 12': nan, 'Risk Assessment Date': nan, 'Operational Risk': 'Entire platform non-functional', 'Confidential Data Handling': 'Personal Customer Data', 'Regulatory Risk': 'Not Regulated (but Required)', 'Policies': 'Policies only Partially available', 'Service Level Agreement': 'Available', 'Business Continuity Plan': 'Not Required', 'External Audits': 'Tech Only (Pen Test, ISO etc.)', 'Customer Verification': 'Simple Customer Verification', 'Concentration Risk': 'Switch to alternative time intensive and costly', 'Outsourcing Risk': 'Service is mainly or fully in-house', 'Overall Risk': nan, 'Unnamed: 25': nan, 'Vendor Questionaire': nan, 'Certifications': 'ISO 27001', 'Unnamed: 28': nan, 'Legal Name': nan, 'Company Address': nan, 'Business Type': nan, 'Cpuntries of Operation': nan, 'Date of Incorporation': NaT, 'Registration Number': nan, 'Incorporation Documents': nan, 'Share Register': nan, 'Shareholder Names': nan, 'Shareholder Address': nan, 'Shareholder Proof of Address': nan, 'Shareholder Nationality': nan, 'Shareholder IDs': nan, 'Director Register': nan, 'Director Names': nan, 'Director Address': nan, 'Director Proof of Address': nan, 'Director Nationality': nan, 'Director IDs': nan}
2025-07-10 11:16:26,628 - ERROR - Failed to process row 1: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,639 - ERROR - Error processing vendor row: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,639 - ERROR - Row data: {'Company Name': 'Onfido Limited', 'Contract Active': 'No', 'Type of Service Provider': 'Critical IT Infrastructure', 'EDD Required': 'Yes', 'Website': nan, 'Type of Service': 'Know Your Customer – Document Verification', 'Lead Manager': 'Thomas Landgraf', 'Contract Start Date': Timestamp('2022-07-01 00:00:00'), 'Contract Renewal Date': 'Not Renewed', 'Last Vendor Review': datetime.datetime(2023, 6, 1, 0, 0), 'Next Vendor Review': nan, 'End Date': 'No Longer in Use (31 June 2023)', 'Unnamed: 12': nan, 'Risk Assessment Date': nan, 'Operational Risk': 'Major parts of the platform down; significant operations affected', 'Confidential Data Handling': 'Personal Customer Data', 'Regulatory Risk': 'Regulated as IT Provider', 'Policies': 'Policies Available', 'Service Level Agreement': 'Available', 'Business Continuity Plan': 'Yes', 'External Audits': 'Tech Only (Pen Test, ISO etc.)', 'Customer Verification': 'Simple Customer Verification', 'Concentration Risk': 'Switch to alternative time intensive and costly', 'Outsourcing Risk': 'Service is mainly or fully in-house', 'Overall Risk': 'Low', 'Unnamed: 25': nan, 'Vendor Questionaire': 'Link', 'Certifications': 'SOC 2, ISO 27001, ISO 27018', 'Unnamed: 28': nan, 'Legal Name': nan, 'Company Address': nan, 'Business Type': nan, 'Cpuntries of Operation': nan, 'Date of Incorporation': NaT, 'Registration Number': nan, 'Incorporation Documents': nan, 'Share Register': nan, 'Shareholder Names': nan, 'Shareholder Address': nan, 'Shareholder Proof of Address': nan, 'Shareholder Nationality': nan, 'Shareholder IDs': nan, 'Director Register': nan, 'Director Names': nan, 'Director Address': nan, 'Director Proof of Address': nan, 'Director Nationality': nan, 'Director IDs': nan}
2025-07-10 11:16:26,639 - ERROR - Failed to process row 2: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,641 - ERROR - Error processing vendor row: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,642 - ERROR - Row data: {'Company Name': 'Mattermost', 'Contract Active': 'Yes', 'Type of Service Provider': 'Basic IT Tool', 'EDD Required': 'No', 'Website': 'mattermost.com', 'Type of Service': 'Internal Messaing System', 'Lead Manager': 'Prasanta Sahoo', 'Contract Start Date': Timestamp('2024-07-22 00:00:00'), 'Contract Renewal Date': 'No Contract', 'Last Vendor Review': 'Not Required', 'Next Vendor Review': nan, 'End Date': 'No Contract', 'Unnamed: 12': nan, 'Risk Assessment Date': nan, 'Operational Risk': 'No impact on operations. E.g., non-essential tools down but with available workarounds.', 'Confidential Data Handling': 'Corporate Data', 'Regulatory Risk': 'Not Regulated (not Required)', 'Policies': 'No Policies Required', 'Service Level Agreement': 'Available', 'Business Continuity Plan': 'Not Required', 'External Audits': 'Not Required', 'Customer Verification': 'Simple Customer Verification', 'Concentration Risk': 'Several Alternatives readily available ', 'Outsourcing Risk': 'Service is mainly or fully in-house', 'Overall Risk': 'Low', 'Unnamed: 25': nan, 'Vendor Questionaire': nan, 'Certifications': 'ISO 27001', 'Unnamed: 28': nan, 'Legal Name': nan, 'Company Address': nan, 'Business Type': nan, 'Cpuntries of Operation': nan, 'Date of Incorporation': NaT, 'Registration Number': nan, 'Incorporation Documents': nan, 'Share Register': nan, 'Shareholder Names': nan, 'Shareholder Address': nan, 'Shareholder Proof of Address': nan, 'Shareholder Nationality': nan, 'Shareholder IDs': nan, 'Director Register': nan, 'Director Names': nan, 'Director Address': nan, 'Director Proof of Address': nan, 'Director Nationality': nan, 'Director IDs': nan}
2025-07-10 11:16:26,643 - ERROR - Failed to process row 3: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,645 - ERROR - Error processing vendor row: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,646 - ERROR - Row data: {'Company Name': 'FreshDesk', 'Contract Active': 'Yes', 'Type of Service Provider': 'Basic IT Tool', 'EDD Required': 'No', 'Website': 'www.freshdesk.com', 'Type of Service': 'Customer Support Management', 'Lead Manager': 'David Miller', 'Contract Start Date': Timestamp('2023-07-01 00:00:00'), 'Contract Renewal Date': 'No Contract', 'Last Vendor Review': datetime.datetime(2023, 7, 1, 0, 0), 'Next Vendor Review': nan, 'End Date': 'Continuous', 'Unnamed: 12': nan, 'Risk Assessment Date': nan, 'Operational Risk': 'Operations barely affected ', 'Confidential Data Handling': 'Anonymized Customer Data', 'Regulatory Risk': 'Not Regulated (not Required)', 'Policies': 'No Policies Required', 'Service Level Agreement': 'Available', 'Business Continuity Plan': 'Not Required', 'External Audits': 'Not Required', 'Customer Verification': 'No Customer Verification', 'Concentration Risk': 'Switch to alternative time intensive and costly', 'Outsourcing Risk': 'Service is fully outsourced', 'Overall Risk': 'Medium', 'Unnamed: 25': nan, 'Vendor Questionaire': nan, 'Certifications': nan, 'Unnamed: 28': nan, 'Legal Name': nan, 'Company Address': nan, 'Business Type': nan, 'Cpuntries of Operation': nan, 'Date of Incorporation': NaT, 'Registration Number': nan, 'Incorporation Documents': nan, 'Share Register': nan, 'Shareholder Names': nan, 'Shareholder Address': nan, 'Shareholder Proof of Address': nan, 'Shareholder Nationality': nan, 'Shareholder IDs': nan, 'Director Register': nan, 'Director Names': nan, 'Director Address': nan, 'Director Proof of Address': nan, 'Director Nationality': nan, 'Director IDs': nan}
2025-07-10 11:16:26,646 - ERROR - Failed to process row 4: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,647 - ERROR - Error processing vendor row: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,648 - ERROR - Row data: {'Company Name': 'Amazon Web Services (AWS)', 'Contract Active': 'Yes', 'Type of Service Provider': 'Critical IT Infrastructure', 'EDD Required': 'No', 'Website': nan, 'Type of Service': 'Cloud infrastructure to run exchange platform and store company and custody related data. ', 'Lead Manager': 'Prasanta Sahoo', 'Contract Start Date': Timestamp('2022-02-18 00:00:00'), 'Contract Renewal Date': 'Monthly Recurring', 'Last Vendor Review': datetime.datetime(2024, 11, 18, 0, 0), 'Next Vendor Review': nan, 'End Date': 'Continuous', 'Unnamed: 12': nan, 'Risk Assessment Date': nan, 'Operational Risk': 'Entire platform non-functional', 'Confidential Data Handling': 'Personal Customer Data', 'Regulatory Risk': 'Not Regulated (but Required)', 'Policies': 'No Policies Required', 'Service Level Agreement': 'Not Available', 'Business Continuity Plan': 'No', 'External Audits': 'Tech Only (Pen Test, ISO etc.)', 'Customer Verification': 'Simple Customer Verification', 'Concentration Risk': 'Switch to alternative time intensive and costly', 'Outsourcing Risk': 'Service is fully outsourced', 'Overall Risk': 'High', 'Unnamed: 25': nan, 'Vendor Questionaire': 'Link', 'Certifications': nan, 'Unnamed: 28': nan, 'Legal Name': nan, 'Company Address': nan, 'Business Type': nan, 'Cpuntries of Operation': nan, 'Date of Incorporation': NaT, 'Registration Number': nan, 'Incorporation Documents': nan, 'Share Register': nan, 'Shareholder Names': nan, 'Shareholder Address': nan, 'Shareholder Proof of Address': nan, 'Shareholder Nationality': nan, 'Shareholder IDs': nan, 'Director Register': nan, 'Director Names': nan, 'Director Address': nan, 'Director Proof of Address': nan, 'Director Nationality': nan, 'Director IDs': nan}
2025-07-10 11:16:26,648 - ERROR - Failed to process row 5: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,649 - ERROR - Error processing vendor row: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,650 - ERROR - Row data: {'Company Name': 'Xero', 'Contract Active': 'Yes', 'Type of Service Provider': 'Non-Customer Data IT Providers', 'EDD Required': 'No', 'Website': nan, 'Type of Service': 'Accounting Software', 'Lead Manager': 'Alberto Turri', 'Contract Start Date': Timestamp('2022-03-01 00:00:00'), 'Contract Renewal Date': 'Monthly Recurring', 'Last Vendor Review': datetime.datetime(2024, 11, 18, 0, 0), 'Next Vendor Review': nan, 'End Date': 'Continuous', 'Unnamed: 12': nan, 'Risk Assessment Date': nan, 'Operational Risk': 'No impact on operations. E.g., non-essential tools down but with available workarounds.', 'Confidential Data Handling': 'Corporate Data', 'Regulatory Risk': 'Not Regulated (not Required)', 'Policies': 'No Policies Required', 'Service Level Agreement': 'Not Required', 'Business Continuity Plan': 'Not Required', 'External Audits': 'Not Required', 'Customer Verification': 'Not Required', 'Concentration Risk': 'Several Alternatives readily available ', 'Outsourcing Risk': 'Service is mainly or fully in-house', 'Overall Risk': 'Low', 'Unnamed: 25': nan, 'Vendor Questionaire': nan, 'Certifications': nan, 'Unnamed: 28': nan, 'Legal Name': nan, 'Company Address': nan, 'Business Type': nan, 'Cpuntries of Operation': nan, 'Date of Incorporation': NaT, 'Registration Number': nan, 'Incorporation Documents': nan, 'Share Register': nan, 'Shareholder Names': nan, 'Shareholder Address': nan, 'Shareholder Proof of Address': nan, 'Shareholder Nationality': nan, 'Shareholder IDs': nan, 'Director Register': nan, 'Director Names': nan, 'Director Address': nan, 'Director Proof of Address': nan, 'Director Nationality': nan, 'Director IDs': nan}
2025-07-10 11:16:26,650 - ERROR - Failed to process row 6: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,652 - ERROR - Error processing vendor row: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,654 - ERROR - Row data: {'Company Name': 'Trading View', 'Contract Active': 'Yes', 'Type of Service Provider': 'Basic IT Tool', 'EDD Required': 'No', 'Website': nan, 'Type of Service': 'Trading Data Visualization', 'Lead Manager': 'Prasanta Sahoo', 'Contract Start Date': Timestamp('2022-03-16 00:00:00'), 'Contract Renewal Date': 'Monthly Recurring', 'Last Vendor Review': 'Not Required', 'Next Vendor Review': nan, 'End Date': 'Continuous', 'Unnamed: 12': nan, 'Risk Assessment Date': nan, 'Operational Risk': 'No impact on operations. E.g., non-essential tools down but with available workarounds.', 'Confidential Data Handling': 'No Data being Stored', 'Regulatory Risk': 'Not Regulated (not Required)', 'Policies': 'No Policies Required', 'Service Level Agreement': 'Not Required', 'Business Continuity Plan': 'Not Required', 'External Audits': 'Not Required', 'Customer Verification': 'Not Required', 'Concentration Risk': 'Several Alternatives readily available ', 'Outsourcing Risk': 'Service is mainly or fully in-house', 'Overall Risk': 'Low', 'Unnamed: 25': nan, 'Vendor Questionaire': nan, 'Certifications': nan, 'Unnamed: 28': nan, 'Legal Name': nan, 'Company Address': nan, 'Business Type': nan, 'Cpuntries of Operation': nan, 'Date of Incorporation': NaT, 'Registration Number': nan, 'Incorporation Documents': nan, 'Share Register': nan, 'Shareholder Names': nan, 'Shareholder Address': nan, 'Shareholder Proof of Address': nan, 'Shareholder Nationality': nan, 'Shareholder IDs': nan, 'Director Register': nan, 'Director Names': nan, 'Director Address': nan, 'Director Proof of Address': nan, 'Director Nationality': nan, 'Director IDs': nan}
2025-07-10 11:16:26,654 - ERROR - Failed to process row 7: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,655 - ERROR - Error processing vendor row: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,656 - ERROR - Row data: {'Company Name': 'Dropbox', 'Contract Active': 'Yes', 'Type of Service Provider': 'Non-Customer Data IT Providers', 'EDD Required': 'No', 'Website': nan, 'Type of Service': 'Cloud Data Storage', 'Lead Manager': 'David Miller', 'Contract Start Date': Timestamp('2022-07-07 00:00:00'), 'Contract Renewal Date': 'Monthly Recurring', 'Last Vendor Review': datetime.datetime(2022, 7, 7, 0, 0), 'Next Vendor Review': nan, 'End Date': 'Continuous', 'Unnamed: 12': nan, 'Risk Assessment Date': nan, 'Operational Risk': 'No impact on operations. E.g., non-essential tools down but with available workarounds.', 'Confidential Data Handling': 'Corporate Data', 'Regulatory Risk': 'Not Regulated (not Required)', 'Policies': 'No Policies Required', 'Service Level Agreement': 'Not Required', 'Business Continuity Plan': 'Not Required', 'External Audits': 'Tech Only (Pen Test, ISO etc.)', 'Customer Verification': 'Not Required', 'Concentration Risk': 'Several Alternatives readily available ', 'Outsourcing Risk': 'Service is mainly or fully in-house', 'Overall Risk': 'Low', 'Unnamed: 25': nan, 'Vendor Questionaire': nan, 'Certifications': 'ISO 27001', 'Unnamed: 28': nan, 'Legal Name': nan, 'Company Address': nan, 'Business Type': nan, 'Cpuntries of Operation': nan, 'Date of Incorporation': NaT, 'Registration Number': nan, 'Incorporation Documents': nan, 'Share Register': nan, 'Shareholder Names': nan, 'Shareholder Address': nan, 'Shareholder Proof of Address': nan, 'Shareholder Nationality': nan, 'Shareholder IDs': nan, 'Director Register': nan, 'Director Names': nan, 'Director Address': nan, 'Director Proof of Address': nan, 'Director Nationality': nan, 'Director IDs': nan}
2025-07-10 11:16:26,656 - ERROR - Failed to process row 8: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,657 - ERROR - Error processing vendor row: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,657 - ERROR - Row data: {'Company Name': 'COMBATEAFRAUDE TECNOLOGIA DA INFORMAÇÃO SA (“CAF’)', 'Contract Active': 'Yes', 'Type of Service Provider': 'Critical IT Infrastructure', 'EDD Required': 'Yes', 'Website': nan, 'Type of Service': 'Know Your Customer – Document Verification', 'Lead Manager': 'Thomas Landgraf', 'Contract Start Date': Timestamp('2023-04-18 00:00:00'), 'Contract Renewal Date': 'Monthly Recurring', 'Last Vendor Review': datetime.datetime(2024, 11, 18, 0, 0), 'Next Vendor Review': nan, 'End Date': 'Continuous', 'Unnamed: 12': nan, 'Risk Assessment Date': nan, 'Operational Risk': 'Major parts of the platform down; significant operations affected', 'Confidential Data Handling': 'Personal Customer Data', 'Regulatory Risk': 'Regulated in High-Risk Jurisdiction', 'Policies': 'Policies Available', 'Service Level Agreement': 'Available', 'Business Continuity Plan': 'Yes', 'External Audits': 'Tech Only (Pen Test, ISO etc.)', 'Customer Verification': 'Enhanced Customer Verification', 'Concentration Risk': 'Switch to alternative time intensive and costly', 'Outsourcing Risk': 'Service is mainly or fully in-house', 'Overall Risk': 'Low', 'Unnamed: 25': nan, 'Vendor Questionaire': nan, 'Certifications': nan, 'Unnamed: 28': nan, 'Legal Name': nan, 'Company Address': nan, 'Business Type': nan, 'Cpuntries of Operation': nan, 'Date of Incorporation': NaT, 'Registration Number': nan, 'Incorporation Documents': nan, 'Share Register': nan, 'Shareholder Names': nan, 'Shareholder Address': nan, 'Shareholder Proof of Address': nan, 'Shareholder Nationality': nan, 'Shareholder IDs': nan, 'Director Register': nan, 'Director Names': nan, 'Director Address': nan, 'Director Proof of Address': nan, 'Director Nationality': nan, 'Director IDs': nan}
2025-07-10 11:16:26,658 - ERROR - Failed to process row 9: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,658 - ERROR - Error processing vendor row: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,659 - ERROR - Row data: {'Company Name': 'B2C2', 'Contract Active': 'Yes', 'Type of Service Provider': 'Critical IT Infrastructure', 'EDD Required': 'No', 'Website': nan, 'Type of Service': 'Liquidity Provider', 'Lead Manager': 'Alberto Turri', 'Contract Start Date': Timestamp('2023-06-06 00:00:00'), 'Contract Renewal Date': 'Monthly Recurring', 'Last Vendor Review': datetime.datetime(2024, 4, 4, 0, 0), 'Next Vendor Review': nan, 'End Date': 'Continuous', 'Unnamed: 12': nan, 'Risk Assessment Date': nan, 'Operational Risk': 'Major parts of the platform down; significant operations affected', 'Confidential Data Handling': 'No Data being Stored', 'Regulatory Risk': 'Regulated as IT Provider or Financial Institution', 'Policies': 'Policies Available', 'Service Level Agreement': 'Not Required', 'Business Continuity Plan': 'Yes', 'External Audits': 'Tech and Finance', 'Customer Verification': 'Original Documents Necessary', 'Concentration Risk': 'Several Alternatives readily available ', 'Outsourcing Risk': 'Service is mainly or fully in-house', 'Overall Risk': 'Low', 'Unnamed: 25': nan, 'Vendor Questionaire': 'Link', 'Certifications': nan, 'Unnamed: 28': nan, 'Legal Name': nan, 'Company Address': nan, 'Business Type': nan, 'Cpuntries of Operation': nan, 'Date of Incorporation': NaT, 'Registration Number': nan, 'Incorporation Documents': nan, 'Share Register': nan, 'Shareholder Names': nan, 'Shareholder Address': nan, 'Shareholder Proof of Address': nan, 'Shareholder Nationality': nan, 'Shareholder IDs': nan, 'Director Register': nan, 'Director Names': nan, 'Director Address': nan, 'Director Proof of Address': nan, 'Director Nationality': nan, 'Director IDs': nan}
2025-07-10 11:16:26,659 - ERROR - Failed to process row 10: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,661 - ERROR - Error processing vendor row: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,661 - ERROR - Row data: {'Company Name': 'Coinbase', 'Contract Active': 'Yes', 'Type of Service Provider': 'Critical IT Infrastructure', 'EDD Required': 'No', 'Website': nan, 'Type of Service': 'Liquidity Provider', 'Lead Manager': 'Alberto Turri', 'Contract Start Date': Timestamp('2023-06-15 00:00:00'), 'Contract Renewal Date': 'Monthly Recurring', 'Last Vendor Review': datetime.datetime(2024, 4, 4, 0, 0), 'Next Vendor Review': nan, 'End Date': 'Continuous', 'Unnamed: 12': nan, 'Risk Assessment Date': nan, 'Operational Risk': 'Major parts of the platform down; significant operations affected', 'Confidential Data Handling': 'No Data being Stored', 'Regulatory Risk': 'Regulated as IT Provider or Financial Institution', 'Policies': 'Policies Available', 'Service Level Agreement': 'Not Required', 'Business Continuity Plan': 'Yes', 'External Audits': 'Tech and Finance', 'Customer Verification': 'Original Documents Necessary', 'Concentration Risk': 'Several Alternatives readily available ', 'Outsourcing Risk': 'Service is mainly or fully in-house', 'Overall Risk': 'Low', 'Unnamed: 25': nan, 'Vendor Questionaire': 'Link', 'Certifications': nan, 'Unnamed: 28': nan, 'Legal Name': 'Shift Markets Group Inc.', 'Company Address': '1013 Centre\nRoad, Suite 403S, 19805 Wilmington, Delaware', 'Business Type': 'Trading Platform White label Provider', 'Cpuntries of Operation': 'Europe, US and Asia', 'Date of Incorporation': Timestamp('2022-02-10 00:00:00'), 'Registration Number': ***********.0, 'Incorporation Documents': 'Link', 'Share Register': 'Link', 'Shareholder Names': 'Shift Ventures LLC (100%): \nM2 Ventures (79.56%)\nBlack Birch Technologies (20.44%)\nIan McAffee (50% M2)\nMatthew Miller (50% M2)\nAnthony di sSanti (100% Black Birch)', 'Shareholder Address': 'Anthony:\n150 E 44 ST 51G, New YorkNY 10017-4085\nIan (15 RIDGE DRIVE PD CHAPPAQUA NY 10514-2612)\nMatthew: 15 Jomarr Ct Massapequa NY 11758-7933)', 'Shareholder Proof of Address': 'UBO Document Folder', 'Shareholder Nationality': 'USA', 'Shareholder IDs': 'UBO Document Folder', 'Director Register': 'Link', 'Director Names': 'Ian McAffee \nMatthew Miller\nAnthony di sSanti', 'Director Address': 'Shift Ventures LLC (100%): \nM2 Ventures (79.56%)\nBlack Birch Technologies (20.44%)\nIan McAffee (50% M2)\nMatthew Miller (50% M2)\nAnthony di sSanti (100% Black Birch)', 'Director Proof of Address': 'Link', 'Director Nationality': nan, 'Director IDs': nan}
2025-07-10 11:16:26,661 - ERROR - Failed to process row 11: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,663 - ERROR - Error processing vendor row: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,663 - ERROR - Row data: {'Company Name': 'Twingate', 'Contract Active': 'Yes', 'Type of Service Provider': 'Basic IT Tool', 'EDD Required': 'No', 'Website': 'twingate.com', 'Type of Service': 'VPN Solution', 'Lead Manager': 'Prasanta Sahoo', 'Contract Start Date': Timestamp('2024-07-22 00:00:00'), 'Contract Renewal Date': 'Monthly Recurring', 'Last Vendor Review': datetime.datetime(2024, 7, 22, 0, 0), 'Next Vendor Review': nan, 'End Date': 'Continuous', 'Unnamed: 12': nan, 'Risk Assessment Date': nan, 'Operational Risk': 'Operations barely affected ', 'Confidential Data Handling': 'No Data being Stored', 'Regulatory Risk': 'Not Regulated (not Required)', 'Policies': 'No Policies Required', 'Service Level Agreement': 'Available', 'Business Continuity Plan': 'Not Required', 'External Audits': 'Tech Only (Pen Test, ISO etc.)', 'Customer Verification': 'Simple Customer Verification', 'Concentration Risk': 'Several Alternatives readily available ', 'Outsourcing Risk': 'Service is mainly or fully in-house', 'Overall Risk': 'Low', 'Unnamed: 25': nan, 'Vendor Questionaire': 'Link', 'Certifications': nan, 'Unnamed: 28': nan, 'Legal Name': nan, 'Company Address': nan, 'Business Type': nan, 'Cpuntries of Operation': nan, 'Date of Incorporation': NaT, 'Registration Number': nan, 'Incorporation Documents': nan, 'Share Register': nan, 'Shareholder Names': nan, 'Shareholder Address': nan, 'Shareholder Proof of Address': nan, 'Shareholder Nationality': nan, 'Shareholder IDs': nan, 'Director Register': nan, 'Director Names': nan, 'Director Address': nan, 'Director Proof of Address': nan, 'Director Nationality': nan, 'Director IDs': nan}
2025-07-10 11:16:26,663 - ERROR - Failed to process row 12: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,665 - ERROR - Error processing vendor row: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,666 - ERROR - Row data: {'Company Name': 'Bitwarden', 'Contract Active': 'Yes', 'Type of Service Provider': 'Non-Customer Data IT Providers', 'EDD Required': 'No', 'Website': 'https://bitwarden.com', 'Type of Service': 'Password Manager', 'Lead Manager': 'Prasanta Sahoo', 'Contract Start Date': Timestamp('2024-07-22 00:00:00'), 'Contract Renewal Date': 'Monthly Recurring', 'Last Vendor Review': datetime.datetime(2024, 7, 22, 0, 0), 'Next Vendor Review': nan, 'End Date': 'Continuous', 'Unnamed: 12': nan, 'Risk Assessment Date': nan, 'Operational Risk': 'No impact on operations. E.g., non-essential tools down but with available workarounds.', 'Confidential Data Handling': 'No Data being Stored', 'Regulatory Risk': 'Not Regulated (not Required)', 'Policies': 'No Policies Required', 'Service Level Agreement': 'Not Required', 'Business Continuity Plan': 'Not Required', 'External Audits': 'Not Required', 'Customer Verification': 'No Customer Verification', 'Concentration Risk': 'Several Alternatives readily available ', 'Outsourcing Risk': 'Service is fully outsourced', 'Overall Risk': 'Low', 'Unnamed: 25': nan, 'Vendor Questionaire': 'Link', 'Certifications': nan, 'Unnamed: 28': nan, 'Legal Name': nan, 'Company Address': nan, 'Business Type': nan, 'Cpuntries of Operation': nan, 'Date of Incorporation': NaT, 'Registration Number': nan, 'Incorporation Documents': nan, 'Share Register': nan, 'Shareholder Names': nan, 'Shareholder Address': nan, 'Shareholder Proof of Address': nan, 'Shareholder Nationality': nan, 'Shareholder IDs': nan, 'Director Register': nan, 'Director Names': nan, 'Director Address': nan, 'Director Proof of Address': nan, 'Director Nationality': nan, 'Director IDs': nan}
2025-07-10 11:16:26,666 - ERROR - Failed to process row 13: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,667 - ERROR - Error processing vendor row: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,667 - ERROR - Row data: {'Company Name': 'Asana', 'Contract Active': 'Yes', 'Type of Service Provider': 'Basic IT Tool', 'EDD Required': 'No', 'Website': 'www.asana.com', 'Type of Service': 'Project Management', 'Lead Manager': 'David Miller', 'Contract Start Date': Timestamp('2022-01-01 00:00:00'), 'Contract Renewal Date': 'Monthly Recurring', 'Last Vendor Review': datetime.datetime(2022, 1, 1, 0, 0), 'Next Vendor Review': nan, 'End Date': 'Continuous', 'Unnamed: 12': nan, 'Risk Assessment Date': nan, 'Operational Risk': 'Operations barely affected ', 'Confidential Data Handling': 'Corporate Data', 'Regulatory Risk': 'Not Regulated (not Required)', 'Policies': 'No Policies Required', 'Service Level Agreement': 'Not Required', 'Business Continuity Plan': 'Not Required', 'External Audits': 'Not Required', 'Customer Verification': 'No Customer Verification', 'Concentration Risk': 'Several Alternatives readily available ', 'Outsourcing Risk': 'Service is fully outsourced', 'Overall Risk': 'Low', 'Unnamed: 25': nan, 'Vendor Questionaire': nan, 'Certifications': nan, 'Unnamed: 28': nan, 'Legal Name': nan, 'Company Address': nan, 'Business Type': nan, 'Cpuntries of Operation': nan, 'Date of Incorporation': NaT, 'Registration Number': nan, 'Incorporation Documents': nan, 'Share Register': nan, 'Shareholder Names': nan, 'Shareholder Address': nan, 'Shareholder Proof of Address': nan, 'Shareholder Nationality': nan, 'Shareholder IDs': nan, 'Director Register': nan, 'Director Names': nan, 'Director Address': nan, 'Director Proof of Address': nan, 'Director Nationality': nan, 'Director IDs': nan}
2025-07-10 11:16:26,667 - ERROR - Failed to process row 14: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,668 - ERROR - Error processing vendor row: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,669 - ERROR - Row data: {'Company Name': 'Azure Active Directory (Microsoft Entra ID)', 'Contract Active': 'Yes', 'Type of Service Provider': 'Critical IT Infrastructure', 'EDD Required': 'No', 'Website': 'https://www.microsoft.com/en-us/security/business/identity-access/microsoft-entra-id', 'Type of Service': 'Cloud Service Provider', 'Lead Manager': 'Prasanta Sahoo', 'Contract Start Date': NaT, 'Contract Renewal Date': 'Monthly Recurring', 'Last Vendor Review': nan, 'Next Vendor Review': nan, 'End Date': 'Continuous', 'Unnamed: 12': nan, 'Risk Assessment Date': nan, 'Operational Risk': 'Major parts of the platform down; significant operations affected', 'Confidential Data Handling': 'Personal Customer Data', 'Regulatory Risk': 'Not Regulated (not Required)', 'Policies': 'Policies only Partially available', 'Service Level Agreement': 'Available', 'Business Continuity Plan': 'Yes', 'External Audits': 'Tech Only (Pen Test, ISO etc.)', 'Customer Verification': 'No Customer Verification', 'Concentration Risk': 'Switch to alternative time intensive and costly', 'Outsourcing Risk': 'Service is fully outsourced', 'Overall Risk': 'High', 'Unnamed: 25': nan, 'Vendor Questionaire': 'Link', 'Certifications': nan, 'Unnamed: 28': nan, 'Legal Name': nan, 'Company Address': nan, 'Business Type': nan, 'Cpuntries of Operation': nan, 'Date of Incorporation': NaT, 'Registration Number': nan, 'Incorporation Documents': nan, 'Share Register': nan, 'Shareholder Names': nan, 'Shareholder Address': nan, 'Shareholder Proof of Address': nan, 'Shareholder Nationality': nan, 'Shareholder IDs': nan, 'Director Register': nan, 'Director Names': nan, 'Director Address': nan, 'Director Proof of Address': nan, 'Director Nationality': nan, 'Director IDs': nan}
2025-07-10 11:16:26,669 - ERROR - Failed to process row 15: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,670 - ERROR - Error processing vendor row: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,670 - ERROR - Row data: {'Company Name': 'Cloudflare', 'Contract Active': 'Yes', 'Type of Service Provider': 'Critical IT Infrastructure', 'EDD Required': 'No', 'Website': 'www.cloudflare.com', 'Type of Service': 'Cloud Cybersecurity', 'Lead Manager': 'Prasanta Sahoo', 'Contract Start Date': Timestamp('2022-02-01 00:00:00'), 'Contract Renewal Date': 'Monthly Recurring', 'Last Vendor Review': datetime.datetime(2022, 2, 1, 0, 0), 'Next Vendor Review': nan, 'End Date': 'Continuous', 'Unnamed: 12': nan, 'Risk Assessment Date': nan, 'Operational Risk': 'Major parts of the platform down; significant operations affected', 'Confidential Data Handling': 'Personal Customer Data', 'Regulatory Risk': 'Not Regulated (not Required)', 'Policies': 'No Policies Required', 'Service Level Agreement': 'Available', 'Business Continuity Plan': 'Yes', 'External Audits': 'Tech Only (Pen Test, ISO etc.)', 'Customer Verification': 'No Customer Verification', 'Concentration Risk': 'Switch to alternative time intensive and costly', 'Outsourcing Risk': 'Service is fully outsourced', 'Overall Risk': 'High', 'Unnamed: 25': nan, 'Vendor Questionaire': nan, 'Certifications': nan, 'Unnamed: 28': nan, 'Legal Name': nan, 'Company Address': nan, 'Business Type': nan, 'Cpuntries of Operation': nan, 'Date of Incorporation': NaT, 'Registration Number': nan, 'Incorporation Documents': nan, 'Share Register': nan, 'Shareholder Names': nan, 'Shareholder Address': nan, 'Shareholder Proof of Address': nan, 'Shareholder Nationality': nan, 'Shareholder IDs': nan, 'Director Register': nan, 'Director Names': nan, 'Director Address': nan, 'Director Proof of Address': nan, 'Director Nationality': nan, 'Director IDs': nan}
2025-07-10 11:16:26,670 - ERROR - Failed to process row 16: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,671 - ERROR - Error processing vendor row: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,671 - ERROR - Row data: {'Company Name': 'Sentry', 'Contract Active': 'Yes', 'Type of Service Provider': 'Basic IT Tool', 'EDD Required': 'No', 'Website': 'sentry.com', 'Type of Service': 'Front End Bug Detection System', 'Lead Manager': 'Prasanta Sahoo', 'Contract Start Date': Timestamp('2025-01-14 00:00:00'), 'Contract Renewal Date': 'Monthly Recurring', 'Last Vendor Review': datetime.datetime(2025, 1, 14, 0, 0), 'Next Vendor Review': nan, 'End Date': 'Continuous', 'Unnamed: 12': nan, 'Risk Assessment Date': nan, 'Operational Risk': 'No impact on operations. E.g., non-essential tools down but with available workarounds.', 'Confidential Data Handling': 'No Data being Stored', 'Regulatory Risk': 'Not Regulated (not Required)', 'Policies': 'No Policies Required', 'Service Level Agreement': 'Not Available', 'Business Continuity Plan': 'Not Required', 'External Audits': 'Not Required', 'Customer Verification': 'Not Required', 'Concentration Risk': 'Several Alternatives readily available ', 'Outsourcing Risk': 'Service is fully outsourced', 'Overall Risk': 'Low', 'Unnamed: 25': nan, 'Vendor Questionaire': 'Not Required', 'Certifications': 'Link ', 'Unnamed: 28': nan, 'Legal Name': nan, 'Company Address': nan, 'Business Type': nan, 'Cpuntries of Operation': nan, 'Date of Incorporation': NaT, 'Registration Number': nan, 'Incorporation Documents': nan, 'Share Register': nan, 'Shareholder Names': nan, 'Shareholder Address': nan, 'Shareholder Proof of Address': nan, 'Shareholder Nationality': nan, 'Shareholder IDs': nan, 'Director Register': nan, 'Director Names': nan, 'Director Address': nan, 'Director Proof of Address': nan, 'Director Nationality': nan, 'Director IDs': nan}
2025-07-10 11:16:26,671 - ERROR - Failed to process row 17: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,672 - ERROR - Error processing vendor row: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,673 - ERROR - Row data: {'Company Name': 'Bitdefender', 'Contract Active': 'Yes', 'Type of Service Provider': 'Basic IT Tool', 'EDD Required': 'No', 'Website': 'www.bitdefender.com', 'Type of Service': 'Ant0virus', 'Lead Manager': 'Stefan Barbuta', 'Contract Start Date': Timestamp('2023-08-01 00:00:00'), 'Contract Renewal Date': datetime.datetime(2025, 8, 1, 0, 0), 'Last Vendor Review': datetime.datetime(2023, 8, 1, 0, 0), 'Next Vendor Review': nan, 'End Date': 'Continuous', 'Unnamed: 12': nan, 'Risk Assessment Date': nan, 'Operational Risk': 'No impact on operations. E.g., non-essential tools down but with available workarounds.', 'Confidential Data Handling': 'No Data being Stored', 'Regulatory Risk': 'Not Regulated (not Required)', 'Policies': 'No Policies Required', 'Service Level Agreement': 'Available', 'Business Continuity Plan': 'Not Required', 'External Audits': 'Not Required', 'Customer Verification': 'No Customer Verification', 'Concentration Risk': 'Several Alternatives readily available ', 'Outsourcing Risk': 'Service is fully outsourced', 'Overall Risk': 'Low', 'Unnamed: 25': nan, 'Vendor Questionaire': 'Not Required', 'Certifications': 'Link ', 'Unnamed: 28': nan, 'Legal Name': nan, 'Company Address': nan, 'Business Type': nan, 'Cpuntries of Operation': nan, 'Date of Incorporation': NaT, 'Registration Number': nan, 'Incorporation Documents': nan, 'Share Register': nan, 'Shareholder Names': nan, 'Shareholder Address': nan, 'Shareholder Proof of Address': nan, 'Shareholder Nationality': nan, 'Shareholder IDs': nan, 'Director Register': nan, 'Director Names': nan, 'Director Address': nan, 'Director Proof of Address': nan, 'Director Nationality': nan, 'Director IDs': nan}
2025-07-10 11:16:26,673 - ERROR - Failed to process row 18: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,673 - ERROR - Error processing vendor row: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,674 - ERROR - Row data: {'Company Name': 'Solidus Labs', 'Contract Active': 'Yes', 'Type of Service Provider': 'Critical IT Infrastructure', 'EDD Required': 'Yes', 'Website': nan, 'Type of Service': 'Trade Surveillance Solution', 'Lead Manager': 'Thomas Landgraf', 'Contract Start Date': Timestamp('2023-06-16 00:00:00'), 'Contract Renewal Date': datetime.datetime(2026, 6, 15, 0, 0), 'Last Vendor Review': datetime.datetime(2023, 10, 9, 0, 0), 'Next Vendor Review': nan, 'End Date': datetime.datetime(2026, 6, 15, 0, 0), 'Unnamed: 12': nan, 'Risk Assessment Date': nan, 'Operational Risk': 'Operations barely affected ', 'Confidential Data Handling': 'Anonymized Customer Data', 'Regulatory Risk': 'Not Regulated (not Required)', 'Policies': 'No Policies Required', 'Service Level Agreement': 'Available', 'Business Continuity Plan': 'Yes', 'External Audits': 'Tech Only (Pen Test, ISO etc.)', 'Customer Verification': 'Simple Customer Verification', 'Concentration Risk': 'Switch to alternative time intensive and costly', 'Outsourcing Risk': 'Service is mainly or fully in-house', 'Overall Risk': 'Low', 'Unnamed: 25': nan, 'Vendor Questionaire': 'Not Required', 'Certifications': 'Link ', 'Unnamed: 28': nan, 'Legal Name': nan, 'Company Address': nan, 'Business Type': nan, 'Cpuntries of Operation': nan, 'Date of Incorporation': NaT, 'Registration Number': nan, 'Incorporation Documents': nan, 'Share Register': nan, 'Shareholder Names': nan, 'Shareholder Address': nan, 'Shareholder Proof of Address': nan, 'Shareholder Nationality': nan, 'Shareholder IDs': nan, 'Director Register': nan, 'Director Names': nan, 'Director Address': nan, 'Director Proof of Address': nan, 'Director Nationality': nan, 'Director IDs': nan}
2025-07-10 11:16:26,674 - ERROR - Failed to process row 19: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,675 - ERROR - Error processing vendor row: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,675 - ERROR - Row data: {'Company Name': 'Chainalysis', 'Contract Active': 'Yes', 'Type of Service Provider': 'Non-Customer Data IT Providers', 'EDD Required': 'No', 'Website': nan, 'Type of Service': 'Onchain Analytics Solution (“KYT”)', 'Lead Manager': 'Thomas Landgraf', 'Contract Start Date': Timestamp('2023-04-11 00:00:00'), 'Contract Renewal Date': datetime.datetime(2025, 7, 7, 0, 0), 'Last Vendor Review': datetime.datetime(2024, 11, 18, 0, 0), 'Next Vendor Review': nan, 'End Date': datetime.datetime(2024, 8, 31, 0, 0), 'Unnamed: 12': nan, 'Risk Assessment Date': nan, 'Operational Risk': 'Major parts of the platform down; significant operations affected', 'Confidential Data Handling': 'Anonymized Customer Data', 'Regulatory Risk': 'Regulated as IT Provider', 'Policies': 'Policies Available', 'Service Level Agreement': 'Available', 'Business Continuity Plan': 'Yes', 'External Audits': 'No Audit Available', 'Customer Verification': 'Simple Customer Verification', 'Concentration Risk': 'Switch to alternative time intensive and costly', 'Outsourcing Risk': 'Service is mainly or fully in-house', 'Overall Risk': 'Low', 'Unnamed: 25': nan, 'Vendor Questionaire': nan, 'Certifications': nan, 'Unnamed: 28': nan, 'Legal Name': nan, 'Company Address': nan, 'Business Type': nan, 'Cpuntries of Operation': nan, 'Date of Incorporation': NaT, 'Registration Number': nan, 'Incorporation Documents': nan, 'Share Register': nan, 'Shareholder Names': nan, 'Shareholder Address': nan, 'Shareholder Proof of Address': nan, 'Shareholder Nationality': nan, 'Shareholder IDs': nan, 'Director Register': nan, 'Director Names': nan, 'Director Address': nan, 'Director Proof of Address': nan, 'Director Nationality': nan, 'Director IDs': nan}
2025-07-10 11:16:26,675 - ERROR - Failed to process row 20: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,676 - ERROR - Error processing vendor row: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,676 - ERROR - Row data: {'Company Name': 'Kandji', 'Contract Active': 'Yes', 'Type of Service Provider': 'Basic IT Tool', 'EDD Required': 'No', 'Website': nan, 'Type of Service': 'Mobile Device Management (Apple)', 'Lead Manager': 'Stefan Barbuta', 'Contract Start Date': Timestamp('2022-07-06 00:00:00'), 'Contract Renewal Date': datetime.datetime(2025, 8, 1, 0, 0), 'Last Vendor Review': datetime.datetime(2024, 11, 18, 0, 0), 'Next Vendor Review': nan, 'End Date': datetime.datetime(2025, 7, 6, 0, 0), 'Unnamed: 12': nan, 'Risk Assessment Date': nan, 'Operational Risk': 'No impact on operations. E.g., non-essential tools down but with available workarounds.', 'Confidential Data Handling': 'Corporate Data', 'Regulatory Risk': 'Not Regulated (not Required)', 'Policies': 'No Policies Required', 'Service Level Agreement': 'Not Required', 'Business Continuity Plan': 'Not Required', 'External Audits': 'Tech Only (Pen Test, ISO etc.)', 'Customer Verification': 'Not Required', 'Concentration Risk': 'Several Alternatives readily available ', 'Outsourcing Risk': 'Service is mainly or fully in-house', 'Overall Risk': 'Low', 'Unnamed: 25': nan, 'Vendor Questionaire': nan, 'Certifications': nan, 'Unnamed: 28': nan, 'Legal Name': nan, 'Company Address': nan, 'Business Type': nan, 'Cpuntries of Operation': nan, 'Date of Incorporation': NaT, 'Registration Number': nan, 'Incorporation Documents': nan, 'Share Register': nan, 'Shareholder Names': nan, 'Shareholder Address': nan, 'Shareholder Proof of Address': nan, 'Shareholder Nationality': nan, 'Shareholder IDs': nan, 'Director Register': nan, 'Director Names': nan, 'Director Address': nan, 'Director Proof of Address': nan, 'Director Nationality': nan, 'Director IDs': nan}
2025-07-10 11:16:26,676 - ERROR - Failed to process row 21: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,677 - ERROR - Error processing vendor row: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,677 - ERROR - Row data: {'Company Name': 'Sumsub', 'Contract Active': 'Yes', 'Type of Service Provider': 'Critical IT Infrastructure', 'EDD Required': 'Yes', 'Website': nan, 'Type of Service': 'Know Your Customer – Document Verification', 'Lead Manager': 'Thomas Landgraf', 'Contract Start Date': Timestamp('2023-07-07 00:00:00'), 'Contract Renewal Date': 'Monthly Recurring', 'Last Vendor Review': datetime.datetime(2023, 7, 7, 0, 0), 'Next Vendor Review': nan, 'End Date': datetime.datetime(2025, 7, 6, 0, 0), 'Unnamed: 12': nan, 'Risk Assessment Date': nan, 'Operational Risk': 'Major parts of the platform down; significant operations affected', 'Confidential Data Handling': 'Personal Customer Data', 'Regulatory Risk': 'Regulated as IT Provider or Financial Institution', 'Policies': 'Policies Available', 'Service Level Agreement': 'Available', 'Business Continuity Plan': 'Yes', 'External Audits': 'Tech Only (Pen Test, ISO etc.)', 'Customer Verification': 'Enhanced Customer Verification', 'Concentration Risk': 'Switch to alternative time intensive and costly', 'Outsourcing Risk': 'Service is mainly or fully in-house', 'Overall Risk': 'Low', 'Unnamed: 25': nan, 'Vendor Questionaire': nan, 'Certifications': nan, 'Unnamed: 28': nan, 'Legal Name': nan, 'Company Address': nan, 'Business Type': nan, 'Cpuntries of Operation': nan, 'Date of Incorporation': NaT, 'Registration Number': nan, 'Incorporation Documents': nan, 'Share Register': nan, 'Shareholder Names': nan, 'Shareholder Address': nan, 'Shareholder Proof of Address': nan, 'Shareholder Nationality': nan, 'Shareholder IDs': nan, 'Director Register': nan, 'Director Names': nan, 'Director Address': nan, 'Director Proof of Address': nan, 'Director Nationality': nan, 'Director IDs': nan}
2025-07-10 11:16:26,677 - ERROR - Failed to process row 22: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,678 - ERROR - Error processing vendor row: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,679 - ERROR - Row data: {'Company Name': 'Hexnode', 'Contract Active': 'Yes', 'Type of Service Provider': 'Basic IT Tool', 'EDD Required': 'No', 'Website': nan, 'Type of Service': 'Windows Device Management ', 'Lead Manager': 'Stefan Barbuta', 'Contract Start Date': Timestamp('2023-06-15 00:00:00'), 'Contract Renewal Date': datetime.datetime(2025, 6, 14, 0, 0), 'Last Vendor Review': datetime.datetime(2024, 3, 25, 0, 0), 'Next Vendor Review': nan, 'End Date': datetime.datetime(2025, 6, 14, 0, 0), 'Unnamed: 12': nan, 'Risk Assessment Date': nan, 'Operational Risk': 'No impact on operations. E.g., non-essential tools down but with available workarounds.', 'Confidential Data Handling': 'No Data being Stored', 'Regulatory Risk': 'Not Regulated (not Required)', 'Policies': 'No Policies Required', 'Service Level Agreement': 'Not Required', 'Business Continuity Plan': 'Not Required', 'External Audits': 'Tech Only (Pen Test, ISO etc.)', 'Customer Verification': 'Not Required', 'Concentration Risk': 'Several Alternatives readily available ', 'Outsourcing Risk': 'Service is mainly or fully in-house', 'Overall Risk': 'Low', 'Unnamed: 25': nan, 'Vendor Questionaire': nan, 'Certifications': nan, 'Unnamed: 28': nan, 'Legal Name': nan, 'Company Address': nan, 'Business Type': nan, 'Cpuntries of Operation': nan, 'Date of Incorporation': NaT, 'Registration Number': nan, 'Incorporation Documents': nan, 'Share Register': nan, 'Shareholder Names': nan, 'Shareholder Address': nan, 'Shareholder Proof of Address': nan, 'Shareholder Nationality': nan, 'Shareholder IDs': nan, 'Director Register': nan, 'Director Names': nan, 'Director Address': nan, 'Director Proof of Address': nan, 'Director Nationality': nan, 'Director IDs': nan}
2025-07-10 11:16:26,679 - ERROR - Failed to process row 23: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,680 - ERROR - Error processing vendor row: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,680 - ERROR - Row data: {'Company Name': 'Fireblocks Ltd.', 'Contract Active': 'Yes', 'Type of Service Provider': 'Critical IT Infrastructure', 'EDD Required': 'Yes', 'Website': nan, 'Type of Service': 'Virtual Asset Custody Infrastructure Provider', 'Lead Manager': 'David Miller', 'Contract Start Date': Timestamp('2022-02-22 00:00:00'), 'Contract Renewal Date': datetime.datetime(2025, 7, 7, 0, 0), 'Last Vendor Review': datetime.datetime(2024, 11, 18, 0, 0), 'Next Vendor Review': nan, 'End Date': datetime.datetime(2024, 7, 6, 0, 0), 'Unnamed: 12': nan, 'Risk Assessment Date': nan, 'Operational Risk': 'Entire platform non-functional', 'Confidential Data Handling': 'Anonymized Customer Data', 'Regulatory Risk': 'Non-Regulated IT provider', 'Policies': 'Policies Available', 'Service Level Agreement': 'Available', 'Business Continuity Plan': 'Yes', 'External Audits': 'Tech Only (Pen Test, ISO etc.)', 'Customer Verification': 'Simple Customer Verification', 'Concentration Risk': 'Switch to alternative time intensive and costly', 'Outsourcing Risk': 'Service is mainly or fully in-house', 'Overall Risk': 'Low', 'Unnamed: 25': nan, 'Vendor Questionaire': nan, 'Certifications': nan, 'Unnamed: 28': nan, 'Legal Name': nan, 'Company Address': nan, 'Business Type': nan, 'Cpuntries of Operation': nan, 'Date of Incorporation': NaT, 'Registration Number': nan, 'Incorporation Documents': nan, 'Share Register': nan, 'Shareholder Names': nan, 'Shareholder Address': nan, 'Shareholder Proof of Address': nan, 'Shareholder Nationality': nan, 'Shareholder IDs': nan, 'Director Register': nan, 'Director Names': nan, 'Director Address': nan, 'Director Proof of Address': nan, 'Director Nationality': nan, 'Director IDs': nan}
2025-07-10 11:16:26,680 - ERROR - Failed to process row 24: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,681 - ERROR - Error processing vendor row: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,682 - ERROR - Row data: {'Company Name': 'Notabene', 'Contract Active': 'Yes', 'Type of Service Provider': 'Non-Customer Data IT Providers', 'EDD Required': 'No', 'Website': nan, 'Type of Service': 'Virtual Asset Travel Rule Solution', 'Lead Manager': 'Thomas Landgraf', 'Contract Start Date': Timestamp('2022-05-01 00:00:00'), 'Contract Renewal Date': datetime.datetime(2025, 7, 7, 0, 0), 'Last Vendor Review': nan, 'Next Vendor Review': nan, 'End Date': datetime.datetime(2024, 7, 6, 0, 0), 'Unnamed: 12': nan, 'Risk Assessment Date': nan, 'Operational Risk': 'Operations barely affected ', 'Confidential Data Handling': 'Anonymized Customer Data', 'Regulatory Risk': 'Regulated as IT Provider', 'Policies': 'Policies only Partially available', 'Service Level Agreement': 'Available', 'Business Continuity Plan': 'Yes', 'External Audits': 'Tech Only (Pen Test, ISO etc.)', 'Customer Verification': 'Enhanced Customer Verification', 'Concentration Risk': 'Switch to alternative time intensive and costly', 'Outsourcing Risk': 'Service is mainly or fully in-house', 'Overall Risk': 'Low', 'Unnamed: 25': nan, 'Vendor Questionaire': nan, 'Certifications': nan, 'Unnamed: 28': nan, 'Legal Name': nan, 'Company Address': nan, 'Business Type': nan, 'Cpuntries of Operation': nan, 'Date of Incorporation': NaT, 'Registration Number': nan, 'Incorporation Documents': nan, 'Share Register': nan, 'Shareholder Names': nan, 'Shareholder Address': nan, 'Shareholder Proof of Address': nan, 'Shareholder Nationality': nan, 'Shareholder IDs': nan, 'Director Register': nan, 'Director Names': nan, 'Director Address': nan, 'Director Proof of Address': nan, 'Director Nationality': nan, 'Director IDs': nan}
2025-07-10 11:16:26,682 - ERROR - Failed to process row 25: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,682 - ERROR - Error processing vendor row: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,683 - ERROR - Row data: {'Company Name': 'Shift Markets Group Inc.', 'Contract Active': 'Yes', 'Type of Service Provider': 'Critical IT Infrastructure', 'EDD Required': 'Yes', 'Website': nan, 'Type of Service': 'Virtual Asset White label Solution (+ integration)', 'Lead Manager': 'Prasanta Sahoo', 'Contract Start Date': Timestamp('2023-05-27 00:00:00'), 'Contract Renewal Date': nan, 'Last Vendor Review': datetime.datetime(2024, 11, 7, 0, 0), 'Next Vendor Review': nan, 'End Date': datetime.datetime(2026, 5, 26, 0, 0), 'Unnamed: 12': nan, 'Risk Assessment Date': nan, 'Operational Risk': 'Entire platform non-functional', 'Confidential Data Handling': 'No Data being Stored', 'Regulatory Risk': 'Non-Regulated IT provider', 'Policies': 'Policies Available', 'Service Level Agreement': 'Not Available', 'Business Continuity Plan': 'Yes', 'External Audits': 'Tech Only (Pen Test, ISO etc.)', 'Customer Verification': 'Simple Customer Verification', 'Concentration Risk': 'Switch to alternative time intensive and costly', 'Outsourcing Risk': 'Service is mainly or fully in-house', 'Overall Risk': 'Low', 'Unnamed: 25': nan, 'Vendor Questionaire': nan, 'Certifications': nan, 'Unnamed: 28': nan, 'Legal Name': nan, 'Company Address': nan, 'Business Type': nan, 'Cpuntries of Operation': nan, 'Date of Incorporation': NaT, 'Registration Number': nan, 'Incorporation Documents': nan, 'Share Register': nan, 'Shareholder Names': nan, 'Shareholder Address': nan, 'Shareholder Proof of Address': nan, 'Shareholder Nationality': nan, 'Shareholder IDs': nan, 'Director Register': nan, 'Director Names': nan, 'Director Address': nan, 'Director Proof of Address': nan, 'Director Nationality': nan, 'Director IDs': nan}
2025-07-10 11:16:26,683 - ERROR - Failed to process row 26: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,683 - ERROR - Error processing vendor row: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,684 - ERROR - Row data: {'Company Name': 'Secureframe', 'Contract Active': 'Yes', 'Type of Service Provider': 'Non-Customer Data IT Providers', 'EDD Required': 'No', 'Website': nan, 'Type of Service': 'Tech Compliance Management Tool', 'Lead Manager': 'Thomas Landgraf', 'Contract Start Date': Timestamp('2023-05-22 00:00:00'), 'Contract Renewal Date': datetime.datetime(2025, 5, 21, 0, 0), 'Last Vendor Review': nan, 'Next Vendor Review': nan, 'End Date': nan, 'Unnamed: 12': nan, 'Risk Assessment Date': nan, 'Operational Risk': 'No impact on operations. E.g., non-essential tools down but with available workarounds.', 'Confidential Data Handling': 'Corporate Data', 'Regulatory Risk': 'Not Regulated (not Required)', 'Policies': 'Policies Available', 'Service Level Agreement': 'Available', 'Business Continuity Plan': 'Yes', 'External Audits': 'Tech Only (Pen Test, ISO etc.)', 'Customer Verification': 'Simple Customer Verification', 'Concentration Risk': 'Switch to alternative time intensive and costly', 'Outsourcing Risk': 'Service is mainly or fully in-house', 'Overall Risk': 'Low', 'Unnamed: 25': nan, 'Vendor Questionaire': nan, 'Certifications': nan, 'Unnamed: 28': nan, 'Legal Name': nan, 'Company Address': nan, 'Business Type': nan, 'Cpuntries of Operation': nan, 'Date of Incorporation': NaT, 'Registration Number': nan, 'Incorporation Documents': nan, 'Share Register': nan, 'Shareholder Names': nan, 'Shareholder Address': nan, 'Shareholder Proof of Address': nan, 'Shareholder Nationality': nan, 'Shareholder IDs': nan, 'Director Register': nan, 'Director Names': nan, 'Director Address': nan, 'Director Proof of Address': nan, 'Director Nationality': nan, 'Director IDs': nan}
2025-07-10 11:16:26,684 - ERROR - Failed to process row 27: current transaction is aborted, commands ignored until end of transaction block

2025-07-10 11:16:26,685 - ERROR - Import failed. 28 errors encountered. All changes rolled back.
2025-07-10 11:16:26,685 - INFO - Database connection closed
2025-07-10 11:16:26,685 - INFO - Import process completed successfully
2025-07-10 11:17:00,620 - INFO - Connected to database successfully
2025-07-10 11:17:00,816 - INFO - Read 28 vendor records from Excel
2025-07-10 11:17:00,816 - INFO - Columns found: ['Company Name', 'Contract Active', 'Type of Service Provider', 'EDD Required', 'Website', 'Type of Service', 'Lead Manager', 'Contract Start Date', 'Contract Renewal Date', 'Last Vendor Review', 'Next Vendor Review', 'End Date', 'Unnamed: 12', 'Risk Assessment Date', 'Operational Risk', 'Confidential Data Handling', 'Regulatory Risk', 'Policies', 'Service Level Agreement', 'Business Continuity Plan', 'External Audits', 'Customer Verification', 'Concentration Risk', 'Outsourcing Risk', 'Overall Risk', 'Unnamed: 25', 'Vendor Questionaire', 'Certifications', 'Unnamed: 28', 'Legal Name', 'Company Address', 'Business Type', 'Cpuntries of Operation', 'Date of Incorporation', 'Registration Number', 'Incorporation Documents', 'Share Register', 'Shareholder Names', 'Shareholder Address', 'Shareholder Proof of Address', 'Shareholder Nationality', 'Shareholder IDs', 'Director Register', 'Director Names', 'Director Address', 'Director Proof of Address', 'Director Nationality', 'Director IDs']
2025-07-10 11:17:00,836 - INFO - Inserted vendor: GoDaddy (ID: 1)
2025-07-10 11:17:00,843 - INFO - Inserted vendor: Modulus Global Inc. (ID: 2)
2025-07-10 11:17:00,846 - INFO - Inserted vendor: Onfido Limited (ID: 3)
2025-07-10 11:17:00,849 - INFO - Inserted vendor: Mattermost (ID: 4)
2025-07-10 11:17:00,851 - INFO - Inserted vendor: FreshDesk (ID: 5)
2025-07-10 11:17:00,854 - INFO - Inserted vendor: Amazon Web Services (AWS) (ID: 6)
2025-07-10 11:17:00,857 - INFO - Inserted vendor: Xero (ID: 7)
2025-07-10 11:17:00,860 - INFO - Inserted vendor: Trading View (ID: 8)
2025-07-10 11:17:00,862 - INFO - Inserted vendor: Dropbox (ID: 9)
2025-07-10 11:17:00,864 - INFO - Inserted vendor: COMBATEAFRAUDE TECNOLOGIA DA INFORMAÇÃO SA (“CAF’) (ID: 10)
2025-07-10 11:17:00,867 - INFO - Inserted vendor: B2C2 (ID: 11)
2025-07-10 11:17:00,869 - INFO - Inserted vendor: Coinbase (ID: 12)
2025-07-10 11:17:00,877 - INFO - Inserted vendor: Twingate (ID: 13)
2025-07-10 11:17:00,879 - INFO - Inserted vendor: Bitwarden (ID: 14)
2025-07-10 11:17:00,881 - INFO - Inserted vendor: Asana (ID: 15)
2025-07-10 11:17:00,884 - INFO - Inserted vendor: Azure Active Directory (Microsoft Entra ID) (ID: 16)
2025-07-10 11:17:00,885 - INFO - Inserted vendor: Cloudflare (ID: 17)
2025-07-10 11:17:00,888 - INFO - Inserted vendor: Sentry (ID: 18)
2025-07-10 11:17:00,891 - INFO - Inserted vendor: Bitdefender (ID: 19)
2025-07-10 11:17:00,894 - INFO - Inserted vendor: Solidus Labs (ID: 20)
2025-07-10 11:17:00,897 - INFO - Inserted vendor: Chainalysis (ID: 21)
2025-07-10 11:17:00,899 - INFO - Inserted vendor: Kandji (ID: 22)
2025-07-10 11:17:00,902 - INFO - Inserted vendor: Sumsub (ID: 23)
2025-07-10 11:17:00,904 - INFO - Inserted vendor: Hexnode (ID: 24)
2025-07-10 11:17:00,906 - INFO - Inserted vendor: Fireblocks Ltd. (ID: 25)
2025-07-10 11:17:00,909 - INFO - Inserted vendor: Notabene (ID: 26)
2025-07-10 11:17:00,911 - INFO - Inserted vendor: Shift Markets Group Inc. (ID: 27)
2025-07-10 11:17:00,914 - INFO - Inserted vendor: Secureframe (ID: 28)
2025-07-10 11:17:00,916 - INFO - Successfully imported 28 vendors
2025-07-10 11:17:00,916 - INFO - Database connection closed
2025-07-10 11:17:00,916 - INFO - Import process completed successfully
