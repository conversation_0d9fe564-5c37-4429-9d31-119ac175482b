{"$schema": "https://schema.tauri.app/config/2", "productName": "Governance App", "version": "0.1.0", "identifier": "com.governance-app", "build": {"beforeDevCommand": "npm run start", "devUrl": "http://localhost:5173", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "Governance App", "width": 1200, "height": 800, "resizable": true, "fullscreen": false}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}