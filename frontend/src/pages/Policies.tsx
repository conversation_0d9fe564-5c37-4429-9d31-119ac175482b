import { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Dialog,
  Typography,
  IconButton,
  Chip,
  Avatar,
  Stack,
  Divider,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Skeleton,
  TextField,
  InputAdornment,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  History as HistoryIcon,
  MoreVert as MoreVertIcon,
  Policy as PolicyIcon,
  Person as PersonIcon,
  Search as SearchIcon,
  Link as LinkIcon,
  Schedule as ScheduleIcon,
  FilterList as FilterListIcon,
  Sort as SortIcon,
} from '@mui/icons-material';
import AddPolicyForm from '../components/policies/AddPolicyForm';
import { calculateDaysLeft, getStatusColor, getStatusText } from '../utils/policyHelpers';
import { apiService } from '../services/api';
import PolicyVersionHistory from '../components/policies/PolicyVersionHistory';
import { Policy, PolicyVersion } from '../utils/types/policy';

// Extended Policy interface for the UI that includes the latest changes description
interface PolicyWithChanges extends Policy {
  latest_changes_description?: string;
}

type SortField = 'name' | 'next_review' | 'created_at';
type SortDirection = 'asc' | 'desc';

interface SortConfig {
  field: SortField;
  direction: SortDirection;
}

export default function Policies() {
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [policies, setPolicies] = useState<PolicyWithChanges[]>([]);
  const [filteredPolicies, setFilteredPolicies] = useState<PolicyWithChanges[]>([]);
  const [editingPolicy, setEditingPolicy] = useState<PolicyWithChanges | null>(null);
  const [editMode, setEditMode] = useState<'new_version' | 'current_version'>('new_version');
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    field: 'next_review',
    direction: 'asc'
  });
  const [selectedPolicyVersions, setSelectedPolicyVersions] = useState<{
    versions: PolicyVersion[];
    currentVersion: string;
  } | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedPolicy, setSelectedPolicy] = useState<PolicyWithChanges | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'due' | 'overdue' | 'current'>('all');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchPolicies();
  }, []);

  useEffect(() => {
    // Filter and sort policies
    let filtered = policies;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(policy =>
        policy.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        policy.author.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(policy => {
        const daysLeft = policy.next_review ? calculateDaysLeft(policy.next_review) : null;
        switch (statusFilter) {
          case 'overdue':
            return daysLeft !== null && daysLeft < 0;
          case 'due':
            return daysLeft !== null && daysLeft >= 0 && daysLeft <= 30;
          case 'current':
            return daysLeft !== null && daysLeft > 30;
          default:
            return true;
        }
      });
    }

    // Apply sorting
    filtered = sortPolicies(filtered);

    setFilteredPolicies(filtered);
  }, [policies, searchTerm, statusFilter, sortConfig]);

  const sortPolicies = (policiesToSort: PolicyWithChanges[]): PolicyWithChanges[] => {
    return [...policiesToSort].sort((a, b) => {
      let aValue: any = a[sortConfig.field];
      let bValue: any = b[sortConfig.field];

      // Handle date fields
      if (sortConfig.field === 'next_review' || sortConfig.field === 'created_at') {
        aValue = aValue ? new Date(aValue).getTime() : 0;
        bValue = bValue ? new Date(bValue).getTime() : 0;
      }

      // Handle string fields (name)
      if (sortConfig.field === 'name') {
        aValue = aValue?.toLowerCase() || '';
        bValue = bValue?.toLowerCase() || '';
      }

      // Handle null/undefined values
      if (aValue === bValue) return 0;
      if (!aValue && bValue) return 1;
      if (aValue && !bValue) return -1;

      const compareResult = aValue < bValue ? -1 : 1;
      return sortConfig.direction === 'asc' ? compareResult : -compareResult;
    });
  };

  const handleSortChange = (field: SortField) => {
    setSortConfig(current => ({
      field,
      direction: current.field === field && current.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const getSortLabel = () => {
    const labels = {
      name: 'Name',
      next_review: 'Due Date',
      created_at: 'Date Created'
    };
    const direction = sortConfig.direction === 'asc' ? '↑' : '↓';
    return `${labels[sortConfig.field]} ${direction}`;
  };

  async function fetchPolicies() {
    setIsLoading(true);
    try {
      const data = await apiService.getPolicies();
      
      // For now, just use the basic policy data without the complex joins
      // TODO: Update backend to support policy versions join
      const transformedData = data?.map(policy => ({
        ...policy,
        latest_changes_description: null // Will be added when backend supports policy versions
      })) || [];

      setPolicies(transformedData);
    } catch (error) {
      console.error('Error fetching policies:', error);
    } finally {
      setIsLoading(false);
    }
  }

  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this policy?')) {
      try {
        // Make direct API call since delete method isn't in apiService yet
        const response = await fetch(`http://localhost:3030/api/policies/${id}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || `Failed to delete policy`);
        }

        setPolicies(currentPolicies => 
          currentPolicies.filter(policy => policy.id !== id)
        );

      } catch (err) {
        console.error('Error deleting policy:', err);
        alert('Failed to delete policy: ' + (err as Error).message);
      }
    }
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, policy: PolicyWithChanges) => {
    setAnchorEl(event.currentTarget);
    setSelectedPolicy(policy);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedPolicy(null);
  };

  const handleEditPolicy = (policy: Policy, mode: 'new_version' | 'current_version') => {
    setEditingPolicy(policy);
    setEditMode(mode);
    setOpenAddDialog(true);
    handleMenuClose();
  };

  const fetchVersionHistory = async (policyId: string, currentVersion: string) => {
    // TODO: Implement policy versions API endpoint
    console.log('Policy version history not yet implemented for API');
    alert('Policy version history feature is temporarily unavailable during migration');
    
    // Temporary: Set empty versions
    setSelectedPolicyVersions({
      versions: [],
      currentVersion: currentVersion
    });
    handleMenuClose();
  };

  const getStatusInfo = (daysLeft: number | null) => {
    if (daysLeft === null) return { color: 'default', severity: 'unknown', text: 'No review date' };
    
    if (daysLeft < 0) return { color: 'error', severity: 'critical', text: `${Math.abs(daysLeft)} days overdue` };
    if (daysLeft <= 7) return { color: 'error', severity: 'high', text: `${daysLeft} days` };
    if (daysLeft <= 30) return { color: 'warning', severity: 'medium', text: `${daysLeft} days` };
    return { color: 'success', severity: 'low', text: `${daysLeft} days` };
  };

  return (
    <Box>
      {/* Page Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" sx={{ fontWeight: 700, color: '#1e293b', mb: 1 }}>
          Policy Management
        </Typography>
        <Typography variant="body1" sx={{ color: '#64748b' }}>
          Manage organizational policies, track review schedules, and maintain compliance.
        </Typography>
      </Box>

      {/* Search, Filter, and Sort Bar */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Search policies by name or author..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                select
                label="Filter by Status"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as any)}
                SelectProps={{
                  native: true,
                }}
              >
                <option value="all">All Policies</option>
                <option value="overdue">Overdue</option>
                <option value="due">Due Soon (30 days)</option>
                <option value="current">Current</option>
              </TextField>
            </Grid>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                select
                label="Sort by"
                value={`${sortConfig.field}-${sortConfig.direction}`}
                onChange={(e) => {
                  const [field, direction] = e.target.value.split('-') as [SortField, SortDirection];
                  setSortConfig({ field, direction });
                }}
                SelectProps={{
                  native: true,
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SortIcon />
                    </InputAdornment>
                  ),
                }}
              >
                <option value="name-asc">Name (A-Z)</option>
                <option value="name-desc">Name (Z-A)</option>
                <option value="next_review-asc">Due Date (Earliest)</option>
                <option value="next_review-desc">Due Date (Latest)</option>
                <option value="created_at-desc">Date Created (Newest)</option>
                <option value="created_at-asc">Date Created (Oldest)</option>
              </TextField>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => {
                  setEditingPolicy(null);
                  setOpenAddDialog(true);
                }}
                sx={{ height: 56 }}
              >
                Add Policy
              </Button>
            </Grid>
          </Grid>

          {/* Sort Summary */}
          <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="body2" color="text.secondary">
              Showing {filteredPolicies.length} of {policies.length} policies
            </Typography>
            <Chip
              label={getSortLabel()}
              size="small"
              variant="outlined"
              color="primary"
              icon={<SortIcon fontSize="small" />}
            />
          </Box>
        </CardContent>
      </Card>

      {/* Policies Grid */}
      {isLoading ? (
        <Grid container spacing={3}>
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Grid item xs={12} md={6} lg={4} key={i}>
              <Card>
                <CardContent>
                  <Skeleton variant="text" height={32} />
                  <Skeleton variant="text" height={20} width="60%" />
                  <Skeleton variant="rectangular" height={80} sx={{ mt: 2 }} />
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      ) : (
        <Grid container spacing={3}>
          {filteredPolicies.map((policy) => {
            const daysLeft = policy.next_review ? calculateDaysLeft(policy.next_review) : null;
            const statusInfo = getStatusInfo(daysLeft);
            
            return (
              <Grid item xs={12} md={6} lg={4} key={policy.id}>
                <Card 
                  sx={{ 
                    height: '100%', 
                    display: 'flex', 
                    flexDirection: 'column',
                    transition: 'all 0.2s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)',
                    }
                  }}
                >
                  <CardContent sx={{ flexGrow: 1, p: 3 }}>
                    {/* Header */}
                    <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', mb: 2 }}>
                      <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                        <PolicyIcon />
                      </Avatar>
                      <IconButton
                        size="small"
                        onClick={(e) => handleMenuClick(e, policy)}
                      >
                        <MoreVertIcon />
                      </IconButton>
                    </Box>

                    {/* Policy Name */}
                    <Typography 
                      variant="h6" 
                      sx={{ 
                        fontWeight: 600, 
                        mb: 1, 
                        lineHeight: 1.3,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                      }}
                    >
                      {policy.name}
                    </Typography>

                    {/* Version and Author */}
                    <Stack direction="row" spacing={1} sx={{ mb: 2 }}>
                      <Chip 
                        label={`v${policy.current_version}`} 
                        size="small" 
                        color="primary"
                        variant="outlined"
                      />
                      <Chip 
                        label={policy.author} 
                        size="small" 
                        variant="outlined"
                        icon={<PersonIcon />}
                      />
                    </Stack>

                    {/* Review Dates */}
                    <Box sx={{ mb: 2 }}>
                      <Stack direction="row" spacing={2} sx={{ mb: 1 }}>
                        <Box>
                          <Typography variant="caption" color="text.secondary">
                            Created
                          </Typography>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {policy.created_at 
                              ? new Date(policy.created_at).toLocaleDateString() 
                              : 'Unknown'
                            }
                          </Typography>
                        </Box>
                        <Box>
                          <Typography variant="caption" color="text.secondary">
                            Next Review
                          </Typography>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {policy.next_review 
                              ? new Date(policy.next_review).toLocaleDateString() 
                              : 'Not set'
                            }
                          </Typography>
                        </Box>
                      </Stack>
                    </Box>

                    {/* Status */}
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      {daysLeft !== null && (
                        <Chip
                          label={statusInfo.text}
                          color={statusInfo.color as any}
                          size="medium"
                          variant={statusInfo.severity === 'critical' ? 'filled' : 'outlined'}
                          sx={{ fontWeight: 600 }}
                        />
                      )}
                      
                      {policy.link && (
                        <IconButton
                          size="small"
                          component="a"
                          href={policy.link}
                          target="_blank"
                          rel="noopener noreferrer"
                          sx={{ 
                            bgcolor: 'grey.100',
                            '&:hover': { bgcolor: 'grey.200' }
                          }}
                        >
                          <LinkIcon />
                        </IconButton>
                      )}
                    </Box>

                    {/* Latest Changes */}
                    {policy.latest_changes_description && (
                      <Box sx={{ mt: 2, pt: 2, borderTop: '1px solid', borderColor: 'grey.200' }}>
                        <Typography variant="caption" color="text.secondary">
                          Latest Changes
                        </Typography>
                        <Typography 
                          variant="body2" 
                          sx={{ 
                            mt: 0.5,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                          }}
                        >
                          {policy.latest_changes_description}
                        </Typography>
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            );
          })}
        </Grid>
      )}

      {/* Empty State */}
      {!isLoading && filteredPolicies.length === 0 && (
        <Card sx={{ textAlign: 'center', py: 6 }}>
          <CardContent>
            <PolicyIcon sx={{ fontSize: 64, color: 'grey.300', mb: 2 }} />
            <Typography variant="h6" sx={{ mb: 1 }}>
              {searchTerm || statusFilter !== 'all' ? 'No policies found' : 'No policies yet'}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              {searchTerm || statusFilter !== 'all' 
                ? 'Try adjusting your search or filter criteria.'
                : 'Get started by adding your first policy.'
              }
            </Typography>
            {(!searchTerm && statusFilter === 'all') && (
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => {
                  setEditingPolicy(null);
                  setOpenAddDialog(true);
                }}
              >
                Add Your First Policy
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={() => selectedPolicy && handleEditPolicy(selectedPolicy, 'current_version')}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Edit Current Version</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => selectedPolicy && handleEditPolicy(selectedPolicy, 'new_version')}>
          <ListItemIcon>
            <AddIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Create New Version</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => selectedPolicy && fetchVersionHistory(selectedPolicy.id, selectedPolicy.current_version)}>
          <ListItemIcon>
            <HistoryIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>View History</ListItemText>
        </MenuItem>
        <Divider />
        <MenuItem 
          onClick={() => {
            if (selectedPolicy) handleDelete(selectedPolicy.id);
            handleMenuClose();
          }}
          sx={{ color: 'error.main' }}
        >
          <ListItemIcon>
            <DeleteIcon fontSize="small" color="error" />
          </ListItemIcon>
          <ListItemText>Delete Policy</ListItemText>
        </MenuItem>
      </Menu>

      {/* Add/Edit Dialog */}
      <Dialog 
        open={openAddDialog} 
        onClose={() => {
          setOpenAddDialog(false);
          setEditingPolicy(null);
        }}
        maxWidth="md"
        fullWidth
      >
        <AddPolicyForm 
          onClose={() => {
            setOpenAddDialog(false);
            setEditingPolicy(null);
          }}
          onSubmit={() => {
            fetchPolicies();
            setOpenAddDialog(false);
            setEditingPolicy(null);
          }}
          existingPolicy={editingPolicy}
          editMode={editMode}
        />
      </Dialog>

      {/* Version History Dialog */}
      {selectedPolicyVersions && (
        <PolicyVersionHistory
          open={!!selectedPolicyVersions}
          onClose={() => setSelectedPolicyVersions(null)}
          versions={selectedPolicyVersions.versions}
          currentVersion={selectedPolicyVersions.currentVersion}
        />
      )}
    </Box>
  );
}
