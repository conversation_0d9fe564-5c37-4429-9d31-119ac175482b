import { <PERSON>, <PERSON>po<PERSON>, Tabs, Tab, <PERSON><PERSON>, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Dialog, DialogTitle, DialogContent, DialogActions, TextField, Alert, Select, MenuItem, Autocomplete, Chip } from '@mui/material';
import { useState, useEffect } from 'react';
import { supabase } from '../utils/supabase/client';
import EditIcon from '@mui/icons-material/Edit';
import ArchiveIcon from '@mui/icons-material/Archive';
import DeleteIcon from '@mui/icons-material/Delete';
import { IconButton } from '@mui/material';
import { SelectChangeEvent } from '@mui/material/Select';

interface Member {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  created_at: string;
}

interface Meeting {
  id: string;
  date: string;
  meeting_type: string;
  attendees: string[];
  topics_discussed: string;
  agenda_link: string | null;
  minutes_link: string | null;
  committee_id: string;
  guests: string[];
  created_at: string;
}

interface Committee {
  id: string;
  committee_name: string;
  description?: string;
  created_at: string;
}

interface Membership {
  member_id: string;
  committee_id: string;
  role: string;
  committee_member_since: string;
  member_until?: string;
  reason_for_leaving?: string;
  status?: 'active' | 'former';
  created_at: string;
  members: Member;
}

function AddMembershipForm({ onClose, onSubmit, existingMembership }: {
  onClose: () => void;
  onSubmit: () => void;
  existingMembership?: Membership | null;
}) {
  const [committees, setCommittees] = useState<Committee[]>([]);
  const [existingMembers, setExistingMembers] = useState<Member[]>([]);
  const [selectedMember, setSelectedMember] = useState<Member | null>(null);
  const [formData, setFormData] = useState({
    committee_id: existingMembership?.committee_id || '',
    committee_member_since: existingMembership?.committee_member_since || new Date().toISOString().split('T')[0],
    member_until: existingMembership?.member_until || '',
    reason_for_leaving: existingMembership?.reason_for_leaving || '',
    status: existingMembership?.status || 'active'
  });
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      // Fetch committees
      const { data: committeesData } = await supabase
        .from('committees')
        .select('*')
        .order('committee_name');
      setCommittees(committeesData || []);

      // Fetch existing members
      const { data: membersData } = await supabase
        .from('members')
        .select('*')
        .order('name');
      setExistingMembers(membersData || []);

      if (existingMembership) {
        const member = membersData?.find(m => m.id === existingMembership.member_id);
        if (member) {
          setSelectedMember(member);
        }
      }
    };
    fetchData();
  }, [existingMembership]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!selectedMember) {
      setError('Please select a member');
      return;
    }

    if (!formData.committee_id) {
      setError('Please select a committee');
      return;
    }

    try {
      const { error: membershipError } = await supabase
        .from('committee_memberships')
        .upsert({
          member_id: selectedMember.id,
          committee_id: formData.committee_id,
          committee_member_since: formData.committee_member_since,
          member_until: formData.member_until || null,
          reason_for_leaving: formData.reason_for_leaving || null,
          status: formData.status
        });

      if (membershipError) throw membershipError;

      onSubmit();
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save membership');
    }
  };

  return (
    <Dialog open={true} onClose={onClose} fullWidth maxWidth="md">
      <Box component="form" onSubmit={handleSubmit} sx={{ p: 2 }}>
        <DialogTitle>
          {existingMembership ? 'Edit Membership' : 'Add Committee Membership'}
        </DialogTitle>
        <DialogContent>
          {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

          <Autocomplete
            value={selectedMember}
            onChange={(_, newValue) => setSelectedMember(newValue)}
            options={existingMembers}
            getOptionLabel={(option) => option.name}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Select Member"
                required
                margin="normal"
              />
            )}
          />

          <Select
            fullWidth
            value={formData.committee_id}
            onChange={(e) => setFormData({ ...formData, committee_id: e.target.value })}
            label="Committee"
            required
            sx={{ mt: 2 }}
          >
            {committees.map((committee) => (
              <MenuItem key={committee.id} value={committee.id}>
                {committee.committee_name}
              </MenuItem>
            ))}
          </Select>

          <TextField
            fullWidth
            type="date"
            label="Member Since"
            value={formData.committee_member_since}
            onChange={(e) => setFormData({ ...formData, committee_member_since: e.target.value })}
            InputLabelProps={{ shrink: true }}
            margin="normal"
            required
          />

          <Select
            fullWidth
            value={formData.status}
            onChange={(e) => setFormData({ ...formData, status: e.target.value })}
            label="Status"
            sx={{ mt: 2 }}
          >
            <MenuItem value="active">Active</MenuItem>
            <MenuItem value="former">Former</MenuItem>
          </Select>

          {formData.status === 'former' && (
            <>
              <TextField
                fullWidth
                type="date"
                label="Member Until"
                value={formData.member_until}
                onChange={(e) => setFormData({ ...formData, member_until: e.target.value })}
                InputLabelProps={{ shrink: true }}
                margin="normal"
                required
              />

              <TextField
                fullWidth
                label="Reason for Leaving"
                value={formData.reason_for_leaving}
                onChange={(e) => setFormData({ ...formData, reason_for_leaving: e.target.value })}
                margin="normal"
                multiline
                rows={3}
                required
              />
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Cancel</Button>
          <Button type="submit" variant="contained">
            {existingMembership ? 'Update Membership' : 'Add Membership'}
          </Button>
        </DialogActions>
      </Box>
    </Dialog>
  );
}

function Meetings() {
  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [committees, setCommittees] = useState<Committee[]>([]);
  const [attendeeNames, setAttendeeNames] = useState<{[key: string]: string}>({});
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [editingMeeting, setEditingMeeting] = useState<Meeting | null>(null);
  const [committeeNames, setCommitteeNames] = useState<{[key: string]: string}>({});
  const [nextMeetingDates, setNextMeetingDates] = useState<{[key: string]: string}>({});
  const [remainingDays, setRemainingDays] = useState<{[key: string]: number}>({});

  const fetchMeetings = async () => {
    try {
      // Fetch committees first
      const { data: committeesData } = await supabase
        .from('committees')
        .select('*')
        .order('committee_name');

      if (committeesData) {
        setCommittees(committeesData);
        // Create committee names map here since we already have the data
        const namesMap = committeesData.reduce((acc, committee) => ({
          ...acc,
          [committee.id]: committee.committee_name
        }), {});
        setCommitteeNames(namesMap);
      }

      const { data: meetingsData } = await supabase
        .from('meetings')
        .select('*')
        .order('date', { ascending: false });

      if (!meetingsData) return;

      // Ensure guests and attendees are always arrays
      const formattedMeetings = meetingsData.map(meeting => ({
        ...meeting,
        guests: Array.isArray(meeting.guests) ? meeting.guests : [],
        attendees: Array.isArray(meeting.attendees) ? meeting.attendees : []
      }));

      setMeetings(formattedMeetings);

      // Calculate next meeting dates and remaining days
      const nextMeetingMap: { [key: string]: string } = {};
      const remainingDaysMap: { [key: string]: number } = {};
      
      formattedMeetings.forEach(meeting => {
        const committeeId = meeting.committee_id;
        const meetingDate = new Date(meeting.date);
        const nextMeetingDate = new Date(meetingDate);
        nextMeetingDate.setMonth(meetingDate.getMonth() + 3); // Assuming next meeting is 3 months later

        if (!nextMeetingMap[committeeId] || new Date(nextMeetingMap[committeeId]) < nextMeetingDate) {
          nextMeetingMap[committeeId] = nextMeetingDate.toISOString().split('T')[0];
          
          // Calculate remaining days
          const today = new Date();
          const daysRemaining = Math.ceil((nextMeetingDate.getTime() - today.getTime()) / (1000 * 3600 * 24));
          remainingDaysMap[committeeId] = daysRemaining;
        }
      });

      setNextMeetingDates(nextMeetingMap);
      setRemainingDays(remainingDaysMap);

      // Fetch member names for attendees
      const { data: membersData } = await supabase
        .from('members')
        .select('id, name');

      if (membersData) {
        const namesMap = membersData.reduce((acc, member) => ({
          ...acc,
          [member.id]: member.name
        }), {});
        setAttendeeNames(namesMap);
      }
    } catch (err) {
      console.error('Error fetching meetings:', err);
    }
  };

  const handleDeleteMeeting = async (meetingId: string) => {
    if (!window.confirm('Are you sure you want to delete this meeting?')) return;

    try {
      const { error } = await supabase
        .from('meetings')
        .delete()
        .eq('id', meetingId);

      if (error) throw error;

      fetchMeetings();
    } catch (err) {
      console.error('Error deleting meeting:', err);
      alert('Failed to delete meeting');
    }
  };

  useEffect(() => {
    fetchMeetings();
  }, []);

  // Group meetings by committee
  const meetingsByCommittee = meetings.reduce((acc, meeting) => {
    if (!acc[meeting.committee_id]) {
      acc[meeting.committee_id] = [];
    }
    acc[meeting.committee_id].push(meeting);
    return acc;
  }, {} as Record<string, Meeting[]>);

  return (
    <Box>
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between' }}>
        <Typography variant="h6">Meetings</Typography>
        <Button 
          variant="contained" 
          onClick={() => setOpenAddDialog(true)}
          sx={{ mb: 2 }}
        >
          Add New Meeting
        </Button>
      </Box>

      {committees.map(committee => (
        <Box key={committee.id} sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            {committee.committee_name}
          </Typography>
          
          <TableContainer component={Paper}>
            <Table size="small"> {/* This makes the table rows more compact */}
              <TableHead>
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold', py: 1 }}>Date</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', py: 1 }}>Type</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', py: 1 }}>Topics Discussed</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', py: 1 }}>Agenda</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', py: 1 }}>Minutes</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', py: 1 }}>Attended Members</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', py: 1 }}>Guests</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', py: 1 }}>Next Meeting</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', py: 1 }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {meetingsByCommittee[committee.id]
                  ?.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()) // Sort by date, most recent first
                  .map((meeting, index) => {
                    const isLatestMeeting = index === 0;
                    const meetingDate = new Date(meeting.date);
                    
                    // For non-latest meetings, calculate days until next meeting
                    const nextMeeting = meetingsByCommittee[committee.id]?.[index - 1];
                    const daysUntilNext = nextMeeting 
                      ? Math.ceil((new Date(nextMeeting.date).getTime() - meetingDate.getTime()) / (1000 * 3600 * 24))
                      : null;

                    return (
                      <TableRow key={meeting.id}>
                        <TableCell sx={{ py: 1 }}>
                          {new Date(meeting.date).toLocaleDateString('en-GB')}
                        </TableCell>
                        <TableCell sx={{ py: 1 }}>{meeting.meeting_type}</TableCell>
                        <TableCell sx={{ py: 1 }}>{meeting.topics_discussed}</TableCell>
                        <TableCell sx={{ py: 1 }}>
                          {meeting.agenda_link && (
                            <a href={meeting.agenda_link} target="_blank" rel="noopener noreferrer">
                              View Agenda
                            </a>
                          )}
                        </TableCell>
                        <TableCell sx={{ py: 1 }}>
                          {meeting.minutes_link && (
                            <a href={meeting.minutes_link} target="_blank" rel="noopener noreferrer">
                              View Minutes
                            </a>
                          )}
                        </TableCell>
                        <TableCell sx={{ py: 1 }}>
                          <Box>
                            {meeting.attendees?.length > 0 ? (
                              meeting.attendees.map((id, index) => (
                                <Chip
                                  key={`${meeting.id}-attendee-${id}-${index}`}
                                  label={attendeeNames[id] || 'Unknown Member'}
                                  sx={{ margin: '2px' }}
                                  size="small" // Make chips smaller
                                />
                              ))
                            ) : (
                              '-'
                            )}
                          </Box>
                        </TableCell>
                        <TableCell sx={{ py: 1 }}>
                          <Box>
                            {Array.isArray(meeting.guests) && meeting.guests.length > 0 ? (
                              <Typography variant="body2">
                                {meeting.guests.join(', ')}
                              </Typography>
                            ) : (
                              '-'
                            )}
                          </Box>
                        </TableCell>
                        <TableCell sx={{ py: 1 }}>
                          {isLatestMeeting ? (
                            // Show remaining days for the latest meeting
                            <Box
                              sx={{
                                display: 'inline-block',
                                padding: '4px 8px',
                                borderRadius: '4px',
                                backgroundColor: (() => {
                                  const daysRemaining = remainingDays[meeting.committee_id];
                                  if (daysRemaining < 0) return '#f44336';
                                  if (daysRemaining <= 7) return '#ff9800';
                                  if (daysRemaining <= 30) return '#ffeb3b';
                                  return '#4caf50';
                                })(),
                                color: '#fff',
                                fontWeight: 'bold',
                                fontSize: '0.875rem'
                              }}
                            >
                              {remainingDays[meeting.committee_id] !== undefined
                                ? remainingDays[meeting.committee_id] > 0
                                  ? `${remainingDays[meeting.committee_id]} days`
                                  : `${-remainingDays[meeting.committee_id]} days overdue`
                                : 'N/A'}
                            </Box>
                          ) : (
                            // Show next meeting date and color code based on 3-month deadline
                            <Box
                              sx={{
                                display: 'inline-block',
                                padding: '4px 8px',
                                borderRadius: '4px',
                                backgroundColor: (() => {
                                  if (!daysUntilNext) return '#9e9e9e'; // No next meeting
                                  if (daysUntilNext > 105) return '#f44336'; // More than 15 days after deadline (red)
                                  if (daysUntilNext > 90) return '#ffeb3b'; // Within 15 days after deadline (yellow)
                                  return '#4caf50'; // Within 3 months deadline (green)
                                })(),
                                color: '#fff',
                                fontWeight: 'bold',
                                fontSize: '0.875rem'
                              }}
                            >
                              {nextMeeting 
                                ? `${new Date(nextMeeting.date).toLocaleDateString('en-GB')} (${
                                    daysUntilNext <= 90 
                                      ? `${90 - daysUntilNext} days before deadline`
                                      : `${daysUntilNext - 90} days after deadline`
                                  })`
                                : 'No following meeting'}
                            </Box>
                          )}
                        </TableCell>
                        <TableCell sx={{ py: 1 }}>
                          <IconButton 
                            size="small" // Make buttons smaller
                            onClick={() => setEditingMeeting(meeting)}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                          <IconButton 
                            size="small"
                            onClick={() => handleDeleteMeeting(meeting.id)}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    );
                  })}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      ))}

      {/* Add Meeting Dialog */}
      {(openAddDialog || editingMeeting) && (
        <AddMeetingForm
          onClose={() => {
            setOpenAddDialog(false);
            setEditingMeeting(null);
          }}
          onSubmit={() => {
            fetchMeetings();
            setOpenAddDialog(false);
            setEditingMeeting(null);
          }}
          existingMeeting={editingMeeting}
        />
      )}
    </Box>
  );
}

function AddMeetingForm({ onClose, onSubmit, existingMeeting }: {
  onClose: () => void;
  onSubmit: () => void;
  existingMeeting?: Meeting | null;
}) {
  const [committees, setCommittees] = useState<Committee[]>([]);
  const [members, setMembers] = useState<Member[]>([]);
  const [selectedAttendees, setSelectedAttendees] = useState<string[]>(existingMeeting?.attendees || []);
  
  const [formData, setFormData] = useState({
    committee_id: existingMeeting?.committee_id || '',
    date: existingMeeting?.date.split('T')[0] || new Date().toISOString().split('T')[0],
    meeting_type: existingMeeting?.meeting_type || 'In-Person',
    topics_discussed: existingMeeting?.topics_discussed || '',
    agenda_link: existingMeeting?.agenda_link || '',
    minutes_link: existingMeeting?.minutes_link || '',
    guests: existingMeeting?.guests ? existingMeeting.guests.join(', ') : ''
  });

  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      // Fetch committees
      const { data: committeesData } = await supabase
        .from('committees')
        .select('*')
        .order('committee_name');
      setCommittees(committeesData || []);

      // Fetch active members if committee is selected
      if (formData.committee_id) {
        const { data: membersData } = await supabase
          .from('committee_memberships')
          .select(`
            members (
              id,
              name
            )
          `)
          .eq('committee_id', formData.committee_id)
          .eq('status', 'active');

        const activeMembers = membersData?.map(m => ({
          id: m.members.id,
          name: m.members.name
        })) || [];

        setMembers(activeMembers);
      }
    };
    fetchData();
  }, [formData.committee_id]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    try {
      const meetingData = {
        ...formData,
        attendees: selectedAttendees,
        guests: formData.guests.split(',').map(g => g.trim()).filter(Boolean)
      };

      const { error: supabaseError } = await supabase
        .from('meetings')
        .upsert({
          ...(existingMeeting?.id ? { id: existingMeeting.id } : {}),
          ...meetingData
        });

      if (supabaseError) throw supabaseError;

      onSubmit();
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save meeting');
    }
  };

  return (
    <Dialog open={true} onClose={onClose} maxWidth="md" fullWidth>
      <Box component="form" onSubmit={handleSubmit} sx={{ p: 2 }}>
        <DialogTitle>
          {existingMeeting ? 'Edit Meeting' : 'Add New Meeting'}
        </DialogTitle>
        <DialogContent>
          {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

          <Select
            fullWidth
            value={formData.committee_id}
            onChange={(e) => setFormData({ ...formData, committee_id: e.target.value })}
            label="Committee"
            required
            sx={{ mt: 2 }}
          >
            {committees.map((committee) => (
              <MenuItem key={committee.id} value={committee.id}>
                {committee.committee_name}
              </MenuItem>
            ))}
          </Select>

          <TextField
            fullWidth
            type="date"
            label="Date"
            value={formData.date}
            onChange={(e) => setFormData({ ...formData, date: e.target.value })}
            InputLabelProps={{ shrink: true }}
            margin="normal"
            required
          />

          <Select
            fullWidth
            value={formData.meeting_type}
            onChange={(e) => setFormData({ ...formData, meeting_type: e.target.value })}
            label="Meeting Type"
            sx={{ mt: 2 }}
          >
            <MenuItem value="In-Person">In-Person</MenuItem>
            <MenuItem value="Remote">Remote</MenuItem>
            <MenuItem value="Written Consent">Written Consent</MenuItem>
          </Select>

          <Autocomplete
            multiple
            value={members.filter(member => selectedAttendees.includes(member.id))}
            onChange={(_, newValue) => {
              setSelectedAttendees(newValue.map(v => v.id));
            }}
            options={members}
            getOptionLabel={(option) => option.name}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Attendees"
                margin="normal"
              />
            )}
          />

          <TextField
            fullWidth
            label="Guests (comma-separated)"
            value={formData.guests}
            onChange={(e) => setFormData({ ...formData, guests: e.target.value })}
            margin="normal"
          />

          <TextField
            fullWidth
            label="Topics Discussed"
            value={formData.topics_discussed}
            onChange={(e) => setFormData({ ...formData, topics_discussed: e.target.value })}
            margin="normal"
            multiline
            rows={3}
            required
          />

          <TextField
            fullWidth
            label="Agenda Link"
            value={formData.agenda_link}
            onChange={(e) => setFormData({ ...formData, agenda_link: e.target.value })}
            margin="normal"
          />

          <TextField
            fullWidth
            label="Minutes Link"
            value={formData.minutes_link}
            onChange={(e) => setFormData({ ...formData, minutes_link: e.target.value })}
            margin="normal"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Cancel</Button>
          <Button type="submit" variant="contained">
            {existingMeeting ? 'Update Meeting' : 'Add Meeting'}
          </Button>
        </DialogActions>
      </Box>
    </Dialog>
  );
}

function AddCommitteeForm({ onClose, onSubmit }: {
  onClose: () => void;
  onSubmit: () => void;
}) {
  const [formData, setFormData] = useState({
    committee_name: '',
    description: ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const { error } = await supabase
        .from('committees')
        .insert([{
          committee_name: formData.committee_name,
          description: formData.description,
          created_at: new Date().toISOString()
        }]);
      
      if (error) throw error;
      onSubmit();
      onClose();
    } catch (err) {
      console.error('Committee creation error:', err);
    }
  };

  return (
    <Box component="form" onSubmit={handleSubmit} sx={{ p: 2 }}>
      <DialogTitle>Create New Committee</DialogTitle>
      <DialogContent>
        <TextField
          fullWidth
          label="Committee Name"
          value={formData.committee_name}
          onChange={(e) => setFormData({ ...formData, committee_name: e.target.value })}
          margin="normal"
          required
        />

        <TextField
          fullWidth
          multiline
          rows={4}
          label="Purpose"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          margin="normal"
          required
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button type="submit" variant="contained">Create Committee</Button>
      </DialogActions>
    </Box>
  );
}

function Committees() {
  const [tabValue, setTabValue] = useState(0);
  const [committees, setCommittees] = useState<Committee[]>([]);
  const [memberships, setMemberships] = useState<Membership[]>([]);
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [editingMembership, setEditingMembership] = useState<Membership | null>(null);
  const [openAddCommitteeDialog, setOpenAddCommitteeDialog] = useState(false);

  useEffect(() => {
    fetchCommitteesAndMemberships();
  }, []);

  const fetchCommitteesAndMemberships = async () => {
    // Fetch committees
    const { data: committeesData, error: committeesError } = await supabase
      .from('committees')
      .select('*')
      .order('committee_name');

    if (committeesError) {
      console.error('Error fetching committees:', committeesError);
      return;
    }
    setCommittees(committeesData || []);

    // Fetch memberships with member details - modified query
    const { data: membershipsData, error: membershipsError } = await supabase
      .from('committee_memberships')
      .select(`
        member_id,
        committee_id,
        role,
        committee_member_since,
        member_until,
        reason_for_leaving,
        status,
        created_at,
        members (
          id,
          name,
          email,
          phone
        )
      `)
      .order('committee_member_since', { ascending: false });

    if (membershipsError) {
      console.error('Error fetching memberships:', membershipsError);
      return;
    }

    console.log('Fetched memberships:', membershipsData); // Add this for debugging
    setMemberships(membershipsData || []);
  };

  // Group memberships by committee
  const membershipsByCommittee = committees.reduce((acc, committee) => {
    acc[committee.id] = memberships.filter(
      membership => membership.committee_id === committee.id
    );
    return acc;
  }, {} as Record<string, Membership[]>);

  const handleDeleteMembership = async (membership: Membership) => {
    if (!window.confirm('Are you sure you want to remove this membership?')) return;

    try {
      const { error } = await supabase
        .from('committee_memberships')
        .delete()
        .eq('member_id', membership.member_id)
        .eq('committee_id', membership.committee_id);

      if (error) throw error;
      fetchCommitteesAndMemberships();
    } catch (err) {
      console.error('Error deleting membership:', err);
      alert('Failed to delete membership');
    }
  };

  return (
    <Box sx={{ mt: 1 }}>
      <Typography variant="h4" gutterBottom sx={{ mb: 1 }}>
        Committee Management
      </Typography>
      
      <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>
        <Tab label="Committees" />
        <Tab label="Meetings" />
      </Tabs>

      <Box sx={{ mt: 1 }}>
        {tabValue === 0 && (
          <Box>
            <Box sx={{ mb: 1, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
              <Button variant="contained" onClick={() => setOpenAddCommitteeDialog(true)}>
                Add New Committee
              </Button>
              <Button variant="contained" onClick={() => setOpenAddDialog(true)}>
                Add New Committee Membership
              </Button>
            </Box>

            {committees.map(committee => (
              <Box key={committee.id} sx={{ mb: 2 }}>
                <Typography variant="h6" gutterBottom>
                  {committee.committee_name}
                </Typography>
                
                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell sx={{ fontWeight: 'bold' }}>Employee</TableCell>
                        <TableCell sx={{ fontWeight: 'bold' }}>Member Since</TableCell>
                        <TableCell sx={{ fontWeight: 'bold' }}>Status</TableCell>
                        <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {membershipsByCommittee[committee.id]?.filter(m => m.status === 'active').map(membership => (
                        <TableRow key={membership.member_id}>
                          <TableCell>{membership.members.name}</TableCell>
                          <TableCell>
                            {new Date(membership.committee_member_since).toLocaleDateString('en-GB')}
                          </TableCell>
                          <TableCell>{membership.status || 'active'}</TableCell>
                          <TableCell>
                            <IconButton onClick={() => setEditingMembership(membership)}>
                              <EditIcon />
                            </IconButton>
                            <IconButton onClick={() => handleDeleteMembership(membership)}>
                              <DeleteIcon />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))}
                      
                      {membershipsByCommittee[committee.id]?.filter(m => m.status === 'former').map(membership => (
                        <TableRow key={membership.member_id} sx={{ bgcolor: '#f5f5f5' }}>
                          <TableCell sx={{ color: '#9e9e9e' }}>{membership.members.name}</TableCell>
                          <TableCell sx={{ color: '#9e9e9e' }}>
                            {new Date(membership.committee_member_since).toLocaleDateString('en-GB')}
                          </TableCell>
                          <TableCell sx={{ color: '#9e9e9e' }}>{membership.status}</TableCell>
                          <TableCell>
                            <IconButton onClick={() => setEditingMembership(membership)}>
                              <EditIcon />
                            </IconButton>
                            <IconButton onClick={() => handleDeleteMembership(membership)}>
                              <DeleteIcon />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            ))}
          </Box>
        )}
        {tabValue === 1 && <Meetings />}
      </Box>

      {/* Add/Edit Membership Dialog */}
      {(openAddDialog || editingMembership) && (
        <AddMembershipForm
          onClose={() => {
            setOpenAddDialog(false);
            setEditingMembership(null);
          }}
          onSubmit={() => {
            fetchCommitteesAndMemberships();
            setOpenAddDialog(false);
            setEditingMembership(null);
          }}
          existingMembership={editingMembership}
        />
      )}

      {openAddCommitteeDialog && (
        <Dialog open={true} onClose={() => setOpenAddCommitteeDialog(false)}>
          <AddCommitteeForm
            onClose={() => setOpenAddCommitteeDialog(false)}
            onSubmit={() => {
              fetchCommitteesAndMemberships();
              setOpenAddCommitteeDialog(false);
            }}
          />
        </Dialog>
      )}
    </Box>
  );
}

export default Committees; 