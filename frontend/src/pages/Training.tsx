import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Tabs,
  Tab,
  IconButton,
  Collapse,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Checkbox,
  FormControlLabel,
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import { supabase } from '../utils/supabase/client';
import UploadIcon from '@mui/icons-material/Upload';

interface Employee {
  id: string;
  name: string;
  department_id: string;
  department_name?: string;
}

interface Training {
  id: string;
  name: string;
  description: string;
  requires_refresh: boolean;
  refresh_frequency_months: number;
  is_mandatory: boolean;
  assigned_departments: string[];
  quiz_template?: string;
  training_material?: string;
  relevant_policy?: string;
}

interface EmployeeTraining {
  id: string;
  employee_id: string;
  training_id: string;
  last_completed: string;
  next_due: string;
  training_name?: string;
  quiz_file?: string;
  record_file?: string;
}

function getStatusColor(daysUntilDue: number): string {
  if (daysUntilDue < 0) return '#000000'; // Overdue - Black
  if (daysUntilDue <= 7) return '#f44336'; // Less than 7 days - Red
  if (daysUntilDue <= 30) return '#ff9800'; // Less than 30 days - Orange
  return '#4caf50'; // More than 30 days - Green
}

function EmployeeTrainingRow({ employee, trainings, onUpdate }: { 
  employee: Employee; 
  trainings: EmployeeTraining[];
  onUpdate?: () => void;
}) {
  const [open, setOpen] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  
  const totalTrainings = trainings.length;
  const completedTrainings = trainings.filter(t => 
    new Date(t.last_completed) > new Date(new Date().setFullYear(new Date().getFullYear() - 1))
  ).length;

  return (
    <>
      <TableRow>
        <TableCell>
          <IconButton size="small" onClick={() => setOpen(!open)}>
            {open ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
          </IconButton>
        </TableCell>
        <TableCell>{employee.name}</TableCell>
        <TableCell>{employee.department_name}</TableCell>
        <TableCell>
          <Chip 
            label={`${completedTrainings} out of ${totalTrainings} complete`}
            color={completedTrainings === totalTrainings ? "success" : "warning"}
          />
        </TableCell>
        <TableCell>
          <IconButton onClick={() => setOpenEditDialog(true)}>
            <EditIcon />
          </IconButton>
        </TableCell>
      </TableRow>
      <TableRow>
        <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={5}>
          <Collapse in={open} timeout="auto" unmountOnExit>
            <Box sx={{ margin: 1 }}>
              <Typography variant="h6" gutterBottom component="div">
                Training Details
              </Typography>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Training Name</TableCell>
                    <TableCell>Last Completed</TableCell>
                    <TableCell>Next Due</TableCell>
                    <TableCell>Status</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {trainings.map((training) => {
                    const daysUntilDue = Math.ceil(
                      (new Date(training.next_due).getTime() - new Date().getTime()) / 
                      (1000 * 60 * 60 * 24)
                    );
                    
                    return (
                      <TableRow key={training.id}>
                        <TableCell>{training.training_name}</TableCell>
                        <TableCell>
                          {new Date(training.last_completed).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          <Box sx={{
                            backgroundColor: getStatusColor(daysUntilDue),
                            color: daysUntilDue < 0 ? '#ffffff' : '#000000',
                            padding: '4px 8px',
                            borderRadius: '4px',
                            display: 'inline-block'
                          }}>
                            {new Date(training.next_due).toLocaleDateString()}
                          </Box>
                        </TableCell>
                        <TableCell>
                          {daysUntilDue < 0 
                            ? `${Math.abs(daysUntilDue)} days overdue`
                            : `${daysUntilDue} days remaining`}
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </Box>
          </Collapse>
        </TableCell>
      </TableRow>

      {openEditDialog && (
        <EditTrainingDialog
          employee={employee}
          trainings={trainings}
          onClose={() => setOpenEditDialog(false)}
          onSave={() => {
            setOpenEditDialog(false);
            if (onUpdate) onUpdate();
          }}
        />
      )}
    </>
  );
}

function EditTrainingDialog({ employee, trainings, onClose, onSave }: {
  employee: Employee;
  trainings: EmployeeTraining[];
  onClose: () => void;
  onSave: () => void;
}) {
  const [editedTrainings, setEditedTrainings] = useState(trainings);

  const calculateNextDueDate = (lastCompleted: string, frequencyMonths: number): string => {
    try {
      if (!lastCompleted) return '';
      
      // Parse the date string
      const [year, month, day] = lastCompleted.split('-').map(Number);
      const date = new Date(year, month - 1, day);
      
      // Add months
      date.setMonth(date.getMonth() + frequencyMonths);
      
      // Format back to YYYY-MM-DD
      return date.toISOString().split('T')[0];
    } catch (error) {
      console.error('Error calculating next due date:', error);
      return '';
    }
  };

  const handleDateChange = (index: number, newDate: string) => {
    try {
      const training = editedTrainings[index];
      const frequencyMonths = training?.refresh_frequency_months || 12;
      
      if (!newDate) return;
      
      const newTrainings = [...editedTrainings];
      const nextDue = calculateNextDueDate(newDate, frequencyMonths);
      
      if (!nextDue) {
        console.error('Failed to calculate next due date');
        return;
      }

      newTrainings[index] = {
        ...training,
        last_completed: newDate,
        next_due: nextDue
      };
      setEditedTrainings(newTrainings);
    } catch (error) {
      console.error('Error handling date change:', error);
    }
  };

  const handleSave = async () => {
    try {
      for (const training of editedTrainings) {
        if (!training.last_completed) continue;

        // Only send the last_completed date, next_due will be calculated by trigger
        const lastCompleted = training.last_completed.split('T')[0];

        // Validate date format
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(lastCompleted)) {
          throw new Error('Invalid date format. Expected YYYY-MM-DD');
        }

        const updateData = {
          employee_id: employee.id,
          training_id: training.training_id,
          last_completed: lastCompleted
          // next_due is removed as it will be calculated by the trigger
        };

        console.log('Attempting to save:', updateData);

        try {
          if (training.id.startsWith('pending-')) {
            const { data, error } = await supabase
              .from('employee_trainings')
              .insert([updateData])
              .select('*')
              .single();
            
            if (error) {
              console.error('Insert error:', error);
              throw error;
            }
            console.log('Successfully inserted:', data);
          } else {
            const { data, error } = await supabase
              .from('employee_trainings')
              .update(updateData)
              .eq('id', training.id)
              .select('*')
              .single();
            
            if (error) {
              console.error('Update error:', error);
              throw error;
            }
            console.log('Successfully updated:', data);
          }
        } catch (dbError) {
          console.error('Database operation failed:', dbError);
          throw dbError;
        }
      }
      onSave();
      window.location.reload();
    } catch (err) {
      console.error('Error saving training data:', err);
      alert(`Failed to save training data: ${err instanceof Error ? err.message : JSON.stringify(err)}`);
    }
  };

  return (
    <Dialog open={true} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Edit Training Records - {employee.name}</DialogTitle>
      <DialogContent>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Training</TableCell>
              <TableCell>Last Completed</TableCell>
              <TableCell>Next Due</TableCell>
              <TableCell>Frequency</TableCell>
              <TableCell>Files</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {editedTrainings.map((training, index) => (
              <TableRow key={training.id}>
                <TableCell>{training.training_name}</TableCell>
                <TableCell>
                  <TextField
                    type="date"
                    value={training.last_completed ? training.last_completed.split('T')[0] : ''}
                    onChange={(e) => handleDateChange(index, e.target.value)}
                    InputLabelProps={{
                      shrink: true,
                    }}
                  />
                </TableCell>
                <TableCell>
                  {training.next_due ? new Date(training.next_due).toLocaleDateString() : 'Not set'}
                </TableCell>
                <TableCell>
                  {training?.refresh_frequency_months || 12} months
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Button
                      size="small"
                      variant="outlined"
                      component="label"
                    >
                      Upload Quiz
                      <input
                        type="file"
                        hidden
                        onChange={async (e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            const { data, error } = await supabase.storage
                              .from('training-files')
                              .upload(`quiz/${employee.id}/${training.id}/${file.name}`, file);
                            
                            if (data) {
                              const newTrainings = [...editedTrainings];
                              newTrainings[index] = {
                                ...training,
                                quiz_file: data.path
                              };
                              setEditedTrainings(newTrainings);
                            }
                          }
                        }}
                      />
                    </Button>
                    <Button
                      size="small"
                      variant="outlined"
                      component="label"
                    >
                      Upload Record
                      <input
                        type="file"
                        hidden
                        onChange={async (e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            const { data, error } = await supabase.storage
                              .from('training-files')
                              .upload(`records/${employee.id}/${training.id}/${file.name}`, file);
                            
                            if (data) {
                              const newTrainings = [...editedTrainings];
                              newTrainings[index] = {
                                ...training,
                                record_file: data.path
                              };
                              setEditedTrainings(newTrainings);
                            }
                          }
                        }}
                      />
                    </Button>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={handleSave} variant="contained">Save</Button>
      </DialogActions>
    </Dialog>
  );
}

function AddTrainingTypeDialog({ onClose, onSave }: {
  onClose: () => void;
  onSave: () => void;
}) {
  const [departments, setDepartments] = useState<{ id: string; name: string; }[]>([]);
  const [formData, setFormData] = useState({
    training_name: '',
    description: '',
    refresh_frequency_months: 12,
    assigned_departments: [] as string[],
    requires_refresh: true,
    is_mandatory: true,
    quiz_template: '',
    training_material: '',
    relevant_policy: ''
  });
  const [selectAll, setSelectAll] = useState(false);

  useEffect(() => {
    const fetchDepartments = async () => {
      const { data } = await supabase
        .from('departments')
        .select('id, name')
        .order('name');
      if (data) setDepartments(data);
    };
    fetchDepartments();
  }, []);

  const handleDepartmentChange = (deptId: string) => {
    setFormData(prev => ({
      ...prev,
      assigned_departments: prev.assigned_departments.includes(deptId)
        ? prev.assigned_departments.filter(id => id !== deptId)
        : [...prev.assigned_departments, deptId]
    }));
  };

  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    setFormData(prev => ({
      ...prev,
      assigned_departments: checked ? departments.map(d => d.id) : []
    }));
  };

  const handleSubmit = async () => {
    try {
      const { error } = await supabase
        .from('trainings')
        .insert([formData]);

      if (error) throw error;
      onSave();
    } catch (err) {
      console.error('Error adding training type:', err);
      alert('Failed to add training type');
    }
  };

  return (
    <Dialog open={true} onClose={onClose}>
      <DialogTitle>Add Training Type</DialogTitle>
      <DialogContent>
        <TextField
          fullWidth
          label="Training Name"
          value={formData.training_name}
          onChange={(e) => setFormData({ ...formData, training_name: e.target.value })}
          margin="normal"
        />
        <TextField
          fullWidth
          label="Description"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          margin="normal"
          multiline
          rows={3}
        />
        <TextField
          fullWidth
          type="number"
          label="Refresh Frequency (Months)"
          value={formData.refresh_frequency_months}
          onChange={(e) => setFormData({ ...formData, refresh_frequency_months: parseInt(e.target.value) })}
          margin="normal"
        />
        <Box sx={{ mt: 2 }}>
          <Typography variant="subtitle1" gutterBottom>
            Assigned Departments
          </Typography>
          <FormControl component="fieldset" fullWidth>
            <Box sx={{ mb: 1 }}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={selectAll}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                  />
                }
                label="All Departments"
              />
            </Box>
            <Box sx={{ 
              display: 'grid', 
              gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))',
              gap: 1 
            }}>
              {departments.map((dept) => (
                <FormControlLabel
                  key={dept.id}
                  control={
                    <Checkbox
                      checked={formData.assigned_departments.includes(dept.id)}
                      onChange={() => handleDepartmentChange(dept.id)}
                    />
                  }
                  label={dept.name}
                />
              ))}
            </Box>
          </FormControl>
        </Box>
        <Box sx={{ mt: 2 }}>
          <Typography variant="subtitle1" gutterBottom>
            Training Files
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Button
              variant="outlined"
              component="label"
              startIcon={<UploadIcon />}
            >
              Upload Quiz Template
              <input
                type="file"
                hidden
                onChange={async (e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    const { data, error } = await supabase.storage
                      .from('training-files')
                      .upload(`templates/quiz/${file.name}`, file);
                    
                    if (data) {
                      setFormData(prev => ({
                        ...prev,
                        quiz_template: data.path
                      }));
                    }
                  }
                }}
              />
            </Button>
            <Button
              variant="outlined"
              component="label"
              startIcon={<UploadIcon />}
            >
              Upload Training Material
              <input
                type="file"
                hidden
                onChange={async (e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    const { data, error } = await supabase.storage
                      .from('training-files')
                      .upload(`templates/material/${file.name}`, file);
                    
                    if (data) {
                      setFormData(prev => ({
                        ...prev,
                        training_material: data.path
                      }));
                    }
                  }
                }}
              />
            </Button>
            <TextField
              fullWidth
              label="Relevant Policy Link"
              value={formData.relevant_policy || ''}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                relevant_policy: e.target.value
              }))}
            />
          </Box>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={handleSubmit} variant="contained">Add Training</Button>
      </DialogActions>
    </Dialog>
  );
}

function EditTrainingTypeDialog({ training, onClose, onSave }: {
  training: Training;
  onClose: () => void;
  onSave: () => void;
}) {
  const [departments, setDepartments] = useState<{ id: string; name: string; }[]>([]);
  const [formData, setFormData] = useState({
    name: training.name,
    description: training.description,
    refresh_frequency_months: training.refresh_frequency_months,
    assigned_departments: training.assigned_departments,
    requires_refresh: training.requires_refresh,
    is_mandatory: training.is_mandatory,
    quiz_template: training.quiz_template,
    training_material: training.training_material,
    relevant_policy: training.relevant_policy
  });

  useEffect(() => {
    const fetchDepartments = async () => {
      const { data } = await supabase
        .from('departments')
        .select('id, name');
      if (data) setDepartments(data);
    };
    fetchDepartments();
  }, []);

  const handleSubmit = async () => {
    try {
      const { error } = await supabase
        .from('trainings')
        .update(formData)
        .eq('id', training.id);

      if (error) throw error;
      onSave();
    } catch (err) {
      console.error('Error updating training type:', err);
      alert('Failed to update training type');
    }
  };

  return (
    <Dialog open={true} onClose={onClose}>
      <DialogTitle>Edit Training Type</DialogTitle>
      <DialogContent>
        <TextField
          fullWidth
          label="Training Name"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          margin="normal"
        />
        <TextField
          fullWidth
          label="Description"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          margin="normal"
          multiline
          rows={3}
        />
        <TextField
          fullWidth
          type="number"
          label="Refresh Frequency (Months)"
          value={formData.refresh_frequency_months}
          onChange={(e) => setFormData({ ...formData, refresh_frequency_months: parseInt(e.target.value) })}
          margin="normal"
        />
        <FormControl fullWidth margin="normal">
          <InputLabel>Assigned Departments</InputLabel>
          <Select
            multiple
            value={formData.assigned_departments}
            onChange={(e) => setFormData({ 
              ...formData, 
              assigned_departments: typeof e.target.value === 'string' 
                ? [e.target.value] 
                : e.target.value 
            })}
            renderValue={(selected) => (
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                {selected.map((value) => (
                  <Chip 
                    key={value} 
                    label={departments.find(d => d.id === value)?.name} 
                  />
                ))}
              </Box>
            )}
          >
            {departments.map((dept) => (
              <MenuItem key={dept.id} value={dept.id}>
                {dept.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <Box sx={{ mt: 2 }}>
          <Typography variant="subtitle1" gutterBottom>
            Training Files
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Button
              variant="outlined"
              component="label"
              startIcon={<UploadIcon />}
            >
              Upload Quiz Template
              <input
                type="file"
                hidden
                onChange={async (e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    const { data, error } = await supabase.storage
                      .from('training-files')
                      .upload(`templates/quiz/${file.name}`, file);
                    
                    if (data) {
                      setFormData(prev => ({
                        ...prev,
                        quiz_template: data.path
                      }));
                    }
                  }
                }}
              />
            </Button>
            <Button
              variant="outlined"
              component="label"
              startIcon={<UploadIcon />}
            >
              Upload Training Material
              <input
                type="file"
                hidden
                onChange={async (e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    const { data, error } = await supabase.storage
                      .from('training-files')
                      .upload(`templates/material/${file.name}`, file);
                    
                    if (data) {
                      setFormData(prev => ({
                        ...prev,
                        training_material: data.path
                      }));
                    }
                  }
                }}
              />
            </Button>
            <TextField
              fullWidth
              label="Relevant Policy Link"
              value={formData.relevant_policy || ''}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                relevant_policy: e.target.value
              }))}
            />
          </Box>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={handleSubmit} variant="contained">Save Changes</Button>
      </DialogActions>
    </Dialog>
  );
}

function DepartmentTags({ departments, assignedDeptIds }: { 
  departments: { id: string; name: string; }[];
  assignedDeptIds: string[];
}) {
  const [expanded, setExpanded] = useState(false);
  const displayLimit = 2;

  const sortedDepartments = assignedDeptIds
    .map(id => departments.find(d => d.id === id))
    .filter((d): d is { id: string; name: string; } => d !== undefined)
    .sort((a, b) => a.name.localeCompare(b.name));

  const displayDepartments = expanded 
    ? sortedDepartments 
    : sortedDepartments.slice(0, displayLimit);

  const remainingCount = sortedDepartments.length - displayLimit;

  return (
    <Box>
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, alignItems: 'center' }}>
        {displayDepartments.map((dept) => (
          <Chip
            key={dept.id}
            label={dept.name}
            size="small"
            sx={{
              backgroundColor: '#e3f2fd',
              color: '#1976d2',
              '&:hover': {
                backgroundColor: '#bbdefb'
              }
            }}
          />
        ))}
        {!expanded && remainingCount > 0 && (
          <Chip
            label={`+${remainingCount} more`}
            size="small"
            onClick={() => setExpanded(true)}
            sx={{
              cursor: 'pointer',
              backgroundColor: '#f5f5f5',
              '&:hover': {
                backgroundColor: '#eeeeee'
              }
            }}
          />
        )}
        {expanded && sortedDepartments.length > displayLimit && (
          <Chip
            label="Show less"
            size="small"
            onClick={() => setExpanded(false)}
            sx={{
              cursor: 'pointer',
              backgroundColor: '#f5f5f5',
              '&:hover': {
                backgroundColor: '#eeeeee'
              }
            }}
          />
        )}
      </Box>
    </Box>
  );
}

function getDepartmentNames(deptIds: string[], departments: { id: string; name: string; }[]): string[] {
  return deptIds.map(id => departments.find(d => d.id === id)?.name || 'Unknown').filter(Boolean);
}

export default function Training() {
  const [tabValue, setTabValue] = useState(0);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [employeeTrainings, setEmployeeTrainings] = useState<{[key: string]: EmployeeTraining[]}>({});
  const [trainingTypes, setTrainingTypes] = useState<Training[]>([]);
  const [openAddTrainingType, setOpenAddTrainingType] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editingTrainingType, setEditingTrainingType] = useState<Training | null>(null);
  const [departments, setDepartments] = useState<{ id: string; name: string; }[]>([]);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setIsLoading(true);
    setError(null);
    try {
      // Test Supabase connection first
      const { data: testData, error: testError } = await supabase
        .from('members')
        .select('count')
        .limit(1);

      if (testError) {
        console.error('Connection test error:', testError);
        throw new Error('Failed to connect to database');
      }

      // Fetch employees with their departments
      const { data: employeesData, error: employeesError } = await supabase
        .from('members')
        .select(`
          id,
          name,
          department_id,
          departments (
            id,
            name
          )
        `)
        .eq('status', 'active');

      console.log('Employees data:', employeesData);
      if (employeesError) {
        console.error('Employees error:', employeesError);
        throw employeesError;
      }

      if (employeesData) {
        setEmployees(employeesData.map(emp => ({
          ...emp,
          department_name: emp.departments?.name
        })));
      }

      // Fetch training types
      const { data: trainingTypesData, error: typesError } = await supabase
        .from('trainings')
        .select(`
          id,
          name,
          description,
          requires_refresh,
          refresh_frequency_months,
          is_mandatory,
          assigned_departments
        `);

      console.log('Training types:', trainingTypesData);
      if (typesError) {
        console.error('Training types error:', typesError);
        throw typesError;
      }

      if (trainingTypesData) {
        setTrainingTypes(trainingTypesData);
      }

      if (employeesData && trainingTypesData) {
        // Create a map of required trainings for each department
        const departmentTrainings = trainingTypesData.reduce((acc, training) => {
          training.assigned_departments.forEach(deptId => {
            if (!acc[deptId]) acc[deptId] = [];
            acc[deptId].push(training);
          });
          return acc;
        }, {} as { [key: string]: Training[] });

        // Fetch completed trainings
        const { data: trainingsData, error: trainingsError } = await supabase
          .from('employee_trainings')
          .select(`
            id,
            employee_id,
            training_id,
            last_completed,
            next_due,
            trainings:training_id (
              id,
              name,
              description,
              requires_refresh,
              refresh_frequency_months,
              is_mandatory
            )
          `);

        if (trainingsError) {
          console.error('Trainings error:', trainingsError);
          setEmployeeTrainings({});
        } else {
          // Process trainings for each employee
          const trainingsByEmployee = employeesData.reduce((acc, employee) => {
            // Get all required trainings for this employee's department
            const requiredTrainings = departmentTrainings[employee.department_id] || [];
            
            // Get completed trainings for this employee
            const completedTrainings = trainingsData?.filter(t => t.employee_id === employee.id) || [];
            
            // Create entries for all required trainings
            acc[employee.id] = requiredTrainings.map(required => {
              const completed = completedTrainings.find(t => t.training_id === required.id);
              return completed || {
                id: `pending-${required.id}`,
                employee_id: employee.id,
                training_id: required.id,
                last_completed: '',
                next_due: '',
                trainings: required,
                training_name: required.name
              };
            });
            
            return acc;
          }, {} as {[key: string]: EmployeeTraining[]});

          setEmployeeTrainings(trainingsByEmployee);
        }
      }

      // Fetch departments
      const { data: departmentsData } = await supabase
        .from('departments')
        .select('id, name')
        .order('name');

      if (departmentsData) {
        setDepartments(departmentsData);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      setError(error instanceof Error ? error.message : 'An error occurred while fetching data');
    } finally {
      setIsLoading(false);
    }
  };

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography color="error">Error: {error}</Typography>
        <Button onClick={fetchData} sx={{ mt: 2 }}>
          Retry
        </Button>
      </Box>
    );
  }

  if (isLoading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography>Loading...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%', padding: 2 }}>
      <Typography variant="h4" gutterBottom>Training Management</Typography>
      
      <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)} sx={{ mb: 2 }}>
        <Tab label="Employee Overview" />
        <Tab label="Training Types" />
      </Tabs>

      {tabValue === 0 && (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell width={50} />
                <TableCell>Employee</TableCell>
                <TableCell>Department</TableCell>
                <TableCell>Training Status</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {employees.map((employee) => (
                <EmployeeTrainingRow
                  key={employee.id}
                  employee={employee}
                  trainings={employeeTrainings[employee.id] || []}
                  onUpdate={fetchData}
                />
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {tabValue === 1 && (
        <Box>
          <Box sx={{ mb: 2 }}>
            <Button
              variant="contained"
              onClick={() => setOpenAddTrainingType(true)}
            >
              Add Training Type
            </Button>
          </Box>

          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Training Name</TableCell>
                  <TableCell>Description</TableCell>
                  <TableCell>Frequency (Months)</TableCell>
                  <TableCell>Assigned Departments</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {trainingTypes.map((training) => (
                  <TableRow key={training.id}>
                    <TableCell>{training.name}</TableCell>
                    <TableCell>{training.description}</TableCell>
                    <TableCell>{training.refresh_frequency_months}</TableCell>
                    <TableCell>
                      {training.assigned_departments.map((deptId) => (
                        <Chip 
                          key={deptId} 
                          label={departments.find(d => d.id === deptId)?.name || 'Unknown'} 
                          sx={{ mr: 1 }} 
                        />
                      ))}
                    </TableCell>
                    <TableCell>
                      <IconButton onClick={() => setEditingTrainingType(training)}>
                        <EditIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          {openAddTrainingType && (
            <AddTrainingTypeDialog
              onClose={() => setOpenAddTrainingType(false)}
              onSave={() => {
                fetchData();
                setOpenAddTrainingType(false);
              }}
            />
          )}
        </Box>
      )}

      {editingTrainingType && (
        <EditTrainingTypeDialog
          training={editingTrainingType}
          onClose={() => setEditingTrainingType(null)}
          onSave={() => {
            fetchData();
            setEditingTrainingType(null);
          }}
        />
      )}
    </Box>
  );
} 