import { useState, useEffect, useCallback } from 'react';
import { 
  <PERSON>, 
  Typography, 
  Card, 
  CardContent, 
  Grid, 
  Chip, 
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Avatar,
  Skeleton,
  Stack,
  Divider,
  Alert,
  AlertTitle
} from '@mui/material';
import { 
  Schedule as ScheduleIcon,
  Policy as PolicyIcon,
  Cake as CakeIcon,
  Groups as GroupsIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  AccessTime as AccessTimeIcon
} from '@mui/icons-material';
import { apiService } from '../services/api';
import { getCachedData, setCachedData } from '../utils/cache';

interface Committee {
  id: string;
  committee_name: string;
}

interface Meeting {
  id: string;
  date: string;
  committee_id: string;
}

interface Employee {
  id: string;
  name: string;
  date_of_birth?: string;
  status: string;
}

interface Policy {
  id: string;
  name: string;
  next_review: string;
}

function Dashboard() {
  const [committees, setCommittees] = useState<Committee[]>([]);
  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [nextMeetingDates, setNextMeetingDates] = useState<{ [key: string]: string }>({});
  const [remainingDays, setRemainingDays] = useState<{ [key: string]: number }>({});
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [upcomingBirthdays, setUpcomingBirthdays] = useState<Employee[]>([]);
  const [policies, setPolicies] = useState<Policy[]>([]);

  // Add loading states
  const [isLoading, setIsLoading] = useState({
    committees: true,
    meetings: true,
    employees: true,
    policies: true
  });

  // Add refresh trigger - this will change when we want to force refresh
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const fetchData = useCallback(async () => {
    console.log('🔄 Dashboard: Fetching data...');
    
    // Reset loading states
    setIsLoading({
      committees: true,
      meetings: true,
      employees: true,
      policies: true
    });

    try {
      // Fetch new data using API service
      const [
        committeesData,
        meetingsData,
        employeesData,
        policiesData
      ] = await Promise.all([
        apiService.getCommittees(),
        apiService.getMeetings(),
        apiService.getMembers(), 
        apiService.getPolicies()
      ]);

      console.log('📊 Dashboard: Data fetched successfully');

      // Handle committees
      setCommittees(committeesData || []);
      setIsLoading(prev => ({ ...prev, committees: false }));

      // Handle meetings 
      setMeetings(meetingsData || []);
      console.log('🗓️ Dashboard: Processing meetings data:', meetingsData?.length, 'meetings');
      
      // Calculate when next meeting is DUE based on last meeting + 3 months
      const nextMeetingMap: { [key: string]: string } = {};
      const remainingDaysMap: { [key: string]: number } = {};
      
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Start of day for accurate comparison
      console.log('📅 Dashboard: Today is:', today.toDateString());
      
      // Group meetings by committee
      const meetingsByCommittee: { [key: string]: any[] } = {};
      (meetingsData || []).forEach((meeting: any) => {
        const committeeId = meeting.committee_id;
        if (!meetingsByCommittee[committeeId]) {
          meetingsByCommittee[committeeId] = [];
        }
        meetingsByCommittee[committeeId].push(meeting);
      });
      
      console.log('📊 Dashboard: Meetings grouped by committee:', Object.keys(meetingsByCommittee).length, 'committees');
      
      // For each committee, find when next meeting is DUE
      Object.keys(meetingsByCommittee).forEach(committeeId => {
        const committeeMeetings = meetingsByCommittee[committeeId];
        console.log(`🔍 Committee ${committeeId}: ${committeeMeetings.length} meetings`);
        
        if (committeeMeetings.length > 0) {
          // Find the most recent meeting (last meeting that happened)
          const sortedMeetings = committeeMeetings.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
          const lastMeeting = sortedMeetings[0]; // Most recent meeting
          
          const lastMeetingDate = new Date(lastMeeting.date);
          console.log(`📋 Committee ${committeeId}: Last meeting was ${lastMeetingDate.toDateString()}`);
          
          // Calculate next due date: last meeting + 3 months (90 days)
          const nextDueDate = new Date(lastMeetingDate);
          nextDueDate.setMonth(nextDueDate.getMonth() + 3);
          
          // Store the due date
          nextMeetingMap[committeeId] = nextDueDate.toISOString().split('T')[0];
          
          // Calculate days remaining until due (positive = future, negative = overdue)
          const daysRemaining = Math.ceil((nextDueDate.getTime() - today.getTime()) / (1000 * 3600 * 24));
          remainingDaysMap[committeeId] = daysRemaining;
          
          console.log(`✅ Committee ${committeeId}: Due ${nextDueDate.toDateString()}, ${daysRemaining} days remaining`);
        } else {
          console.log(`❌ Committee ${committeeId}: No meetings found`);
        }
      });
      
      setNextMeetingDates(nextMeetingMap);
      setRemainingDays(remainingDaysMap);
      setIsLoading(prev => ({ ...prev, meetings: false }));

      // Handle employees  
      setEmployees(employeesData || []);
      setIsLoading(prev => ({ ...prev, employees: false }));

      // Handle policies - sort by due date (most urgent first)
      const sortedPolicies = (policiesData || [])
        .filter(policy => policy.next_review) // Only include policies with review dates
        .sort((a, b) => {
          const aDate = new Date(a.next_review).getTime();
          const bDate = new Date(b.next_review).getTime();
          return aDate - bDate; // Ascending order (earliest due dates first)
        })
        .slice(0, 4); // Show top 4 most urgent policies

      setPolicies(sortedPolicies);
      setIsLoading(prev => ({ ...prev, policies: false }));

    } catch (error) {
      console.error('💥 Dashboard: Error fetching data:', error);
      // Reset loading states on error
      setIsLoading({
        committees: false,
        meetings: false,
        employees: false,
        policies: false
      });
    }
  }, []);

  // Effect that runs on mount and whenever refreshTrigger changes
  useEffect(() => {
    fetchData();
  }, [fetchData, refreshTrigger]);

  // Effect to trigger refresh when component becomes visible
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        console.log('🔄 Dashboard: Page became visible, refreshing data');
        setRefreshTrigger(prev => prev + 1);
      }
    };

    const handleFocus = () => {
      console.log('🔄 Dashboard: Window focused, refreshing data');
      setRefreshTrigger(prev => prev + 1);
    };

    // Listen for visibility and focus events
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  }, []);

  useEffect(() => {
    const getUpcomingBirthdays = () => {
      const today = new Date();
      const currentYear = today.getFullYear();

      const birthdays = employees
        .filter(employee => employee.date_of_birth && employee.status === 'active') // Filter for active employees
        .map(employee => {
          const birthDate = new Date(employee.date_of_birth);
          const nextBirthday = new Date(currentYear, birthDate.getMonth(), birthDate.getDate());

          // If the birthday has already passed this year, set it for next year
          if (nextBirthday < today) {
            nextBirthday.setFullYear(currentYear + 1);
          }

          const daysRemaining = Math.ceil((nextBirthday.getTime() - today.getTime()) / (1000 * 3600 * 24));

          return {
            name: employee.name,
            birthday: nextBirthday,
            daysRemaining,
          };
        })
        .sort((a, b) => a.daysRemaining - b.daysRemaining) // Sort by days remaining
        .slice(0, 3); // Get the next 3 birthdays

      setUpcomingBirthdays(birthdays);
    };

    getUpcomingBirthdays();
  }, [employees]);

  // Helper function for color coding and status
  const getStatusInfo = (days: number, type: 'meeting' | 'policy' | 'birthday') => {
    if (type === 'birthday') {
      if (days === 0) return { color: 'error', severity: 'high', label: 'Today!' };
      if (days === 1) return { color: 'error', severity: 'high', label: '1 day' };
      if (days <= 14) return { color: 'warning', severity: 'medium', label: `${days} days` };
      return { color: 'success', severity: 'low', label: `${days} days` };
    } else {
      if (days < 0) return { color: 'error', severity: 'critical', label: `${-days} days overdue` };
      if (days <= 7) return { color: 'error', severity: 'high', label: `${days} days` };
      if (days <= 30) return { color: 'warning', severity: 'medium', label: `${days} days` };
      return { color: 'success', severity: 'low', label: `${days} days` };
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return <WarningIcon sx={{ fontSize: 20, color: '#ef4444' }} />;
      case 'high': return <WarningIcon sx={{ fontSize: 20, color: '#f59e0b' }} />;
      case 'medium': return <AccessTimeIcon sx={{ fontSize: 20, color: '#f59e0b' }} />;
      default: return <CheckCircleIcon sx={{ fontSize: 20, color: '#10b981' }} />;
    }
  };

  return (
    <Box>
      {/* Page Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" sx={{ fontWeight: 700, color: '#1e293b', mb: 1 }}>
          Dashboard
        </Typography>
        <Typography variant="body1" sx={{ color: '#64748b' }}>
          Welcome to your governance management portal. Monitor committees, policies, and team updates.
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Committee Meetings Card */}
        <Grid item xs={12} lg={6}>
          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <CardContent sx={{ flexGrow: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                  <GroupsIcon />
                </Avatar>
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Committee Meetings
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    3-month cycle schedule
                  </Typography>
                </Box>
              </Box>

              {isLoading.committees || isLoading.meetings ? (
                <Stack spacing={2}>
                  {[1, 2, 3].map((i) => (
                    <Skeleton key={i} variant="rectangular" height={60} />
                  ))}
                </Stack>
              ) : (
                <List sx={{ p: 0 }}>
                  {committees.slice(0, 5).map((committee, index) => {
                    const days = remainingDays[committee.id];
                    const statusInfo = days !== undefined ? getStatusInfo(days, 'meeting') : null;
                    
                    return (
                      <Box key={committee.id}>
                        <ListItem sx={{ px: 0 }}>
                          <ListItemIcon sx={{ minWidth: 40 }}>
                            {statusInfo && getSeverityIcon(statusInfo.severity)}
                          </ListItemIcon>
                          <ListItemText
                            primary={
                              <Typography variant="body1" sx={{ fontWeight: 500 }}>
                                {committee.committee_name}
                              </Typography>
                            }
                            secondary={
                              nextMeetingDates[committee.id] 
                                ? `Due: ${new Date(nextMeetingDates[committee.id]).toLocaleDateString('en-GB')}`
                                : 'No meetings recorded'
                            }
                          />
                          {statusInfo && (
                            <Chip
                              label={statusInfo.label}
                              color={statusInfo.color as any}
                              size="small"
                              variant={statusInfo.severity === 'critical' ? 'filled' : 'outlined'}
                            />
                          )}
                        </ListItem>
                        {index < committees.length - 1 && index < 4 && <Divider />}
                      </Box>
                    );
                  })}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Policy Reviews Card */}
        <Grid item xs={12} lg={6}>
          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <CardContent sx={{ flexGrow: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
                  <PolicyIcon />
                </Avatar>
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Policy Reviews
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Most urgent review deadlines
                  </Typography>
                </Box>
              </Box>

              {isLoading.policies ? (
                <Stack spacing={2}>
                  {[1, 2, 3].map((i) => (
                    <Skeleton key={i} variant="rectangular" height={60} />
                  ))}
                </Stack>
              ) : (
                <List sx={{ p: 0 }}>
                  {policies.map((policy, index) => {
                    const reviewDate = new Date(policy.next_review);
                    const today = new Date();
                    const daysRemaining = Math.ceil(
                      (reviewDate.getTime() - today.getTime()) / (1000 * 3600 * 24)
                    );
                    const statusInfo = getStatusInfo(daysRemaining, 'policy');

                    return (
                      <Box key={policy.id}>
                        <ListItem sx={{ px: 0 }}>
                          <ListItemIcon sx={{ minWidth: 40 }}>
                            {getSeverityIcon(statusInfo.severity)}
                          </ListItemIcon>
                          <ListItemText
                            primary={
                              <Typography variant="body1" sx={{ fontWeight: 500 }}>
                                {policy.name}
                              </Typography>
                            }
                            secondary={`Review: ${reviewDate.toLocaleDateString('en-GB')}`}
                          />
                          <Chip
                            label={statusInfo.label}
                            color={statusInfo.color as any}
                            size="small"
                            variant={statusInfo.severity === 'critical' ? 'filled' : 'outlined'}
                          />
                        </ListItem>
                        {index < policies.length - 1 && <Divider />}
                      </Box>
                    );
                  })}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Upcoming Birthdays Card */}
        <Grid item xs={12} lg={6}>
          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <CardContent sx={{ flexGrow: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                  <CakeIcon />
                </Avatar>
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Upcoming Birthdays
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Team celebrations
                  </Typography>
                </Box>
              </Box>

              {isLoading.employees ? (
                <Stack spacing={2}>
                  {[1, 2, 3].map((i) => (
                    <Skeleton key={i} variant="rectangular" height={60} />
                  ))}
                </Stack>
              ) : upcomingBirthdays.length > 0 ? (
                <List sx={{ p: 0 }}>
                  {upcomingBirthdays.map((employee, index) => {
                    const statusInfo = getStatusInfo(employee.daysRemaining, 'birthday');

                    return (
                      <Box key={employee.name}>
                        <ListItem sx={{ px: 0 }}>
                          <ListItemIcon sx={{ minWidth: 40 }}>
                            <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.light' }}>
                              {employee.name.charAt(0)}
                            </Avatar>
                          </ListItemIcon>
                          <ListItemText
                            primary={
                              <Typography variant="body1" sx={{ fontWeight: 500 }}>
                                {employee.name}
                              </Typography>
                            }
                            secondary={employee.birthday.toLocaleDateString('en-GB', { 
                              day: '2-digit',
                              month: 'long' 
                            })}
                          />
                          <Chip
                            label={statusInfo.label}
                            color={statusInfo.color as any}
                            size="small"
                            variant={employee.daysRemaining === 0 ? 'filled' : 'outlined'}
                          />
                        </ListItem>
                        {index < upcomingBirthdays.length - 1 && <Divider />}
                      </Box>
                    );
                  })}
                </List>
              ) : (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <CakeIcon sx={{ fontSize: 48, color: 'grey.300', mb: 2 }} />
                  <Typography variant="body2" color="text.secondary">
                    No upcoming birthdays
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}

export default Dashboard; 