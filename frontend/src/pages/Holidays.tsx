import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Autocomplete,
  TextField,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { supabase } from '../utils/supabase/client';
import { format, addDays, isWeekend } from 'date-fns';

interface Employee {
  id: string;
  name: string;
  yearly_holiday_allowance: number;
}

interface Holiday {
  id: string;
  member_id: string;
  start_date: string;
  end_date: string;
  reason?: string;
}

interface EmployeeHolidays {
  employee: Employee;
  holidays: Holiday[];
  remainingDays: number;
}

function HolidayDetailsModal({ holiday, onClose }: { 
  holiday: Holiday; 
  onClose: () => void;
}) {
  const startDate = new Date(holiday.start_date);
  const endDate = new Date(holiday.end_date);
  const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;

  let returnDate = addDays(endDate, 1);
  while (isWeekend(returnDate)) {
    returnDate = addDays(returnDate, 1);
  }

  return (
    <Dialog open={true} onClose={onClose}>
      <DialogTitle>Holiday Details</DialogTitle>
      <DialogContent>
        <Box sx={{ p: 2 }}>
          <Typography variant="body1" gutterBottom>
            Start Date: {format(startDate, 'dd MMMM yyyy')}
          </Typography>
          <Typography variant="body1" gutterBottom>
            End Date: {format(endDate, 'dd MMMM yyyy')}
          </Typography>
          <Typography variant="body1" gutterBottom>
            Days Taken: {days}
          </Typography>
          <Typography variant="body1" gutterBottom>
            Return Date: {format(returnDate, 'dd MMMM yyyy')}
          </Typography>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Close</Button>
      </DialogActions>
    </Dialog>
  );
}

function AddHolidayForm({ onClose, onSubmit }: {
  onClose: () => void;
  onSubmit: () => void;
}) {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [reason, setReason] = useState('');

  useEffect(() => {
    const fetchEmployees = async () => {
      const { data, error } = await supabase
        .from('members')
        .select('id, name, yearly_holiday_allowance')
        .eq('status', 'active')
        .order('name');

      if (error) {
        console.error('Error fetching employees:', error);
        return;
      }

      setEmployees(data || []);
    };

    fetchEmployees();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!selectedEmployee) {
      setError('Please select an employee');
      return;
    }

    if (!startDate || !endDate) {
      setError('Please select both start and end dates');
      return;
    }

    if (endDate < startDate) {
      setError('End date cannot be before start date');
      return;
    }

    try {
      const { error: supabaseError } = await supabase
        .from('holidays')
        .insert({
          member_id: selectedEmployee.id,
          start_date: startDate.toISOString().split('T')[0],
          end_date: endDate.toISOString().split('T')[0],
          reason: reason,
        });

      if (supabaseError) throw supabaseError;

      onSubmit();
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save holiday');
    }
  };

  return (
    <Dialog open={true} onClose={onClose}>
      <Box component="form" onSubmit={handleSubmit} sx={{ p: 2 }}>
        <DialogTitle>Add Leave</DialogTitle>
        <DialogContent>
          {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

          <Autocomplete
            value={selectedEmployee}
            onChange={(_, newValue) => setSelectedEmployee(newValue)}
            options={employees}
            getOptionLabel={(option) => option.name}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Select Employee"
                required
                margin="normal"
              />
            )}
          />

          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <Box sx={{ mt: 2 }}>
              <DatePicker
                label="Start Date"
                value={startDate}
                onChange={(newValue) => setStartDate(newValue)}
              />
            </Box>
            <Box sx={{ mt: 2 }}>
              <DatePicker
                label="End Date"
                value={endDate}
                onChange={(newValue) => setEndDate(newValue)}
                minDate={startDate || undefined}
              />
            </Box>
          </LocalizationProvider>

          <TextField
            fullWidth
            label="Reason for Leave"
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            margin="normal"
            multiline
            rows={3}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Cancel</Button>
          <Button type="submit" variant="contained">Add Leave</Button>
        </DialogActions>
      </Box>
    </Dialog>
  );
}

export default function Holidays() {
  const [employeeHolidays, setEmployeeHolidays] = useState<EmployeeHolidays[]>([]);
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [selectedHoliday, setSelectedHoliday] = useState<Holiday | null>(null);

  const calculateRemainingDays = (allowance: number, holidays: Holiday[]) => {
    const daysUsed = holidays.reduce((total, holiday) => {
      const start = new Date(holiday.start_date);
      const end = new Date(holiday.end_date);
      const days = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;
      return total + days;
    }, 0);
    return allowance - daysUsed;
  };

  const fetchHolidays = async () => {
    try {
      const { data: employees, error: employeesError } = await supabase
        .from('members')
        .select('id, name, yearly_holiday_allowance')
        .eq('status', 'active')
        .order('name');

      if (employeesError) throw employeesError;

      const { data: holidays, error: holidaysError } = await supabase
        .from('holidays')
        .select('*')
        .order('start_date');

      if (holidaysError) throw holidaysError;

      const combined = employees.map(employee => ({
        employee,
        holidays: holidays.filter(h => h.member_id === employee.id),
        remainingDays: calculateRemainingDays(
          employee.yearly_holiday_allowance,
          holidays.filter(h => h.member_id === employee.id)
        )
      }));

      setEmployeeHolidays(combined);
    } catch (err) {
      console.error('Error fetching holidays:', err);
    }
  };

  useEffect(() => {
    fetchHolidays();
  }, []);

  return (
    <Box sx={{ padding: 2 }}>
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4">Holiday Management</Typography>
        <Button
          variant="contained"
          onClick={() => setOpenAddDialog(true)}
        >
          Add Leave
        </Button>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell sx={{ fontWeight: 'bold' }}>Employee</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Holidays</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>Remaining Days</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {employeeHolidays.map(({ employee, holidays, remainingDays }) => (
              <TableRow key={employee.id}>
                <TableCell>{employee.name}</TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {holidays.map((holiday) => {
                      const start = new Date(holiday.start_date);
                      const end = new Date(holiday.end_date);
                      const days = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;
                      
                      return (
                        <Button
                          key={holiday.id}
                          variant="outlined"
                          size="small"
                          onClick={() => setSelectedHoliday(holiday)}
                          sx={{ mb: 1 }}
                        >
                          {`${days} days taken on ${format(start, 'dd MMMM')}`}
                        </Button>
                      );
                    })}
                  </Box>
                </TableCell>
                <TableCell>{remainingDays}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {openAddDialog && (
        <AddHolidayForm
          onClose={() => setOpenAddDialog(false)}
          onSubmit={() => {
            fetchHolidays();
            setOpenAddDialog(false);
          }}
        />
      )}

      {selectedHoliday && (
        <HolidayDetailsModal
          holiday={selectedHoliday}
          onClose={() => setSelectedHoliday(null)}
        />
      )}
    </Box>
  );
} 