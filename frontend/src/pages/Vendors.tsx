import { useState, useEffect, ChangeEvent } from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Tabs,
  Tab,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Tooltip,
  Alert,
  Grid,
  Collapse,
  FormHelperText,
  FormControlLabel,
  Checkbox,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  CircularProgress,
  Card,
  CardContent,
  CardActions,
  Badge,
  ToggleButton,
  ToggleButtonGroup
} from '@mui/material';
import { apiService } from '../services/api';
import SearchIcon from '@mui/icons-material/Search';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import WarningIcon from '@mui/icons-material/Warning';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import DownloadIcon from '@mui/icons-material/Download';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import VisibilityIcon from '@mui/icons-material/Visibility';
import GridViewIcon from '@mui/icons-material/GridView';
import ErrorIcon from '@mui/icons-material/Error';
import ScheduleIcon from '@mui/icons-material/Schedule';
import InfoIcon from '@mui/icons-material/Info';
import PDFViewer from '../components/PDFViewer';
import EDDSection from '../components/vendors/EDDSection';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { parseISO, format, isWithinInterval, addDays, isAfter } from 'date-fns';

// Define interfaces for vendor data
interface ContractDocument {
  document_id: string;
  doc_type: string;
  document_name: string;
  file_path: string;
  url?: string;
  expiry_date?: string;
  uploaded_at: string;
  notes?: string;
}

interface Vendor {
  id: string;
  company_name: string;
  website?: string;
  vendor_type: string; // Now represents Risk Status: critical, high, moderate, low
  service_type: string; // Updated service types
  lead_manager?: string; // Will be member ID, not name
  countries_of_operation?: string[];
  incorporation_date?: string;
  registration_number?: string;
  edd_required: boolean;
  active: boolean;
  annual_cost?: number; // New field for annual cost
  // Account Manager fields
  account_manager_assigned?: boolean;
  account_manager_name?: string;
  account_manager_email?: string;
  account_manager_phone?: string;
  // Contract fields
  contract_id?: number;
  contract_active?: boolean;
  contract_start_date?: string;
  contract_renewal_date?: string;
  contract_end_date?: string;
  contract_notes?: string;
  contract_continuous?: boolean; // Monthly subscription
  contract_status?: 'active' | 'inactive' | 'expired' | 'expiring_soon' | 'continuous';
  // Review fields
  review_id?: number;
  last_review_date?: string;
  next_review_date?: string;
  review_notes?: string;
  review_completed?: boolean;
  created_at: string;
  updated_at: string;
}

// Member interface for dropdown
interface Member {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  department_name?: string;
}

// Interface for notification
interface Notification {
  id: string;
  type: 'review' | 'renewal';
  vendor_id: string;
  vendor_name: string;
  due_date: string;
  days_remaining: number;
  severity: 'info' | 'warning' | 'error';
  alert_type?: string;
  risk_level?: string;
  contract_active?: boolean;
  days_overdue?: number;
}

// Contract status options
const contractStatusOptions = [
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' },
  { value: 'expired', label: 'Expired' },
  { value: 'expiring_soon', label: 'Expiring Soon' },
  { value: 'continuous', label: 'Monthly Subscription' },
  { value: 'renewal_due', label: 'Renewal Due' }
];

// Function to get color based on contract status
const getContractStatusColor = (status?: string): string => {
  switch (status) {
    case 'active':
      return '#4caf50'; // Green
    case 'continuous':
      return '#2196f3'; // Blue
    case 'expiring_soon':
      return '#ff9800'; // Orange
    case 'expired':
      return '#f44336'; // Red
    case 'inactive':
      return '#9e9e9e'; // Grey
    default:
      return '#757575'; // Dark grey
  }
};

// Function to check if a date is within the notification threshold
const isDateApproaching = (dateStr: string, thresholdDays: number = 30): boolean => {
  if (!dateStr) return false;
  try {
    const targetDate = parseISO(dateStr);
    const today = new Date();
    const thresholdDate = addDays(today, thresholdDays);
    
    return isWithinInterval(targetDate, {
      start: today,
      end: thresholdDate
    });
  } catch (error) {
    console.error('Date parsing error:', error);
    return false;
  }
};

// Function to check if a date is in the past
const isDatePast = (dateStr: string): boolean => {
  if (!dateStr) return false;
  try {
    const targetDate = parseISO(dateStr);
    return isAfter(new Date(), targetDate);
  } catch (error) {
    console.error('Date parsing error:', error);
    return false;
  }
};

// Function to format date for display
const formatDate = (dateStr: string): string => {
  if (!dateStr) return 'Not set';
  try {
    return format(parseISO(dateStr), 'MMM dd, yyyy');
  } catch (error) {
    console.error('Date formatting error:', error);
    return 'Invalid date';
  }
};

// Helper functions for review date filtering
const isWithinMonths = (dateStr: string, months: number): boolean => {
  if (!dateStr) return false;
  try {
    const targetDate = parseISO(dateStr);
    const today = new Date();
    const futureDate = new Date();
    futureDate.setMonth(today.getMonth() + months);
    
    return isWithinInterval(targetDate, {
      start: today,
      end: futureDate
    });
  } catch (error) {
    console.error('Date comparison error:', error);
    return false;
  }
};

const isReviewDateOverdue = (dateStr: string): boolean => {
  if (!dateStr) return false;
  try {
    const reviewDate = parseISO(dateStr);
    return isAfter(new Date(), reviewDate);
  } catch (error) {
    console.error('Date comparison error:', error);
    return false;
  }
};

// Review date filter options
const reviewDateFilterOptions = [
  { value: '', label: 'All' },
  { value: 'no_date', label: 'No Date Available' },
  { value: 'overdue', label: 'Overdue' },
  { value: 'next_month', label: 'Next Month' },
  { value: 'next_3_months', label: 'Next 3 Months' },
  { value: 'next_6_months', label: 'Next 6 Months' }
];

// Risk Status options and formatting
const riskStatusOptions = [
  { value: 'critical', label: 'Critical' },
  { value: 'high', label: 'High' },
  { value: 'moderate', label: 'Moderate' },
  { value: 'low', label: 'Low' }
];

const formatRiskStatus = (status: string): string => {
  const option = riskStatusOptions.find(opt => opt.value === status);
  return option ? option.label : 'Low'; // Default to 'Low' if not found
};

// Risk Status color coding
const getRiskStatusColor = (status: string): string => {
  switch (status) {
    case 'critical':
      return '#f44336'; // Red
    case 'high':
      return '#ff9800'; // Orange
    case 'moderate':
      return '#ff9800'; // Orange
    case 'low':
      return '#4caf50'; // Green
    default:
      return '#4caf50'; // Green (default)
  }
};

// Service Type options and formatting
const serviceTypeOptions = [
  { value: 'it_infrastructure', label: 'IT Infrastructure' },
  { value: 'software_development', label: 'Software Development' },
  { value: 'aml_services', label: 'AML Services' },
  { value: 'marketing_tools', label: 'Marketing Tools' },
  { value: 'cybersecurity', label: 'Cybersecurity' },
  { value: 'communication', label: 'Communication' },
  { value: 'finances', label: 'Finances' }
];

const formatServiceType = (type: string): string => {
  const option = serviceTypeOptions.find(opt => opt.value === type);
  return option ? option.label : 'IT Infrastructure'; // Default to 'IT Infrastructure' if not found
};

// Contract Status formatting
const formatContractStatus = (status?: string): string => {
  switch (status) {
    case 'active':
      return 'Active';
    case 'inactive':
      return 'Inactive';
    case 'expired':
      return 'Expired';
    case 'expiring_soon':
      return 'Expiring Soon';
    case 'renewal_due':
      return 'Renewal Due';
    case 'continuous':
      return 'Monthly Subscription';
    default:
      return 'No Contract';
  }
};

// Helper function to sanitize vendor data
const sanitizeVendorData = (vendor: Vendor): Vendor => {
  const validRiskStatuses = ['critical', 'high', 'moderate', 'low'];
  const validServiceTypes = ['it_infrastructure', 'software_development', 'aml_services', 'marketing_tools', 'cybersecurity', 'communication', 'finances'];
  
  return {
    ...vendor,
    vendor_type: validRiskStatuses.includes(vendor.vendor_type) ? vendor.vendor_type : 'low',
    service_type: validServiceTypes.includes(vendor.service_type) ? vendor.service_type : 'it_infrastructure'
  };
};

// Contract Documents component
function ContractDocuments({ vendorId }: { vendorId: string }) {
  const [documents, setDocuments] = useState<ContractDocument[]>([]);
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [documentName, setDocumentName] = useState('');
  const [documentNotes, setDocumentNotes] = useState('');
  const [pdfViewerOpen, setPdfViewerOpen] = useState(false);
  const [currentPdfUrl, setCurrentPdfUrl] = useState('');
  const [currentPdfName, setCurrentPdfName] = useState('');

  // Fetch documents when component mounts
  useEffect(() => {
    fetchDocuments();
  }, [vendorId]);

  const fetchDocuments = async () => {
    setLoading(true);
    console.log('🔍 Fetching documents for vendor:', vendorId);
    try {
      const docs = await apiService.getVendorDocuments(vendorId);
      console.log('📄 Documents received:', docs);
      setDocuments(docs);
    } catch (error) {
      console.error('❌ Error fetching documents:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type !== 'application/pdf') {
        alert('Only PDF files are allowed');
        return;
      }
      setSelectedFile(file);
      setDocumentName(file.name);
      setUploadDialogOpen(true);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) return;

    setUploading(true);
    console.log('📤 Uploading document for vendor:', vendorId);
    console.log('📁 File details:', {
      name: selectedFile.name,
      size: selectedFile.size,
      type: selectedFile.type,
      documentName,
      documentNotes
    });
    
    try {
      const result = await apiService.uploadContractDocument(
        vendorId,
        selectedFile,
        documentName,
        documentNotes
      );
      console.log('✅ Upload successful:', result);
      
      setUploadDialogOpen(false);
      setSelectedFile(null);
      setDocumentName('');
      setDocumentNotes('');
      fetchDocuments(); // Refresh the list
    } catch (error) {
      console.error('❌ Error uploading document:', error);
      alert('Failed to upload document: ' + error.message);
    } finally {
      setUploading(false);
    }
  };

  const handleDownload = async (doc: ContractDocument) => {
    console.log('⬇️ Downloading document:', doc.document_name, 'for vendor:', vendorId);
    try {
      const blob = await apiService.downloadVendorDocument(vendorId, doc.document_id);
      console.log('📥 Download blob received:', blob);
      
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = doc.document_name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      console.log('✅ Download completed successfully');
    } catch (error) {
      console.error('❌ Error downloading document:', error);
      alert('Failed to download document');
    }
  };

  const handleDelete = async (doc: ContractDocument) => {
    if (window.confirm(`Are you sure you want to delete "${doc.document_name}"?`)) {
      try {
        await apiService.deleteVendorDocument(vendorId, doc.document_id);
        fetchDocuments(); // Refresh the list
      } catch (error) {
        console.error('Error deleting document:', error);
        alert('Failed to delete document');
      }
    }
  };

  const handleView = (doc: ContractDocument) => {
    const pdfUrl = `http://localhost:3030/api/contracts/files/${doc.file_path}`;
    console.log('👁️ Opening PDF viewer with URL:', pdfUrl);
    console.log('📄 Document details:', doc);
    setCurrentPdfUrl(pdfUrl);
    setCurrentPdfName(doc.document_name);
    setPdfViewerOpen(true);
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="subtitle2">Contract Documents</Typography>
        <input
          accept="application/pdf"
          style={{ display: 'none' }}
          id={`upload-button-${vendorId}`}
          type="file"
          onChange={handleFileSelect}
        />
        <label htmlFor={`upload-button-${vendorId}`}>
          <Button
            variant="outlined"
            component="span"
            startIcon={<UploadFileIcon />}
            size="small"
          >
            Upload PDF
          </Button>
        </label>
      </Box>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
          <CircularProgress size={24} />
        </Box>
      ) : documents.length === 0 ? (
        <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
          No contract documents uploaded yet.
        </Typography>
      ) : (
        <List dense>
          {documents.map((doc) => (
            <ListItem key={doc.document_id} divider>
              <ListItemIcon>
                <PictureAsPdfIcon color="error" />
              </ListItemIcon>
              <ListItemText
                primary={doc.document_name}
                secondary={`Uploaded: ${formatDate(doc.uploaded_at)}${doc.notes ? ` • ${doc.notes}` : ''}`}
              />
              <ListItemSecondaryAction>
                <IconButton
                  edge="end"
                  aria-label="view"
                  onClick={() => handleView(doc)}
                  sx={{ mr: 1 }}
                  title="View PDF"
                >
                  <VisibilityIcon />
                </IconButton>
                <IconButton
                  edge="end"
                  aria-label="download"
                  onClick={() => handleDownload(doc)}
                  sx={{ mr: 1 }}
                  title="Download PDF"
                >
                  <DownloadIcon />
                </IconButton>
                <IconButton
                  edge="end"
                  aria-label="delete"
                  onClick={() => handleDelete(doc)}
                  title="Delete PDF"
                >
                  <DeleteIcon />
                </IconButton>
              </ListItemSecondaryAction>
            </ListItem>
          ))}
        </List>
      )}

      {/* Upload Dialog */}
      <Dialog open={uploadDialogOpen} onClose={() => !uploading && setUploadDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Upload Contract Document</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Document Name"
            fullWidth
            variant="outlined"
            value={documentName}
            onChange={(e) => setDocumentName(e.target.value)}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="Notes (Optional)"
            fullWidth
            multiline
            rows={3}
            variant="outlined"
            value={documentNotes}
            onChange={(e) => setDocumentNotes(e.target.value)}
          />
          {selectedFile && (
            <Typography variant="body2" sx={{ mt: 2, color: 'text.secondary' }}>
              File: {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUploadDialogOpen(false)} disabled={uploading}>
            Cancel
          </Button>
          <Button
            onClick={handleUpload}
            variant="contained"
            disabled={!selectedFile || !documentName || uploading}
          >
            {uploading ? <CircularProgress size={20} /> : 'Upload'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* PDF Viewer Modal */}
      <PDFViewer
        open={pdfViewerOpen}
        onClose={() => setPdfViewerOpen(false)}
        pdfUrl={currentPdfUrl}
        documentName={currentPdfName}
        onDownload={() => {
          // Find the document to download
          const docToDownload = documents.find(d => d.document_name === currentPdfName);
          if (docToDownload) {
            handleDownload(docToDownload);
          }
        }}
      />
    </Box>
  );
}

// Vendor row with expandable details
function VendorRow({ vendor, onEdit, onDelete }: { 
  vendor: Vendor; 
  onEdit: (vendor: Vendor) => void;
  onDelete: (id: string) => void;
}) {
  const [open, setOpen] = useState(false);
  const [members, setMembers] = useState<Member[]>([]);
  
  // Only check end dates for non-continuous contracts
  const isContractEndSoon = !vendor.contract_continuous && isDateApproaching(vendor.contract_end_date, 90); // 3 months warning
  const isContractOverdue = !vendor.contract_continuous && isDatePast(vendor.contract_end_date);

  // Determine notification status
  const hasWarning = isContractEndSoon;
  const hasError = isContractOverdue;

  // Fetch members for Lead Manager name lookup
  useEffect(() => {
    const fetchMembers = async () => {
      try {
        const response = await fetch('http://localhost:3030/api/members');
        if (response.ok) {
          const membersData = await response.json();
          setMembers(membersData);
        }
      } catch (error) {
        console.error('Error fetching members:', error);
      }
    };
    
    fetchMembers();
  }, []);

  // Helper function to get lead manager name
  const getLeadManagerName = (managerId?: string): string => {
    if (!managerId) return 'Not assigned';
    const manager = members.find(member => member.id === managerId);
    return manager ? manager.name : 'Not assigned';
  };

  return (
    <>
      <TableRow 
        sx={{ 
          '& > *': { borderBottom: 'unset' },
          backgroundColor: hasError ? 'rgba(244, 67, 54, 0.1)' : (hasWarning ? 'rgba(255, 152, 0, 0.1)' : 'inherit')
        }}
      >
        <TableCell>
          <IconButton size="small" onClick={() => setOpen(!open)}>
            {open ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
          </IconButton>
        </TableCell>
        <TableCell>{vendor.company_name}</TableCell>
        <TableCell>
          <Chip 
            label={formatRiskStatus(vendor.vendor_type)} 
            sx={{ 
              backgroundColor: getRiskStatusColor(vendor.vendor_type),
              color: 'white',
              fontWeight: 'bold'
            }}
          />
        </TableCell>
        <TableCell>
          <Chip 
            label={formatContractStatus(vendor.contract_status)} 
            sx={{ 
              backgroundColor: getContractStatusColor(vendor.contract_status),
              color: 'white'
            }}
          />
        </TableCell>
        <TableCell>
          {isContractOverdue && (
            <Tooltip title="Contract renewal overdue!">
              <WarningIcon color="error" />
            </Tooltip>
          )}
          {!isContractOverdue && isContractEndSoon && (
            <Tooltip title="Contract ending soon!">
              <WarningIcon color="warning" />
            </Tooltip>
          )}
        </TableCell>
        <TableCell>
          <Box sx={{ display: 'flex' }}>
            <IconButton onClick={() => onEdit(vendor)}>
              <EditIcon />
            </IconButton>
            <IconButton onClick={() => onDelete(vendor.id)}>
              <DeleteIcon />
            </IconButton>
          </Box>
        </TableCell>
      </TableRow>
      <TableRow>
        <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={6}>
          <Collapse in={open} timeout="auto" unmountOnExit>
            <Box sx={{ margin: 1 }}>
              <Typography variant="h6" gutterBottom component="div">
                Vendor Details
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2">Vendor Information</Typography>
                  <Typography>Risk Status: {formatRiskStatus(vendor.vendor_type)}</Typography>
                  <Typography>Service Type: {formatServiceType(vendor.service_type)}</Typography>
                  <Typography>Lead Manager: {getLeadManagerName(vendor.lead_manager)}</Typography>
                  <Typography>Website: {vendor.website || 'N/A'}</Typography>
                  <Typography>Countries: {vendor.countries_of_operation?.join(', ') || 'N/A'}</Typography>
                  <Typography>Annual Cost: {vendor.annual_cost ? `$${vendor.annual_cost.toLocaleString()}` : 'Not specified'}</Typography>
                  
                  <Typography variant="subtitle2" sx={{ mt: 2 }}>Account Manager</Typography>
                  {vendor.account_manager_assigned === false ? (
                    <Typography color="text.secondary" sx={{ fontStyle: 'italic' }}>
                      No account manager assigned
                    </Typography>
                  ) : (
                    <>
                      <Typography>Name: {vendor.account_manager_name || 'Not specified'}</Typography>
                      <Typography>Email: {vendor.account_manager_email || 'Not specified'}</Typography>
                      <Typography>Phone: {vendor.account_manager_phone || 'Not specified'}</Typography>
                    </>
                  )}
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2">Contract Information</Typography>
                  <Typography>
                    Status: {formatContractStatus(vendor.contract_status)}
                    {vendor.contract_status === 'expired' && <Chip size="small" label="Expired" color="error" sx={{ ml: 1 }} />}
                    {vendor.contract_status === 'renewal_due' && <Chip size="small" label="Renewal Due" color="warning" sx={{ ml: 1 }} />}
                  </Typography>
                  <Typography>
                    Contract Start: {formatDate(vendor.contract_start_date)}
                  </Typography>
                  {vendor.contract_continuous ? (
                    <Typography>
                      Contract Type: <Chip size="small" label="Monthly Subscription" color="info" sx={{ ml: 1 }} />
                    </Typography>
                  ) : (
                    <Typography>
                      Contract End: {formatDate(vendor.contract_end_date)}
                    </Typography>
                  )}
                  <Typography>
                    Last Renewal: {formatDate(vendor.contract_renewal_date)}
                  </Typography>
                  <Typography>
                    EDD Required: {vendor.edd_required ? 'Yes' : 'No'}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2">Contract Notes</Typography>
                  <Paper sx={{ p: 2, backgroundColor: '#f5f5f5' }}>
                    <Typography variant="body2">{vendor.contract_notes || 'No contract notes available.'}</Typography>
                  </Paper>
                </Grid>
                <Grid item xs={12}>
                  <Divider sx={{ my: 2 }} />
                  <ContractDocuments vendorId={vendor.id} />
                </Grid>
              </Grid>
            </Box>
          </Collapse>
        </TableCell>
      </TableRow>
    </>
  );
}

// Form validation types
interface ValidationError {
  [key: string]: string;
}

// Add/Edit Vendor Dialog
function VendorFormDialog({ open, onClose, onSave, vendor }: {
  open: boolean;
  onClose: () => void;
  onSave: (vendor: Omit<Vendor, 'id' | 'created_at' | 'updated_at'>) => void;
  vendor?: Vendor;
}) {
  const isEditing = !!vendor;
  const [members, setMembers] = useState<Member[]>([]);
  const [formData, setFormData] = useState<Omit<Vendor, 'id' | 'created_at' | 'updated_at'>>(() => {
    if (vendor) {
      const sanitized = sanitizeVendorData(vendor);
      return {
        company_name: sanitized.company_name || '',
        website: sanitized.website || '',
        vendor_type: sanitized.vendor_type,
        service_type: sanitized.service_type,
        lead_manager: sanitized.lead_manager || '',
        countries_of_operation: sanitized.countries_of_operation || [],
        incorporation_date: sanitized.incorporation_date || '',
        registration_number: sanitized.registration_number || '',
        edd_required: sanitized.edd_required || false,
        active: sanitized.active ?? true,
        annual_cost: sanitized.annual_cost ?? undefined,
        // Account Manager fields
        account_manager_assigned: sanitized.account_manager_assigned ?? true,
        account_manager_name: sanitized.account_manager_name || '',
        account_manager_email: sanitized.account_manager_email || '',
        account_manager_phone: sanitized.account_manager_phone || '',
        contract_id: sanitized.contract_id,
        contract_active: sanitized.contract_active || false,
        contract_start_date: sanitized.contract_start_date || '',
        contract_renewal_date: sanitized.contract_renewal_date || '',
        contract_end_date: sanitized.contract_end_date || '',
        contract_notes: sanitized.contract_notes || '',
        contract_continuous: sanitized.contract_continuous || false,
        contract_status: sanitized.contract_status,
        // Review fields
        review_id: sanitized.review_id,
        last_review_date: sanitized.last_review_date || '',
        next_review_date: sanitized.next_review_date || '',
        review_notes: sanitized.review_notes || '',
        review_completed: sanitized.review_completed || false
      };
    }
    return {
      company_name: '',
      website: '',
      vendor_type: 'low',
      service_type: 'it_infrastructure',
      lead_manager: '',
      countries_of_operation: [],
      incorporation_date: '',
      registration_number: '',
      edd_required: false,
      active: true,
      annual_cost: undefined,
      // Account Manager fields
      account_manager_assigned: true,
      account_manager_name: '',
      account_manager_email: '',
      account_manager_phone: '',
      contract_id: undefined,
      contract_active: false,
      contract_start_date: '',
      contract_renewal_date: '',
      contract_end_date: '',
      contract_notes: '',
      contract_continuous: false,
      contract_status: undefined,
      // Review fields
      review_id: undefined,
      last_review_date: '',
      next_review_date: '',
      review_notes: '',
      review_completed: false
    };
  });

  const [errors, setErrors] = useState<ValidationError>({});

  // Fetch members for Lead Manager dropdown
  useEffect(() => {
    const fetchMembers = async () => {
      try {
        const response = await fetch('http://localhost:3030/api/members');
        if (response.ok) {
          const membersData = await response.json();
          setMembers(membersData);
        }
      } catch (error) {
        console.error('Error fetching members:', error);
      }
    };
    
    if (open) {
      fetchMembers();
    }
  }, [open]);

  // Function to validate form data
  const validateForm = (): boolean => {
    const newErrors: ValidationError = {};
    
    // Required fields
    if (!formData.company_name) newErrors.company_name = 'Company name is required';
    if (!formData.vendor_type) newErrors.vendor_type = 'Risk status is required';
    if (!formData.service_type) newErrors.service_type = 'Service type is required';
    
    // Annual cost validation
    if (formData.annual_cost !== undefined && formData.annual_cost < 0) {
      newErrors.annual_cost = 'Annual cost cannot be negative';
    }
    
    // Account Manager email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (formData.account_manager_assigned && formData.account_manager_email && !emailRegex.test(formData.account_manager_email)) {
      newErrors.account_manager_email = 'Invalid email format';
    }
    
    // Date validations
    if (formData.contract_start_date && formData.contract_end_date) {
      if (new Date(formData.contract_start_date) > new Date(formData.contract_end_date)) {
        newErrors.contract_end_date = 'End date must be after start date';
      }
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    setFormData(prev => {
      const updated = { ...prev, [name]: value };
      
      // Auto-calculate next review date when last review date is entered
      if (name === 'last_review_date' && value) {
        try {
          const lastReviewDate = new Date(value);
          if (!isNaN(lastReviewDate.getTime())) {
            // Add one year for the next review date
            const nextReviewDate = new Date(lastReviewDate);
            nextReviewDate.setFullYear(nextReviewDate.getFullYear() + 1);
            
            // Format as YYYY-MM-DD for the date input
            const nextReviewDateStr = nextReviewDate.toISOString().split('T')[0];
            updated.next_review_date = nextReviewDateStr;
          }
        } catch (error) {
          console.error('Error calculating next review date:', error);
        }
      }
      
      return updated;
    });
    
    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Handle select changes
  const handleSelectChange = (e: any) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle date changes
  const handleDateChange = (name: string, date: Date | null) => {
    if (!date || isNaN(date.getTime())) {
      setFormData(prev => ({ ...prev, [name]: '' }));
      return;
    }
    
    try {
      const formattedDate = format(date, 'yyyy-MM-dd');
      setFormData(prev => ({ ...prev, [name]: formattedDate }));
    } catch (error) {
      console.error('Date formatting error:', error);
      setFormData(prev => ({ ...prev, [name]: '' }));
      return;
    }
    
    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Handle form submission
  const handleSubmit = () => {
    if (validateForm()) {
      onSave(formData);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>{isEditing ? 'Edit Vendor' : 'Add New Vendor'}</DialogTitle>
      <DialogContent>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          {/* Basic Vendor Information */}
          <Grid item xs={12} sm={6}>
            <TextField
              name="company_name"
              label="Company Name"
              value={formData.company_name}
              onChange={handleChange}
              fullWidth
              required
              error={!!errors.company_name}
              helperText={errors.company_name}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              name="website"
              label="Website"
              value={formData.website}
              onChange={handleChange}
              fullWidth
              error={!!errors.website}
              helperText={errors.website}
            />
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth required>
              <InputLabel>Risk Status</InputLabel>
              <Select
                name="vendor_type"
                value={formData.vendor_type}
                label="Risk Status"
                onChange={handleSelectChange}
                error={!!errors.vendor_type}
              >
                {riskStatusOptions.map(option => (
                  <MenuItem key={option.value} value={option.value}>{option.label}</MenuItem>
                ))}
              </Select>
              {errors.vendor_type && <FormHelperText error>{errors.vendor_type}</FormHelperText>}
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth required>
              <InputLabel>Service Type</InputLabel>
              <Select
                name="service_type"
                value={formData.service_type}
                label="Service Type"
                onChange={handleSelectChange}
                error={!!errors.service_type}
              >
                {serviceTypeOptions.map(option => (
                  <MenuItem key={option.value} value={option.value}>{option.label}</MenuItem>
                ))}
              </Select>
              {errors.service_type && <FormHelperText error>{errors.service_type}</FormHelperText>}
            </FormControl>
          </Grid>
          
          {/* Management Information */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom>Management Information</Typography>
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth error={!!errors.lead_manager}>
              <InputLabel>Lead Manager</InputLabel>
              <Select
                name="lead_manager"
                value={formData.lead_manager}
                label="Lead Manager"
                onChange={handleSelectChange}
              >
                <MenuItem value="">
                  <em>Select a Lead Manager</em>
                </MenuItem>
                {members.map(member => (
                  <MenuItem key={member.id} value={member.id}>
                    {member.name} {member.department_name && `(${member.department_name})`}
                  </MenuItem>
                ))}
              </Select>
              {errors.lead_manager && <FormHelperText error>{errors.lead_manager}</FormHelperText>}
            </FormControl>
          </Grid>

          {/* Account Manager Section */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom>Account Manager</Typography>
          </Grid>
          
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={!formData.account_manager_assigned}
                  onChange={(e) => {
                    const noAccountManager = e.target.checked;
                    setFormData(prev => ({ 
                      ...prev, 
                      account_manager_assigned: !noAccountManager,
                      // Clear fields when no account manager is selected
                      account_manager_name: noAccountManager ? '' : prev.account_manager_name,
                      account_manager_email: noAccountManager ? '' : prev.account_manager_email,
                      account_manager_phone: noAccountManager ? '' : prev.account_manager_phone
                    }));
                  }}
                />
              }
              label="No Account Manager assigned"
            />
          </Grid>
          
          <Grid item xs={12} sm={4}>
            <TextField
              name="account_manager_name"
              label="Name"
              value={formData.account_manager_name}
              onChange={handleChange}
              fullWidth
              disabled={!formData.account_manager_assigned}
              error={!!errors.account_manager_name}
              helperText={errors.account_manager_name}
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <TextField
              name="account_manager_email"
              label="E-mail"
              type="email"
              value={formData.account_manager_email}
              onChange={handleChange}
              fullWidth
              disabled={!formData.account_manager_assigned}
              error={!!errors.account_manager_email}
              helperText={errors.account_manager_email}
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <TextField
              name="account_manager_phone"
              label="Phone Number"
              value={formData.account_manager_phone}
              onChange={handleChange}
              fullWidth
              disabled={!formData.account_manager_assigned}
              error={!!errors.account_manager_phone}
              helperText={errors.account_manager_phone}
            />
          </Grid>
          
          {/* Company Details */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom>Company Details</Typography>
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <TextField
              name="registration_number"
              label="Registration Number"
              value={formData.registration_number}
              onChange={handleChange}
              fullWidth
              error={!!errors.registration_number}
              helperText={errors.registration_number}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label="Incorporation Date"
                value={formData.incorporation_date && formData.incorporation_date.trim() ? parseISO(formData.incorporation_date) : null}
                onChange={(date) => handleDateChange('incorporation_date', date)}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    error: !!errors.incorporation_date,
                    helperText: errors.incorporation_date
                  }
                }}
              />
            </LocalizationProvider>
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              name="annual_cost"
              label="Annual Cost ($)"
              type="number"
              value={formData.annual_cost || ''}
              onChange={(e) => {
                const value = e.target.value;
                setFormData(prev => ({ 
                  ...prev, 
                  annual_cost: value === '' ? undefined : Number(value)
                }));
              }}
              fullWidth
              error={!!errors.annual_cost}
              helperText={errors.annual_cost}
              inputProps={{ min: 0, step: 1 }}
            />
          </Grid>
          
          {/* Contract Information */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom>Contract Information</Typography>
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <FormControlLabel
              control={
                <Checkbox
                  name="contract_active"
                  checked={formData.contract_active || false}
                  onChange={(e) => setFormData(prev => ({ ...prev, contract_active: e.target.checked }))}
                />
              }
              label="Contract Active"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControlLabel
              control={
                <Checkbox
                  name="edd_required"
                  checked={formData.edd_required}
                  onChange={(e) => setFormData(prev => ({ ...prev, edd_required: e.target.checked }))}
                />
              }
              label="EDD Required"
            />
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <FormControlLabel
              control={
                <Checkbox
                  name="contract_continuous"
                  checked={formData.contract_continuous || false}
                  onChange={(e) => {
                    const isMonthlySubscription = e.target.checked;
                    setFormData(prev => ({ 
                      ...prev, 
                      contract_continuous: isMonthlySubscription,
                      // Clear end date and renewal date if monthly subscription
                      contract_end_date: isMonthlySubscription ? '' : prev.contract_end_date,
                      contract_renewal_date: isMonthlySubscription ? '' : prev.contract_renewal_date
                    }));
                  }}
                />
              }
              label="Monthly Subscription"
            />
          </Grid>
          
          <Grid item xs={12} sm={4}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label="Contract Start Date"
                value={formData.contract_start_date && formData.contract_start_date.trim() ? parseISO(formData.contract_start_date) : null}
                onChange={(date) => handleDateChange('contract_start_date', date)}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    error: !!errors.contract_start_date,
                    helperText: errors.contract_start_date
                  }
                }}
              />
            </LocalizationProvider>
          </Grid>
          <Grid item xs={12} sm={4}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label="Contract End Date"
                value={formData.contract_end_date && formData.contract_end_date.trim() ? parseISO(formData.contract_end_date) : null}
                onChange={(date) => handleDateChange('contract_end_date', date)}
                disabled={formData.contract_continuous && formData.contract_active}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    error: !!errors.contract_end_date,
                    helperText: (formData.contract_continuous && formData.contract_active)
                      ? "End date is disabled for active monthly subscriptions" 
                      : errors.contract_end_date
                  }
                }}
              />
            </LocalizationProvider>
          </Grid>
          <Grid item xs={12} sm={4}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label="Contract Renewal Date"
                value={formData.contract_renewal_date && formData.contract_renewal_date.trim() ? parseISO(formData.contract_renewal_date) : null}
                onChange={(date) => handleDateChange('contract_renewal_date', date)}
                disabled={formData.contract_continuous && formData.contract_active}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    error: !!errors.contract_renewal_date,
                    helperText: (formData.contract_continuous && formData.contract_active)
                      ? "Renewal date is disabled for active monthly subscriptions"
                      : errors.contract_renewal_date
                  }
                }}
              />
            </LocalizationProvider>
          </Grid>
          
          <Grid item xs={12}>
            <TextField
              name="contract_notes"
              label="Contract Notes"
              value={formData.contract_notes}
              onChange={handleChange}
              fullWidth
              multiline
              rows={3}
            />
          </Grid>
          
          {/* Vendor Review Information */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>Vendor Review Information</Typography>
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label="Last Review Date"
                value={formData.last_review_date && formData.last_review_date.trim() ? parseISO(formData.last_review_date) : null}
                onChange={(date) => {
                  handleDateChange('last_review_date', date);
                  // Auto-calculate next review date (1 year later) if not already set
                  if (date && !formData.next_review_date) {
                    const nextYear = new Date(date);
                    nextYear.setFullYear(nextYear.getFullYear() + 1);
                    handleDateChange('next_review_date', nextYear);
                  }
                }}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    error: !!errors.last_review_date,
                    helperText: errors.last_review_date
                  }
                }}
              />
            </LocalizationProvider>
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label="Next Review Date"
                value={formData.next_review_date && formData.next_review_date.trim() ? parseISO(formData.next_review_date) : null}
                onChange={(date) => handleDateChange('next_review_date', date)}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    error: !!errors.next_review_date,
                    helperText: errors.next_review_date || "Reviews should be done annually"
                  }
                }}
              />
            </LocalizationProvider>
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <FormControlLabel
              control={
                <Checkbox
                  name="review_completed"
                  checked={formData.review_completed || false}
                  onChange={(e) => setFormData(prev => ({ ...prev, review_completed: e.target.checked }))}
                />
              }
              label="Review Completed"
            />
          </Grid>
          
          <Grid item xs={12}>
            <TextField
              name="review_notes"
              label="Review Notes"
              value={formData.review_notes}
              onChange={handleChange}
              fullWidth
              multiline
              rows={3}
              placeholder="Add notes about the vendor review..."
            />
          </Grid>
        </Grid>

        {/* EDD Section - only show when EDD is required */}
        {formData.edd_required && (
          <EDDSection 
            vendorId={vendor?.id || null} 
            eddRequired={formData.edd_required} 
          />
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={handleSubmit} variant="contained" color="primary">
          {isEditing ? 'Save Changes' : 'Add Vendor'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}

// Delete confirmation dialog
function DeleteConfirmationDialog({ open, onClose, onConfirm, vendorName }: {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  vendorName: string;
}) {
  return (
    <Dialog open={open} onClose={onClose}>
      <DialogTitle>Confirm Deletion</DialogTitle>
      <DialogContent>
        <Typography>
          Are you sure you want to delete the vendor "{vendorName}"?
          This action cannot be undone.
        </Typography>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={onConfirm} color="error" variant="contained">
          Delete
        </Button>
      </DialogActions>
    </Dialog>
  );
}

// Filter component
function VendorFilters({ onFilterChange }: {
  onFilterChange: (filters: { 
    name: string; 
    riskStatus: string; 
    serviceType: string; 
    contractStatus: string;
    reviewDateFilter: string;
  }) => void;
}) {
  const [filters, setFilters] = useState({
    name: '',
    riskStatus: '',
    serviceType: '',
    contractStatus: '',
    reviewDateFilter: ''
  });

  const handleFilterChange = (key: string, value: string) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  return (
    <Box sx={{ mb: 3, p: 2, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
      <Typography variant="h6" gutterBottom>Filters</Typography>
      <Grid container spacing={2} alignItems="center">
        <Grid item xs={12} sm={3}>
          <TextField
            label="Search by Company Name"
            value={filters.name}
            onChange={(e) => handleFilterChange('name', e.target.value)}
            fullWidth
            margin="normal"
            InputProps={{
              startAdornment: <SearchIcon sx={{ mr: 1, color: 'action.active' }} />
            }}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <FormControl fullWidth margin="normal">
            <InputLabel id="risk-status-filter-label">Risk Status</InputLabel>
            <Select
              labelId="risk-status-filter-label"
              value={filters.riskStatus}
              label="Risk Status"
              onChange={(e) => handleFilterChange('riskStatus', e.target.value)}
            >
              <MenuItem value="">All</MenuItem>
              {riskStatusOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>{option.label}</MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={3}>
          <FormControl fullWidth margin="normal">
            <InputLabel id="service-type-filter-label">Service Type</InputLabel>
            <Select
              labelId="service-type-filter-label"
              value={filters.serviceType}
              label="Service Type"
              onChange={(e) => handleFilterChange('serviceType', e.target.value)}
            >
              <MenuItem value="">All</MenuItem>
              {serviceTypeOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>{option.label}</MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={3}>
          <FormControl fullWidth margin="normal">
            <InputLabel id="contract-status-filter-label">Contract Status</InputLabel>
            <Select
              labelId="contract-status-filter-label"
              value={filters.contractStatus}
              label="Contract Status"
              onChange={(e) => handleFilterChange('contractStatus', e.target.value)}
            >
              <MenuItem value="">All</MenuItem>
              {contractStatusOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>{option.label}</MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
      </Grid>
      <Grid container spacing={2} alignItems="center" sx={{ mt: 1 }}>
        <Grid item xs={12} sm={3}>
          <FormControl fullWidth margin="normal">
            <InputLabel id="review-date-filter-label">Next Review Date</InputLabel>
            <Select
              labelId="review-date-filter-label"
              value={filters.reviewDateFilter}
              label="Next Review Date"
              onChange={(e) => handleFilterChange('reviewDateFilter', e.target.value)}
            >
              {reviewDateFilterOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>{option.label}</MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
      </Grid>
    </Box>
  );
}

// Notification Display Components
const NotificationCard = ({ notification, onEdit }: { notification: Notification; onEdit: () => void }) => {
  const getCardColor = () => {
    switch (notification.alert_type) {
      case 'overdue': return '#6c757d';
      case 'urgent': return '#dc3545';
      case 'warning': return '#ffc107';
      case 'review-overdue': return '#8b5cf6';
      case 'review-urgent': return '#f59e0b';
      case 'review-warning': return '#10b981';
      default: return '#e9ecef';
    }
  };

  const getIcon = () => {
    switch (notification.alert_type) {
      case 'overdue':
      case 'urgent':
        return <ErrorIcon />;
      case 'warning':
      case 'review-urgent':
        return <WarningIcon />;
      case 'review-overdue':
      case 'review-warning':
        return <ScheduleIcon />;
      default:
        return <InfoIcon />;
    }
  };

  const getDaysText = () => {
    if (notification.days_remaining <= 0) {
      return `${Math.abs(notification.days_remaining)} days overdue`;
    }
    return `${notification.days_remaining} days remaining`;
  };

  return (
    <Card 
      sx={{ 
        height: '100%',
        border: `2px solid ${getCardColor()}`,
        '&:hover': { 
          boxShadow: 3,
          transform: 'translateY(-2px)',
          transition: 'all 0.2s'
        }
      }}
    >
      <CardContent sx={{ pb: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
          <Box sx={{ color: getCardColor(), mr: 1 }}>
            {getIcon()}
          </Box>
          <Box sx={{ flex: 1 }}>
            <Typography variant="subtitle2" fontWeight="bold" noWrap>
              {notification.vendor_name}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {notification.type === 'review' ? 'Review Due' : 'Contract Expiry'}
            </Typography>
          </Box>
          {notification.risk_level && (notification.risk_level === 'critical' || notification.risk_level === 'high') && (
            <Chip 
              label={notification.risk_level} 
              size="small" 
              color={notification.risk_level === 'critical' ? 'error' : 'warning'}
              sx={{ fontSize: '0.7rem', height: 20 }}
            />
          )}
        </Box>
        
        <Typography variant="body2" sx={{ mb: 1, fontSize: '0.85rem' }}>
          {format(parseISO(notification.due_date), 'MMM dd, yyyy')}
        </Typography>
        
        <Chip 
          label={getDaysText()}
          size="small"
          color={notification.days_remaining <= 0 ? 'error' : notification.days_remaining <= 7 ? 'error' : notification.days_remaining <= 30 ? 'warning' : 'info'}
          sx={{ fontSize: '0.7rem' }}
        />
      </CardContent>
      <CardActions sx={{ pt: 0, pb: 1, px: 2 }}>
        <Button size="small" onClick={onEdit} sx={{ fontSize: '0.75rem' }}>
          Edit Vendor
        </Button>
      </CardActions>
    </Card>
  );
};



const NotificationSummary = ({ notifications, onEdit }: { notifications: Notification[]; onEdit: (vendorId: string) => void }) => {
  const [expandedCategory, setExpandedCategory] = useState<string | null>(null);

  const groupedNotifications = notifications.reduce((acc, notification) => {
    const category = notification.alert_type || 'other';
    if (!acc[category]) acc[category] = [];
    acc[category].push(notification);
    return acc;
  }, {} as Record<string, Notification[]>);

  const getCategoryInfo = (category: string) => {
    switch (category) {
      case 'overdue':
        return { label: 'Overdue Contracts', color: '#6c757d', icon: <ErrorIcon /> };
      case 'urgent':
        return { label: 'Urgent Renewals', color: '#dc3545', icon: <ErrorIcon /> };
      case 'warning':
        return { label: 'Contract Warnings', color: '#ffc107', icon: <WarningIcon /> };
      case 'review-overdue':
        return { label: 'Overdue Reviews', color: '#8b5cf6', icon: <ScheduleIcon /> };
      case 'review-urgent':
        return { label: 'Urgent Reviews', color: '#f59e0b', icon: <ScheduleIcon /> };
      case 'review-warning':
        return { label: 'Review Notices', color: '#10b981', icon: <InfoIcon /> };
      default:
        return { label: 'Other Alerts', color: '#e9ecef', icon: <InfoIcon /> };
    }
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Alerts & Notifications ({notifications.length})
      </Typography>
      
      <Grid container spacing={2} sx={{ mb: 2 }}>
        {Object.entries(groupedNotifications).map(([category, categoryNotifications]) => {
          const categoryInfo = getCategoryInfo(category);
          return (
            <Grid item xs={12} sm={6} md={4} key={category}>
              <Card 
                sx={{ 
                  cursor: 'pointer',
                  border: `2px solid ${categoryInfo.color}`,
                  '&:hover': { boxShadow: 3 }
                }}
                onClick={() => setExpandedCategory(expandedCategory === category ? null : category)}
              >
                <CardContent sx={{ display: 'flex', alignItems: 'center', py: 2 }}>
                  <Box sx={{ color: categoryInfo.color, mr: 2 }}>
                    {categoryInfo.icon}
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="subtitle1" fontWeight="bold">
                      {categoryInfo.label}
                    </Typography>
                    <Badge badgeContent={categoryNotifications.length} color="primary">
                      <Typography variant="body2" color="text.secondary">
                        Click to view
                      </Typography>
                    </Badge>
                  </Box>
                  {expandedCategory === category ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
                </CardContent>
              </Card>
            </Grid>
          );
        })}
      </Grid>

      {expandedCategory && (
        <Collapse in={expandedCategory !== null}>
          <Paper sx={{ p: 2, mb: 2 }}>
            <Typography variant="subtitle1" gutterBottom fontWeight="bold">
              {getCategoryInfo(expandedCategory).label}
            </Typography>
            <Grid container spacing={2}>
              {groupedNotifications[expandedCategory]?.map(notification => (
                <Grid item xs={12} sm={6} md={4} key={notification.id}>
                  <NotificationCard 
                    notification={notification} 
                    onEdit={() => onEdit(notification.vendor_id)} 
                  />
                </Grid>
              ))}
            </Grid>
          </Paper>
        </Collapse>
      )}
    </Box>
  );
};

type NotificationViewType = 'cards' | 'summary';

// Main component
export default function Vendors() {
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [filteredVendors, setFilteredVendors] = useState<Vendor[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [editingVendor, setEditingVendor] = useState<Vendor | undefined>(undefined);
  const [deletingVendor, setDeletingVendor] = useState<Vendor | null>(null);
  const [alerts, setAlerts] = useState<Notification[]>([]);
  const [notificationView, setNotificationView] = useState<NotificationViewType>('cards');
  const [filters, setFilters] = useState({
    name: '',
    riskStatus: '',
    serviceType: '',
    contractStatus: '',
    reviewDateFilter: ''
  });

  // Fetch vendors on component mount
  useEffect(() => {
    fetchVendors();
  }, []);

  // Apply filters effect
  useEffect(() => {
    applyFilters();
  }, [vendors, filters]);

  // Generate alerts based on dates
  useEffect(() => {
    generateAlerts();
  }, [vendors]);

  // Fetch vendors from API
  const fetchVendors = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await apiService.getVendors();
      if (data) {
        setVendors(data);
        applyFilters();
      }
    } catch (err) {
      console.error('Error fetching vendors:', err);
      setError('Failed to load vendors. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  // Apply filters to vendors
  const applyFilters = () => {
    let filtered = [...vendors];
    
    // Filter by name
    if (filters.name) {
      const searchTerm = filters.name.toLowerCase();
      filtered = filtered.filter(vendor => 
        vendor.company_name.toLowerCase().includes(searchTerm)
      );
    }
    
    // Filter by risk status
    if (filters.riskStatus) {
      filtered = filtered.filter(vendor => 
        vendor.vendor_type === filters.riskStatus
      );
    }
    
    // Filter by service type
    if (filters.serviceType) {
      filtered = filtered.filter(vendor => 
        vendor.service_type === filters.serviceType
      );
    }
    
    // Filter by contract status
    if (filters.contractStatus) {
      filtered = filtered.filter(vendor => 
        vendor.contract_status === filters.contractStatus
      );
    }
    
    // Filter by next review date
    if (filters.reviewDateFilter) {
      filtered = filtered.filter(vendor => {
        const reviewDate = vendor.next_review_date;
        
        switch (filters.reviewDateFilter) {
          case 'no_date':
            return !reviewDate;
          case 'overdue':
            return reviewDate && isReviewDateOverdue(reviewDate);
          case 'next_month':
            return reviewDate && isWithinMonths(reviewDate, 1) && !isReviewDateOverdue(reviewDate);
          case 'next_3_months':
            return reviewDate && isWithinMonths(reviewDate, 3) && !isReviewDateOverdue(reviewDate);
          case 'next_6_months':
            return reviewDate && isWithinMonths(reviewDate, 6) && !isReviewDateOverdue(reviewDate);
          default:
            return true;
        }
      });
    }
    
    setFilteredVendors(filtered);
  };

  // Generate alerts for upcoming contract renewals
  const generateAlerts = () => {
    const notifications: Notification[] = [];
    const today = new Date();
    
    vendors.forEach(vendor => {
      try {
        // Only check contract end dates for non-continuous contracts
        if (!vendor.contract_continuous && vendor.contract_end_date) {
          const contractEndDate = parseISO(vendor.contract_end_date);
          const daysUntilContractEnd = Math.ceil(
            (contractEndDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
          );
          
          if (daysUntilContractEnd <= 0) {
            // Overdue contracts - Black (using error severity with special styling)
            notifications.push({
              id: `overdue-${vendor.id}`,
              type: 'renewal',
              vendor_id: vendor.id,
              vendor_name: vendor.company_name,
              due_date: vendor.contract_end_date,
              days_remaining: daysUntilContractEnd,
              severity: 'error',
              alert_type: 'overdue',
              risk_level: vendor.vendor_type,
              contract_active: vendor.contract_active,
              days_overdue: Math.abs(daysUntilContractEnd)
            });
          } else if (daysUntilContractEnd <= 30) {
            // 1 month or less - Red
            notifications.push({
              id: `urgent-${vendor.id}`,
              type: 'renewal',
              vendor_id: vendor.id,
              vendor_name: vendor.company_name,
              due_date: vendor.contract_end_date,
              days_remaining: daysUntilContractEnd,
              severity: 'error',
              alert_type: 'urgent',
              risk_level: vendor.vendor_type,
              contract_active: vendor.contract_active
            });
          } else if (daysUntilContractEnd <= 90) {
            // 3 months or less - Yellow/Orange
            notifications.push({
              id: `warning-${vendor.id}`,
              type: 'renewal',
              vendor_id: vendor.id,
              vendor_name: vendor.company_name,
              due_date: vendor.contract_end_date,
              days_remaining: daysUntilContractEnd,
              severity: 'warning',
              alert_type: 'warning',
              risk_level: vendor.vendor_type,
              contract_active: vendor.contract_active
            });
          }
        }

        // Check vendor review dates
        if (vendor.next_review_date && !vendor.review_completed) {
          const reviewDate = parseISO(vendor.next_review_date);
          const daysUntilReview = Math.ceil(
            (reviewDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
          );
          
          if (daysUntilReview <= 0) {
            // Overdue reviews
            notifications.push({
              id: `review-overdue-${vendor.id}`,
              type: 'review',
              vendor_id: vendor.id,
              vendor_name: vendor.company_name,
              due_date: vendor.next_review_date,
              days_remaining: daysUntilReview,
              severity: 'error',
              alert_type: 'review-overdue',
              risk_level: vendor.vendor_type,
              days_overdue: Math.abs(daysUntilReview)
            });
          } else if (daysUntilReview <= 30) {
            // 1 month or less - urgent
            notifications.push({
              id: `review-urgent-${vendor.id}`,
              type: 'review',
              vendor_id: vendor.id,
              vendor_name: vendor.company_name,
              due_date: vendor.next_review_date,
              days_remaining: daysUntilReview,
              severity: 'warning',
              alert_type: 'review-urgent',
              risk_level: vendor.vendor_type
            });
          } else if (daysUntilReview <= 90) {
            // 3 months or less - info
            notifications.push({
              id: `review-warning-${vendor.id}`,
              type: 'review',
              vendor_id: vendor.id,
              vendor_name: vendor.company_name,
              due_date: vendor.next_review_date,
              days_remaining: daysUntilReview,
              severity: 'info',
              alert_type: 'review-warning',
              risk_level: vendor.vendor_type
            });
          }
        }
      } catch (e) {
        console.error(`Error processing dates for vendor ${vendor.company_name} (ID: ${vendor.id}). Skipping alerts for this vendor. Error:`, e);
      }
    });
    
    // Sort notifications by days remaining (most urgent first)
    const sortedNotifications = notifications.sort((a, b) => {
      return a.days_remaining - b.days_remaining;
    });
    
    setAlerts(sortedNotifications);
  };

  // Add new vendor
  const handleAddVendor = async (vendorData: Omit<Vendor, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const newVendor = await apiService.createVendor(vendorData);
      
      // Handle review information if provided
      if (newVendor?.id && (vendorData.last_review_date || vendorData.next_review_date || vendorData.review_notes)) {
        const reviewData = {
          last_review_date: vendorData.last_review_date || null,
          next_review_date: vendorData.next_review_date || null,
          review_notes: vendorData.review_notes || null,
          completed: vendorData.review_completed || false
        };

        console.log('Creating review for new vendor:', reviewData);

        const reviewResponse = await fetch(`http://localhost:3030/api/vendors/${newVendor.id}/review`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(reviewData)
        });

        if (!reviewResponse.ok) {
          console.error('Failed to create review, but vendor was created successfully');
        }
      }
      
      fetchVendors();
      setOpenAddDialog(false);
    } catch (err) {
      console.error('Error adding vendor:', err);
      alert('Failed to add vendor');
    }
  };

  // Update existing vendor
  const handleUpdateVendor = async (vendorData: Omit<Vendor, 'id' | 'created_at' | 'updated_at'>) => {
    if (!editingVendor) return;
    
    try {
      // Filter out read-only fields and prepare clean data for the API
      const cleanVendorData = {
        company_name: vendorData.company_name,
        website: vendorData.website || null,
        vendor_type: vendorData.vendor_type,
        service_type: vendorData.service_type,
        lead_manager: vendorData.lead_manager || null,
        countries_of_operation: vendorData.countries_of_operation || null,
        incorporation_date: vendorData.incorporation_date || null,
        registration_number: vendorData.registration_number || null,
        edd_required: vendorData.edd_required,
        active: vendorData.active,
        annual_cost: vendorData.annual_cost ?? null,
        // Account Manager fields
        account_manager_assigned: vendorData.account_manager_assigned ?? true,
        account_manager_name: vendorData.account_manager_name || null,
        account_manager_email: vendorData.account_manager_email || null,
        account_manager_phone: vendorData.account_manager_phone || null,
        // Contract fields
        contract_active: vendorData.contract_active,
        contract_start_date: vendorData.contract_start_date || null,
        contract_renewal_date: vendorData.contract_renewal_date || null,
        contract_end_date: vendorData.contract_continuous ? null : (vendorData.contract_end_date || null),
        contract_notes: vendorData.contract_notes || null,
        contract_continuous: vendorData.contract_continuous || false
      };

      console.log('Sending vendor update:', cleanVendorData);

      const response = await fetch(`http://localhost:3030/api/vendors/${editingVendor.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(cleanVendorData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      // Handle review information if provided
      if (vendorData.last_review_date || vendorData.next_review_date || vendorData.review_notes) {
        const reviewData = {
          last_review_date: vendorData.last_review_date || null,
          next_review_date: vendorData.next_review_date || null,
          review_notes: vendorData.review_notes || null,
          completed: vendorData.review_completed || false
        };

        console.log('Sending review update:', reviewData);

        // Check if review exists and update or create accordingly
        if (vendorData.review_id) {
          // Update existing review
          const reviewResponse = await fetch(`http://localhost:3030/api/vendors/${editingVendor.id}/review/${vendorData.review_id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(reviewData)
          });

          if (!reviewResponse.ok) {
            console.error('Failed to update review, but vendor was updated successfully');
          }
        } else {
          // Create new review
          const reviewResponse = await fetch(`http://localhost:3030/api/vendors/${editingVendor.id}/review`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(reviewData)
          });

          if (!reviewResponse.ok) {
            console.error('Failed to create review, but vendor was updated successfully');
          }
        }
      }
      
      fetchVendors();
      setEditingVendor(undefined);
    } catch (err) {
      console.error('Error updating vendor:', err);
      alert(`Failed to update vendor: ${err.message || err}`);
    }
  };

  // Delete vendor
  const handleDeleteVendor = async () => {
    if (!deletingVendor) return;
    
    try {
      await fetch(`http://localhost:3030/api/vendors/${deletingVendor.id}`, {
        method: 'DELETE'
      });
      
      fetchVendors();
      setDeletingVendor(null);
    } catch (err) {
      console.error('Error deleting vendor:', err);
      alert('Failed to delete vendor');
    }
  };

  // Handle filter changes
  const handleFilterChange = (newFilters: {
    name: string; 
    riskStatus: string; 
    serviceType: string; 
    contractStatus: string;
    reviewDateFilter: string;
  }) => {
    setFilters(newFilters);
  };

  // Show loading indicator
  if (isLoading && vendors.length === 0) {
    return (
      <Box sx={{ padding: 2 }}>
        <Typography variant="h4" gutterBottom>Vendors</Typography>
        <Typography>Loading vendors...</Typography>
      </Box>
    );
  }

  // Show error message
  if (error) {
    return (
      <Box sx={{ padding: 2 }}>
        <Typography variant="h4" gutterBottom>Vendors</Typography>
        <Alert severity="error">{error}</Alert>
        <Button onClick={fetchVendors} sx={{ mt: 2 }}>
          Retry
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ padding: 2 }}>
      <Typography variant="h4" gutterBottom>Vendor Management</Typography>
      
      {/* Enhanced Notifications Section */}
      {alerts.length > 0 && (
        <Box sx={{ mb: 3 }}>
          {/* View Toggle Controls */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h5" gutterBottom sx={{ mb: 0 }}>
              Vendor Notifications
            </Typography>
            <ToggleButtonGroup
              value={notificationView}
              exclusive
              onChange={(_, newView) => newView && setNotificationView(newView)}
              size="small"
            >
              <ToggleButton value="cards">
                <GridViewIcon sx={{ mr: 1 }} />
                Cards
              </ToggleButton>
              <ToggleButton value="summary">
                <InfoIcon sx={{ mr: 1 }} />
                Summary
              </ToggleButton>
            </ToggleButtonGroup>
          </Box>

          {/* Dynamic Notification Display */}
          {notificationView === 'cards' && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Alerts & Notifications ({alerts.length})
              </Typography>
              <Grid container spacing={2}>
                {alerts.map(notification => (
                  <Grid item xs={12} sm={6} md={4} lg={3} key={notification.id}>
                    <NotificationCard 
                      notification={notification} 
                      onEdit={() => {
                        const vendor = vendors.find(v => v.id === notification.vendor_id);
                        if (vendor) setEditingVendor(vendor);
                      }} 
                    />
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}

          {notificationView === 'summary' && (
            <NotificationSummary
              notifications={alerts}
              onEdit={(vendorId) => {
                const vendor = vendors.find(v => v.id === vendorId);
                if (vendor) setEditingVendor(vendor);
              }}
            />
          )}
        </Box>
      )}

      {/* Action Buttons */}
      <Box sx={{ mb: 3 }}>
        <Button 
          variant="contained" 
          color="primary" 
          onClick={() => setOpenAddDialog(true)}
          sx={{ mr: 2 }}
        >
          Add New Vendor
        </Button>
        <Button 
          variant="outlined" 
          color="primary"
          onClick={fetchVendors}
        >
          Refresh Data
        </Button>
      </Box>
      
      {/* Filters */}
      <VendorFilters onFilterChange={handleFilterChange} />

      {/* Split vendors into active and inactive contracts */}
      {(() => {
        const activeVendors = filteredVendors.filter(vendor => vendor.contract_active !== false);
        const inactiveVendors = filteredVendors.filter(vendor => vendor.contract_active === false);

        const VendorTable = ({ vendors, title, isInactive = false }: { vendors: Vendor[], title: string, isInactive?: boolean }) => (
          <Box sx={{ mb: 4 }}>
            <Typography variant="h5" gutterBottom sx={{ mt: 3, mb: 2 }}>
              {title} ({vendors.length})
            </Typography>
            <TableContainer 
              component={Paper} 
              sx={isInactive ? { 
                opacity: 0.6,
                '& .MuiTable-root': { color: 'text.disabled' },
                '& .MuiTableCell-root': { color: 'text.disabled' }
              } : {}}
            >
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell width={50} />
                    <TableCell>Company Name</TableCell>
                    <TableCell>Risk Status</TableCell>
                    <TableCell>Contract Status</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {vendors.length > 0 ? (
                    vendors.map(vendor => (
                      <VendorRow 
                        key={vendor.id} 
                        vendor={vendor} 
                        onEdit={(vendor) => setEditingVendor(vendor)}
                        onDelete={(id) => setDeletingVendor(filteredVendors.find(v => v.id === id) || null)}
                      />
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} align="center">
                        <Typography sx={{ py: 2 }}>
                          No {title.toLowerCase()} found.
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        );

        return (
          <>
            {/* Active Contracts Table */}
            <VendorTable 
              vendors={activeVendors} 
              title="Active Contracts" 
              isInactive={false}
            />
            
            {/* Inactive Contracts Table */}
            {inactiveVendors.length > 0 && (
              <VendorTable 
                vendors={inactiveVendors} 
                title="Inactive Contracts" 
                isInactive={true}
              />
            )}
            
            {/* Show message if no vendors at all */}
            {filteredVendors.length === 0 && (
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell width={50} />
                      <TableCell>Company Name</TableCell>
                      <TableCell>Risk Status</TableCell>
                      <TableCell>Contract Status</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    <TableRow>
                      <TableCell colSpan={6} align="center">
                        <Typography sx={{ py: 2 }}>
                          {vendors.length > 0 
                            ? 'No vendors match the current filters'
                            : 'No vendors found. Add a vendor to get started.'}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </>
        );
      })()}

      {/* Add Vendor Dialog */}
      {openAddDialog && (
        <VendorFormDialog
          open={openAddDialog}
          onClose={() => setOpenAddDialog(false)}
          onSave={handleAddVendor}
        />
      )}

      {/* Edit Vendor Dialog */}
      {editingVendor && (
        <VendorFormDialog
          open={!!editingVendor}
          onClose={() => setEditingVendor(undefined)}
          onSave={handleUpdateVendor}
          vendor={editingVendor}
        />
      )}

      {/* Delete Confirmation Dialog */}
      {deletingVendor && (
        <DeleteConfirmationDialog
          open={!!deletingVendor}
          onClose={() => setDeletingVendor(null)}
          onConfirm={handleDeleteVendor}
          vendorName={deletingVendor.company_name}
        />
      )}
    </Box>
  );
} 
