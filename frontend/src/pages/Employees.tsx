import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Alert,
  Button,
  Modal,
  TextField,
  Select,
  MenuItem,
  InputLabel,
  FormControl,
  Chip,
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import AssessmentIcon from '@mui/icons-material/Assessment';
import { apiService } from '../services/api';
import { PerformanceReport } from '../utils/types/performance';
import PerformanceReportModal from '../components/PerformanceReportModal';
import HolidayManagement from '../components/HolidayManagement';

interface Department {
  id: string;
  name: string;
}

interface Member {
  id: string;
  name: string | null;
  email: string | null;
  role: string | null;
  phone?: string | null;
  address?: string | null;
  notes?: string | null;
  date_of_birth?: string | null;
  start_of_employment?: string | null;
  end_of_employment?: string | null;
  kye_passed?: string | null;
  department_id?: string | null;
  department_name?: string | null;
  status?: string | null;
  yearly_holiday_allowance?: string | null;
  kye_link?: string | null;
  next_performance_review?: string | null;
  last_performance_review?: string | null;
  performance_review_count?: number;
  remaining_vacation_days?: number;
}

const modalStyle = {
  position: 'absolute' as 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: 800,
  bgcolor: 'background.paper',
  boxShadow: 24,
  p: 4,
  maxHeight: '90vh',
  overflowY: 'auto',
  borderRadius: 2,
};

function Employees() {
  const [members, setMembers] = useState<Member[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [openModal, setOpenModal] = useState(false);
  const [selectedMember, setSelectedMember] = useState<Member | null>(null);
  const [editFormData, setEditFormData] = useState<Partial<Member>>({});
  const [performanceModalOpen, setPerformanceModalOpen] = useState(false);
  const [performanceMember, setPerformanceMember] = useState<Member | null>(null);
  const [performanceReports, setPerformanceReports] = useState<PerformanceReport[]>([]);

  const formatDateForInput = (dateString: string | null | undefined): string => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      return date.toISOString().split('T')[0];
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  const currentEmployees = members.filter(member => !member.end_of_employment);
  const formerEmployees = members.filter(member => member.end_of_employment);

  const handleOpenModal = (member: Member) => {
    setSelectedMember(member);
    setEditFormData({
      name: member.name,
      email: member.email,
      role: member.role,
      phone: member.phone,
      address: member.address,
      notes: member.notes,
      date_of_birth: formatDateForInput(member.date_of_birth),
      start_of_employment: formatDateForInput(member.start_of_employment),
      end_of_employment: formatDateForInput(member.end_of_employment),
      kye_passed: member.kye_passed,
      department_id: member.department_id,
      status: member.status,
      yearly_holiday_allowance: member.yearly_holiday_allowance,
      kye_link: member.kye_link,
    });
    fetchPerformanceReports(member.id);
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setSelectedMember(null);
    setEditFormData({});
    setPerformanceReports([]);
  };

  const handleOpenPerformanceModal = (member: Member) => {
    setPerformanceMember(member);
    setPerformanceModalOpen(true);
  };

  const handleClosePerformanceModal = () => {
    setPerformanceModalOpen(false);
    setPerformanceMember(null);
    fetchMembers();
  };

  const handleInputChange = (event: any) => {
    const { name, value } = event.target;
    setEditFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSaveChanges = async () => {
    if (!selectedMember) return;
    setLoading(true);
    setError(null);

    try {
      const updatePayload: Partial<Member> = {};
      Object.keys(editFormData).forEach(key => {
        const k = key as keyof Member;
        if (editFormData[k] !== undefined && editFormData[k] !== selectedMember[k]) {
          (updatePayload as any)[k] = editFormData[k];
        }
      });

      await apiService.updateMember(selectedMember.id, updatePayload);
      fetchMembers();
      handleCloseModal();
    } catch (err: any) {
      console.error('Error saving member changes:', err);
      setError(`Failed to save changes: ${err.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const fetchPerformanceReports = async (memberId: string) => {
    if (!memberId) return;
    try {
      const data = await apiService.getPerformanceReports(memberId);
      setPerformanceReports(data);
    } catch (error) {
      console.error('Error fetching performance reports:', error);
      setPerformanceReports([]);
    }
  };

  const calculateOverallScore = (kpis: any[]) => {
    if (!kpis || kpis.length === 0) return 0;
    const totalWeight = kpis.reduce((sum: number, kpi: any) => sum + (kpi.weight || 1), 0);
    const weightedScore = kpis.reduce((sum: number, kpi: any) => sum + (kpi.score || 0), 0);
    return Math.round((weightedScore / totalWeight));
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'success';
    if (score >= 75) return 'info';
    if (score >= 60) return 'warning';
    return 'error';
  };

  const fetchMembers = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await apiService.getMembers();

      // Fetch vacation days for each member
      const membersWithVacationDays = await Promise.all(
        data.map(async (member: Member) => {
          try {
            const holidaySummary = await apiService.getMemberHolidaySummary(member.id);
            return {
              ...member,
              remaining_vacation_days: holidaySummary.remaining_days || 0
            };
          } catch (error) {
            console.warn(`Failed to fetch vacation days for member ${member.id}:`, error);
            return {
              ...member,
              remaining_vacation_days: 0
            };
          }
        })
      );

      setMembers(membersWithVacationDays);
    } catch (err: any) {
      console.error('Error fetching members:', err);
      setError(`Failed to fetch members: ${err.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const fetchDepartments = async () => {
    try {
      const data = await apiService.getDepartments();
      setDepartments(data as Department[]);
    } catch (error) {
      console.error('Error fetching departments:', error);
    }
  };

  useEffect(() => {
    fetchMembers();
    fetchDepartments();
  }, []);

  const EmployeeTable = ({ employees, title }: { employees: Member[], title: string }) => (
    <Box sx={{ mb: 4 }}>
      <Typography variant="h5" gutterBottom sx={{ mt: 3, mb: 2 }}>
        {title} ({employees.length})
      </Typography>
      <TableContainer component={Paper}>
        <Table sx={{ minWidth: 650 }} aria-label={`${title.toLowerCase()} table`}>
          <TableHead>
            <TableRow sx={{ '& th': { fontWeight: 'bold' } }}>
              <TableCell>Name</TableCell>
              <TableCell>Email</TableCell>
              <TableCell>Role</TableCell>
              <TableCell>Department</TableCell>
              <TableCell>Next Review</TableCell>
              <TableCell>Reviews</TableCell>
              {title === 'Current Employees' && <TableCell>Vacation Days</TableCell>}
              {title === 'Former Employees' && <TableCell>End Date</TableCell>}
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {employees.length === 0 ? (
              <TableRow>
                <TableCell colSpan={title === 'Former Employees' ? 8 : 8} align="center">
                  No {title.toLowerCase()} found.
                </TableCell>
              </TableRow>
            ) : (
              employees.map((member) => (
                <TableRow key={member.id} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                  <TableCell component="th" scope="row">
                    {member.name || 'N/A'}
                  </TableCell>
                  <TableCell>{member.email || 'N/A'}</TableCell>
                  <TableCell>{member.role || 'N/A'}</TableCell>
                  <TableCell>{member.department_name || 'N/A'}</TableCell>
                  <TableCell>
                    {member.next_performance_review ? (
                      <Chip 
                        label={new Date(member.next_performance_review).toLocaleDateString()}
                        size="small"
                        color={
                          member.next_performance_review < new Date().toISOString() 
                            ? 'error' 
                            : member.next_performance_review < new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
                            ? 'warning'
                            : 'success'
                        }
                      />
                    ) : (
                      <Typography variant="body2" color="text.secondary">Not Set</Typography>
                    )}
                  </TableCell>
                  <TableCell>
                    <Chip 
                      label={member.performance_review_count || 0}
                      size="small"
                      variant="outlined"
                    />
                  </TableCell>
                  {title === 'Current Employees' && (
                    <TableCell>
                      <Chip
                        label={`${member.remaining_vacation_days || 0} days`}
                        size="small"
                        color={
                          (member.remaining_vacation_days || 0) < 5
                            ? 'error'
                            : (member.remaining_vacation_days || 0) < 10
                            ? 'warning'
                            : 'success'
                        }
                        variant="outlined"
                      />
                    </TableCell>
                  )}
                  {title === 'Former Employees' && (
                    <TableCell>
                      {member.end_of_employment ? new Date(member.end_of_employment).toLocaleDateString() : 'N/A'}
                    </TableCell>
                  )}
                  <TableCell>
                    <Button
                      variant="outlined"
                      size="small"
                      startIcon={<EditIcon />}
                      onClick={() => handleOpenModal(member)}
                    >
                      View Details
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Employees
      </Typography>

      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>
          <CircularProgress />
        </Box>
      )}

      {error && (
        <Alert severity="error" sx={{ my: 2 }}>
          {error}
        </Alert>
      )}

      {!loading && !error && (
        <>
          <EmployeeTable employees={currentEmployees} title="Current Employees" />
          <EmployeeTable employees={formerEmployees} title="Former Employees" />
        </>
      )}

      <Modal
        open={openModal}
        onClose={handleCloseModal}
        aria-labelledby="edit-member-modal-title"
      >
        <Box sx={modalStyle}>
          <Typography id="edit-member-modal-title" variant="h6" component="h2">
            Employee Details
          </Typography>
          {selectedMember && (
            <Box component="form" sx={{ mt: 2 }}>
              <TextField margin="normal" fullWidth label="Name" name="name" value={editFormData.name || ''} onChange={handleInputChange} />
              <TextField margin="normal" fullWidth label="Email" name="email" value={editFormData.email || ''} onChange={handleInputChange} />
              <TextField margin="normal" fullWidth label="Role" name="role" value={editFormData.role || ''} onChange={handleInputChange} />
              <TextField margin="normal" fullWidth label="Phone" name="phone" value={editFormData.phone || ''} onChange={handleInputChange} />
              <TextField margin="normal" fullWidth label="Address" name="address" multiline rows={3} value={editFormData.address || ''} onChange={handleInputChange} />
              <TextField margin="normal" fullWidth label="Notes" name="notes" multiline rows={3} value={editFormData.notes || ''} onChange={handleInputChange} />
              <TextField margin="normal" fullWidth label="Date of Birth" name="date_of_birth" type="date" value={editFormData.date_of_birth || ''} onChange={handleInputChange} InputLabelProps={{ shrink: true }} />
              <TextField margin="normal" fullWidth label="Start of Employment" name="start_of_employment" type="date" value={editFormData.start_of_employment || ''} onChange={handleInputChange} InputLabelProps={{ shrink: true }} />
              <TextField margin="normal" fullWidth label="End of Employment" name="end_of_employment" type="date" value={editFormData.end_of_employment || ''} onChange={handleInputChange} InputLabelProps={{ shrink: true }} />
              <TextField margin="normal" fullWidth label="KYE Passed" name="kye_passed" value={editFormData.kye_passed || ''} onChange={handleInputChange} />
              <TextField margin="normal" fullWidth label="KYE Report" name="kye_link" value={editFormData.kye_link || ''} onChange={handleInputChange} />
              
              <FormControl fullWidth margin="normal">
                <InputLabel>Department</InputLabel>
                <Select
                  name="department_id"
                  value={editFormData.department_id || ''}
                  onChange={handleInputChange}
                  label="Department"
                >
                  <MenuItem value=""><em>None</em></MenuItem>
                  {departments.map((dept) => (
                    <MenuItem key={dept.id} value={dept.id}>{dept.name}</MenuItem>
                  ))}
                </Select>
              </FormControl>

              <TextField margin="normal" fullWidth label="Status" name="status" value={editFormData.status || ''} onChange={handleInputChange} />
              <TextField margin="normal" fullWidth label="Yearly Holiday Allowance" name="yearly_holiday_allowance" value={editFormData.yearly_holiday_allowance || ''} onChange={handleInputChange} />

              <Box sx={{ mt: 4, pt: 2, borderTop: '1px solid #e0e0e0' }}>
                <Typography variant="h6" gutterBottom>Performance Management</Typography>
                
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle1" gutterBottom>Performance Summary</Typography>
                  <Box sx={{ display: 'flex', gap: 2 }}>
                    <Chip label={`${performanceReports.length} Reviews`} color="primary" variant="outlined" />
                    {performanceReports.length > 0 && (
                      <Chip label={`Avg Score: ${Math.round(performanceReports.reduce((sum, r) => sum + calculateOverallScore(r.kpi_data || []), 0) / performanceReports.length)}%`} color="success" />
                    )}
                  </Box>
                </Box>

                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2">Next Review: {selectedMember?.next_performance_review ? new Date(selectedMember.next_performance_review).toLocaleDateString() : 'Not Set'}</Typography>
                  <Typography variant="body2">Last Review: {selectedMember?.last_performance_review ? new Date(selectedMember.last_performance_review).toLocaleDateString() : 'Not Set'}</Typography>
                </Box>

                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>Previous Reports ({performanceReports.length})</Typography>
                  {performanceReports.length === 0 ? (
                    <Typography variant="body2" color="text.secondary">No performance reports found</Typography>
                  ) : (
                    <Box sx={{ maxHeight: 300, overflowY: 'auto' }}>
                      {performanceReports.map((report) => (
                        <Paper key={report.id} sx={{ p: 2, mb: 1 }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                            <Typography variant="body2">Review Date: {new Date(report.review_date).toLocaleDateString()}</Typography>
                            <Chip label={`${calculateOverallScore(report.kpi_data || [])}%`} color={getScoreColor(calculateOverallScore(report.kpi_data || []))} size="small" />
                          </Box>
                          {report.review_notes && (
                            <Typography variant="body2" color="text.secondary">{report.review_notes}</Typography>
                          )}
                        </Paper>
                      ))}
                    </Box>
                  )}
                </Box>

                <Button
                  variant="contained"
                  startIcon={<AssessmentIcon />}
                  onClick={() => {
                    if (selectedMember) {
                      handleCloseModal();
                      handleOpenPerformanceModal(selectedMember);
                    }
                  }}
                  fullWidth
                >
                  Manage Performance Reports
                </Button>
              </Box>

              {/* Holiday Management Section */}
              <Box sx={{ mt: 4, pt: 2, borderTop: '1px solid #e0e0e0' }}>
                <HolidayManagement
                  memberId={selectedMember.id}
                  memberName={selectedMember.name || 'Unknown'}
                  onHolidayChange={fetchMembers}
                />
              </Box>

              <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
                <Button onClick={handleCloseModal} sx={{ mr: 1 }}>Cancel</Button>
                <Button variant="contained" onClick={handleSaveChanges}>Save Changes</Button>
              </Box>
            </Box>
          )}
        </Box>
      </Modal>

      <PerformanceReportModal
        open={performanceModalOpen}
        onClose={handleClosePerformanceModal}
        memberId={performanceMember ? parseInt(performanceMember.id) : 0}
        memberName={performanceMember?.name || ''}
        onSuccess={() => {
          fetchMembers();
          handleClosePerformanceModal();
        }}
      />
    </Box>
  );
}

export default Employees;