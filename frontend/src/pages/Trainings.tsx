import { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  Switch,
  FormControlLabel,
  Tabs,
  Tab,
  Chip,
  Autocomplete
} from '@mui/material';
import { supabase } from '../utils/supabase/client';
import EditIcon from '@mui/icons-material/Edit';
import { IconButton } from '@mui/material';

interface Training {
  id: string;
  training_name: string;
  description?: string;
  requires_refresh: boolean;
  refresh_frequency_months?: number;
  is_mandatory: boolean;
}

interface TrainingSession {
  id: string;
  training_id: string;
  session_date: string;
  location?: string;
  notes?: string;
  auto_generated: boolean;
  training?: Training;
  attendees?: Attendee[];
}

interface Member {
  id: string;
  name: string;
}

interface Attendee {
  id: string;
  member_id: string;
  training_session_id: string;
  date_completed?: string;
  status?: string;
  pass_fail?: string;
  certificate_link?: string;
  admin_notes?: string;
  member?: Member;
}

function AddTrainingForm({ onClose, onSubmit, existingTraining }: {
  onClose: () => void;
  onSubmit: () => void;
  existingTraining?: Training | null;
}) {
  const [formData, setFormData] = useState({
    training_name: existingTraining?.training_name || '',
    description: existingTraining?.description || '',
    requires_refresh: existingTraining?.requires_refresh || false,
    refresh_frequency_months: existingTraining?.refresh_frequency_months || '',
    is_mandatory: existingTraining?.is_mandatory || false
  });
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!formData.training_name.trim()) {
      setError('Training name is required');
      return;
    }

    try {
      const { error: supabaseError } = await supabase
        .from('trainings')
        .upsert({
          ...(existingTraining?.id ? { id: existingTraining.id } : {}),
          ...formData,
          refresh_frequency_months: formData.refresh_frequency_months || null
        });

      if (supabaseError) throw supabaseError;

      onSubmit();
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save training');
    }
  };

  return (
    <Dialog open={true} onClose={onClose}>
      <Box component="form" onSubmit={handleSubmit} sx={{ p: 2 }}>
        <DialogTitle>
          {existingTraining ? 'Edit Training' : 'Add New Training'}
        </DialogTitle>
        <DialogContent>
          {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

          <TextField
            fullWidth
            label="Training Name"
            value={formData.training_name}
            onChange={(e) => setFormData({ ...formData, training_name: e.target.value })}
            margin="normal"
            required
          />

          <TextField
            fullWidth
            label="Description"
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            margin="normal"
            multiline
            rows={3}
          />

          <FormControlLabel
            control={
              <Switch
                checked={formData.requires_refresh}
                onChange={(e) => setFormData({ ...formData, requires_refresh: e.target.checked })}
              />
            }
            label="Requires Refresh"
          />

          {formData.requires_refresh && (
            <TextField
              fullWidth
              label="Refresh Frequency (months)"
              type="number"
              value={formData.refresh_frequency_months}
              onChange={(e) => setFormData({ ...formData, refresh_frequency_months: e.target.value })}
              margin="normal"
            />
          )}

          <FormControlLabel
            control={
              <Switch
                checked={formData.is_mandatory}
                onChange={(e) => setFormData({ ...formData, is_mandatory: e.target.checked })}
              />
            }
            label="Is Mandatory"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Cancel</Button>
          <Button type="submit" variant="contained">
            {existingTraining ? 'Update Training' : 'Add Training'}
          </Button>
        </DialogActions>
      </Box>
    </Dialog>
  );
}

function AddSessionForm({ onClose, onSubmit, existingSession }: {
  onClose: () => void;
  onSubmit: () => void;
  existingSession?: TrainingSession | null;
}) {
  const [trainings, setTrainings] = useState<Training[]>([]);
  const [members, setMembers] = useState<Member[]>([]);
  const [selectedMembers, setSelectedMembers] = useState<Member[]>(
    existingSession?.attendees?.map(a => a.member!).filter(Boolean) || []
  );
  const [formData, setFormData] = useState({
    training_id: existingSession?.training_id || '',
    session_date: existingSession?.session_date || '',
    location: existingSession?.location || '',
    notes: existingSession?.notes || '',
    auto_generated: existingSession?.auto_generated || false
  });
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      // Fetch trainings
      const { data: trainingsData } = await supabase
        .from('trainings')
        .select('*')
        .order('training_name');
      setTrainings(trainingsData || []);

      // Fetch members
      const { data: membersData } = await supabase
        .from('members')
        .select('id, name')
        .order('name');
      setMembers(membersData || []);
    };

    fetchData();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!formData.training_id) {
      setError('Training type is required');
      return;
    }

    if (!formData.session_date) {
      setError('Session date is required');
      return;
    }

    try {
      // First, create or update the session
      const { data: sessionData, error: sessionError } = await supabase
        .from('training_sessions')
        .upsert({
          ...(existingSession?.id ? { id: existingSession.id } : {}),
          ...formData
        })
        .select()
        .single();

      if (sessionError) throw sessionError;

      // Then, handle attendees
      if (sessionData) {
        // Delete existing attendees if editing
        if (existingSession?.id) {
          await supabase
            .from('attendance')
            .delete()
            .eq('training_session_id', existingSession.id);
        }

        // Add new attendees
        if (selectedMembers.length > 0) {
          const attendees = selectedMembers.map(member => ({
            member_id: member.id,
            training_session_id: sessionData.id,
            status: 'registered'
          }));

          const { error: attendanceError } = await supabase
            .from('attendance')
            .insert(attendees);

          if (attendanceError) throw attendanceError;
        }
      }

      onSubmit();
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save session');
    }
  };

  return (
    <Dialog open={true} onClose={onClose} maxWidth="md" fullWidth>
      <Box component="form" onSubmit={handleSubmit} sx={{ p: 2 }}>
        <DialogTitle>
          {existingSession ? 'Edit Training Session' : 'Add New Training Session'}
        </DialogTitle>
        <DialogContent>
          {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

          <Autocomplete
            value={trainings.find(t => t.id === formData.training_id) || null}
            onChange={(_, newValue) => {
              setFormData({ ...formData, training_id: newValue?.id || '' });
            }}
            options={trainings}
            getOptionLabel={(option) => option.training_name}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Training Type"
                required
                margin="normal"
              />
            )}
          />

          <TextField
            fullWidth
            type="date"
            label="Session Date"
            value={formData.session_date}
            onChange={(e) => setFormData({ ...formData, session_date: e.target.value })}
            InputLabelProps={{ shrink: true }}
            margin="normal"
            required
          />

          <TextField
            fullWidth
            label="Location"
            value={formData.location}
            onChange={(e) => setFormData({ ...formData, location: e.target.value })}
            margin="normal"
          />

          <TextField
            fullWidth
            label="Notes"
            value={formData.notes}
            onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
            margin="normal"
            multiline
            rows={3}
          />

          <Autocomplete
            multiple
            value={selectedMembers}
            onChange={(_, newValue) => {
              setSelectedMembers(newValue);
            }}
            options={members}
            getOptionLabel={(option) => option.name}
            renderTags={(value, getTagProps) =>
              value.map((option, index) => (
                <Chip
                  label={option.name}
                  {...getTagProps({ index })}
                  key={option.id}
                />
              ))
            }
            renderInput={(params) => (
              <TextField
                {...params}
                label="Attendees"
                margin="normal"
              />
            )}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Cancel</Button>
          <Button type="submit" variant="contained">
            {existingSession ? 'Update Session' : 'Add Session'}
          </Button>
        </DialogActions>
      </Box>
    </Dialog>
  );
}

export default function Trainings() {
  const [tabValue, setTabValue] = useState(0);
  const [trainings, setTrainings] = useState<Training[]>([]);
  const [sessions, setSessions] = useState<TrainingSession[]>([]);
  const [openAddTrainingDialog, setOpenAddTrainingDialog] = useState(false);
  const [openAddSessionDialog, setOpenAddSessionDialog] = useState(false);
  const [editingTraining, setEditingTraining] = useState<Training | null>(null);
  const [editingSession, setEditingSession] = useState<TrainingSession | null>(null);

  const fetchTrainings = async () => {
    const { data, error } = await supabase
      .from('trainings')
      .select('*')
      .order('training_name');

    if (error) {
      console.error('Error fetching trainings:', error);
      return;
    }

    setTrainings(data || []);
  };

  const fetchSessions = async () => {
    const { data, error } = await supabase
      .from('training_sessions')
      .select(`
        *,
        training:trainings(*),
        attendees:attendance(
          *,
          member:members(id, name)
        )
      `)
      .order('session_date', { ascending: false });

    if (error) {
      console.error('Error fetching sessions:', error);
      return;
    }

    setSessions(data || []);
  };

  useEffect(() => {
    fetchTrainings();
    fetchSessions();
  }, []);

  return (
    <Box sx={{ width: '100%', padding: 2 }}>
      <Typography variant="h4" gutterBottom>
        Training Management
      </Typography>

      <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>
        <Tab label="Training Types" />
        <Tab label="Training Sessions" />
      </Tabs>

      <Box sx={{ mt: 3 }}>
        {tabValue === 0 && (
          <>
            <Box sx={{ mb: 2, display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                variant="contained"
                onClick={() => setOpenAddTrainingDialog(true)}
              >
                Add Training
              </Button>
            </Box>

            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Training Name</TableCell>
                    <TableCell>Description</TableCell>
                    <TableCell>Requires Refresh</TableCell>
                    <TableCell>Refresh Frequency</TableCell>
                    <TableCell>Mandatory</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {trainings.map((training) => (
                    <TableRow key={training.id}>
                      <TableCell>{training.training_name}</TableCell>
                      <TableCell>{training.description}</TableCell>
                      <TableCell>{training.requires_refresh ? 'Yes' : 'No'}</TableCell>
                      <TableCell>
                        {training.refresh_frequency_months 
                          ? `${training.refresh_frequency_months} months`
                          : '-'}
                      </TableCell>
                      <TableCell>{training.is_mandatory ? 'Yes' : 'No'}</TableCell>
                      <TableCell>
                        <IconButton onClick={() => setEditingTraining(training)}>
                          <EditIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </>
        )}

        {tabValue === 1 && (
          <>
            <Box sx={{ mb: 2, display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                variant="contained"
                onClick={() => setOpenAddSessionDialog(true)}
              >
                Add Session
              </Button>
            </Box>

            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Training Type</TableCell>
                    <TableCell>Date</TableCell>
                    <TableCell>Location</TableCell>
                    <TableCell>Notes</TableCell>
                    <TableCell>Attendees</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {sessions.map((session) => (
                    <TableRow key={session.id}>
                      <TableCell>{session.training?.training_name}</TableCell>
                      <TableCell>
                        {new Date(session.session_date).toLocaleDateString('en-GB')}
                      </TableCell>
                      <TableCell>{session.location}</TableCell>
                      <TableCell>{session.notes}</TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {session.attendees?.map((attendee) => (
                            <Chip
                              key={attendee.id}
                              label={attendee.member?.name}
                              size="small"
                            />
                          ))}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <IconButton onClick={() => setEditingSession(session)}>
                          <EditIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </>
        )}
      </Box>

      {(openAddTrainingDialog || editingTraining) && (
        <AddTrainingForm
          onClose={() => {
            setOpenAddTrainingDialog(false);
            setEditingTraining(null);
          }}
          onSubmit={() => {
            fetchTrainings();
            setOpenAddTrainingDialog(false);
            setEditingTraining(null);
          }}
          existingTraining={editingTraining}
        />
      )}

      {(openAddSessionDialog || editingSession) && (
        <AddSessionForm
          onClose={() => {
            setOpenAddSessionDialog(false);
            setEditingSession(null);
          }}
          onSubmit={() => {
            fetchSessions();
            setOpenAddSessionDialog(false);
            setEditingSession(null);
          }}
          existingSession={editingSession}
        />
      )}
    </Box>
  );
} 