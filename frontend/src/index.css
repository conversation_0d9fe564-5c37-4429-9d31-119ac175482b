@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

* {
  box-sizing: border-box;
}

html {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #fafafa;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(40, 231, 197, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(40, 231, 197, 0.6);
}

/* Remove focus outline for mouse users */
.js-focus-visible :focus:not(.focus-visible) {
  outline: none;
}

/* Smooth transitions for all interactive elements */
button, a, input, select, textarea {
  transition: all 0.2s ease-in-out;
}

/* Improve button hover states */
button:hover {
  transform: translateY(-1px);
}

/* Link styling */
a {
  color: #28E7C5;
  text-decoration: none;
  transition: color 0.2s ease-in-out;
}

a:hover {
  color: #1CB5A0;
  text-decoration: underline;
}
