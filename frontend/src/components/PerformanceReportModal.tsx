import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  Typography,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Chip,
  Alert,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton as MuiIconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import { Close as CloseIcon, Add as AddIcon, Delete as DeleteIcon } from '@mui/icons-material';
import { PerformanceReport, KPI } from '../utils/types/performance';
import { apiService } from '../services/api';

interface PerformanceReportModalProps {
  open: boolean;
  onClose: () => void;
  memberId: number;
  memberName: string;
  onSuccess: () => void;
}

const PerformanceReportModal: React.FC<PerformanceReportModalProps> = ({
  open,
  onClose,
  memberId,
  memberName,
  onSuccess,
}) => {
  const [loading, setLoading] = useState(false);
  const [reports, setReports] = useState<PerformanceReport[]>([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newReport, setNewReport] = useState({
    review_date: new Date().toISOString().split('T')[0],
    review_notes: '',
    kpi_data: [] as KPI[],
  });
  const [newKPI, setNewKPI] = useState<KPI>({
    name: '',
    description: '',
    target: 0,
    actual: 0,
    weight: 1,
    score: 0,
  });

  useEffect(() => {
    if (open && memberId) {
      fetchReports();
    }
  }, [open, memberId]);

  const fetchReports = async () => {
    try {
      const response = await apiService.request<any[]>(`/members/${memberId}/performance-reports`);
      setReports(response);
    } catch (error) {
      console.error('Error fetching performance reports:', error);
    }
  };

  const handleAddKPI = () => {
    if (newKPI.name && newKPI.target > 0) {
      const updatedKPI = {
        ...newKPI,
        score: (newKPI.actual / newKPI.target) * newKPI.weight * 100,
      };
      setNewReport({
        ...newReport,
        kpi_data: [...newReport.kpi_data, updatedKPI],
      });
      setNewKPI({
        name: '',
        description: '',
        target: 0,
        actual: 0,
        weight: 1,
        score: 0,
      });
    }
  };

  const handleRemoveKPI = (index: number) => {
    const updatedKPIs = newReport.kpi_data.filter((_, i) => i !== index);
    setNewReport({ ...newReport, kpi_data: updatedKPIs });
  };

  const handleSubmit = async () => {
    setLoading(true);
    try {
      await api.post(`/members/${memberId}/performance-reports`, newReport);
      setNewReport({
        review_date: new Date().toISOString().split('T')[0],
        review_notes: '',
        kpi_data: [],
      });
      setShowAddForm(false);
      await fetchReports();
      onSuccess();
    } catch (error) {
      console.error('Error creating performance report:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateOverallScore = (kpis: KPI[]) => {
    if (kpis.length === 0) return 0;
    const totalWeight = kpis.reduce((sum, kpi) => sum + kpi.weight, 0);
    const weightedScore = kpis.reduce((sum, kpi) => sum + kpi.score, 0);
    return Math.round((weightedScore / totalWeight));
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'success';
    if (score >= 75) return 'info';
    if (score >= 60) return 'warning';
    return 'error';
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        Performance Reports - {memberName}
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent>
        {/* Performance Summary */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Performance Summary
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <Chip 
              label={`${reports.length} Reviews`} 
              color="primary" 
              variant="outlined" 
            />
            {reports.length > 0 && (
              <Chip 
                label={`Avg Score: ${Math.round(
                  reports.reduce((sum, r) => sum + calculateOverallScore(r.kpi_data), 0) / reports.length
                )}%`} 
                color="success" 
              />
            )}
          </Box>
        </Box>

        {/* Existing Reports */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Previous Reports
          </Typography>
          {reports.length === 0 ? (
            <Alert severity="info">No performance reports found</Alert>
          ) : (
            reports.map((report) => (
              <Paper key={report.id} sx={{ p: 2, mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                  <Typography variant="subtitle1">
                    Review Date: {new Date(report.review_date).toLocaleDateString()}
                  </Typography>
                  <Chip 
                    label={`${calculateOverallScore(report.kpi_data)}%`} 
                    color={getScoreColor(calculateOverallScore(report.kpi_data))} 
                    size="small"
                  />
                </Box>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Next Review: {new Date(report.next_review_date).toLocaleDateString()}
                </Typography>
                {report.review_notes && (
                  <Typography variant="body2" gutterBottom>
                    {report.review_notes}
                  </Typography>
                )}
                {report.report_document_url && (
                  <Button 
                    size="small" 
                    variant="outlined"
                    href={`/uploads/performance-reports/${report.report_document_url}`}
                    target="_blank"
                  >
                    View Report Document
                  </Button>
                )}
                {report.kpi_data.length > 0 && (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      KPIs:
                    </Typography>
                    <TableContainer>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>KPI</TableCell>
                            <TableCell align="right">Target</TableCell>
                            <TableCell align="right">Actual</TableCell>
                            <TableCell align="right">Score</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {report.kpi_data.map((kpi, index) => (
                            <TableRow key={index}>
                              <TableCell>{kpi.name}</TableCell>
                              <TableCell align="right">{kpi.target}</TableCell>
                              <TableCell align="right">{kpi.actual}</TableCell>
                              <TableCell align="right">{Math.round(kpi.score)}%</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Box>
                )}
              </Paper>
            ))
          )}
        </Box>

        {/* Add New Report */}
        {!showAddForm ? (
          <Button
            variant="contained"
            onClick={() => setShowAddForm(true)}
            startIcon={<AddIcon />}
          >
            Add New Report
          </Button>
        ) : (
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Add New Performance Report
            </Typography>
            
            <TextField
              label="Review Date"
              type="date"
              value={newReport.review_date}
              onChange={(e) => setNewReport({ ...newReport, review_date: e.target.value })}
              fullWidth
              sx={{ mb: 2 }}
              InputLabelProps={{ shrink: true }}
            />
            
            <TextField
              label="Review Notes"
              multiline
              rows={3}
              value={newReport.review_notes}
              onChange={(e) => setNewReport({ ...newReport, review_notes: e.target.value })}
              fullWidth
              sx={{ mb: 2 }}
            />

            {/* KPI Management */}
            <Typography variant="h6" gutterBottom>
              Key Performance Indicators (KPIs)
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', mb: 2 }}>
              <TextField
                label="KPI Name"
                value={newKPI.name}
                onChange={(e) => setNewKPI({ ...newKPI, name: e.target.value })}
                size="small"
              />
              <TextField
                label="Target"
                type="number"
                value={newKPI.target}
                onChange={(e) => setNewKPI({ ...newKPI, target: Number(e.target.value) })}
                size="small"
              />
              <TextField
                label="Actual"
                type="number"
                value={newKPI.actual}
                onChange={(e) => setNewKPI({ ...newKPI, actual: Number(e.target.value) })}
                size="small"
              />
              <TextField
                label="Weight"
                type="number"
                value={newKPI.weight}
                onChange={(e) => setNewKPI({ ...newKPI, weight: Number(e.target.value) })}
                size="small"
              />
              <Button
                variant="outlined"
                onClick={handleAddKPI}
                startIcon={<AddIcon />}
                size="small"
              >
                Add KPI
              </Button>
            </Box>

            {newReport.kpi_data.length > 0 && (
              <TableContainer sx={{ mb: 2 }}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>KPI</TableCell>
                      <TableCell align="right">Target</TableCell>
                      <TableCell align="right">Actual</TableCell>
                      <TableCell align="right">Score</TableCell>
                      <TableCell align="right">Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {newReport.kpi_data.map((kpi, index) => (
                      <TableRow key={index}>
                        <TableCell>{kpi.name}</TableCell>
                        <TableCell align="right">{kpi.target}</TableCell>
                        <TableCell align="right">{kpi.actual}</TableCell>
                        <TableCell align="right">{Math.round(kpi.score)}%</TableCell>
                        <TableCell align="right">
                          <IconButton
                            size="small"
                            onClick={() => handleRemoveKPI(index)}
                            color="error"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
              <Button
                variant="outlined"
                onClick={() => {
                  setShowAddForm(false);
                  setNewReport({
                    review_date: new Date().toISOString().split('T')[0],
                    review_notes: '',
                    kpi_data: [],
                  });
                }}
              >
                Cancel
              </Button>
              <Button
                variant="contained"
                onClick={handleSubmit}
                disabled={loading || newReport.kpi_data.length === 0}
              >
                {loading ? 'Creating...' : 'Create Report'}
              </Button>
            </Box>
          </Paper>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default PerformanceReportModal;