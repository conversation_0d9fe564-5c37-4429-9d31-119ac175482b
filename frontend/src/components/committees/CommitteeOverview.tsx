interface CommitteeOverview {
  id: string;
  name: string;
  members: Member[];
  lastMeeting?: Meeting;
  nextMeetingDate?: string;
}

// Add this component to show committee overview with timeline
function CommitteeTimeline({ committee }: { committee: CommitteeOverview }) {
  return (
    <Box>
      <Typography variant="h6">{committee.name}</Typography>
      <Timeline>
        {committee.lastMeeting && (
          <TimelineItem>
            <TimelineContent>
              Last Meeting: {new Date(committee.lastMeeting.date).toLocaleDateString()}
            </TimelineContent>
          </TimelineItem>
        )}
        {committee.nextMeetingDate && (
          <TimelineItem>
            <TimelineContent>
              Next Meeting: {new Date(committee.nextMeetingDate).toLocaleDateString()}
            </TimelineContent>
          </TimelineItem>
        )}
      </Timeline>
    </Box>
  );
} 