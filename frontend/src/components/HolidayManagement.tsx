import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  Chip,
  IconButton,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { Add as AddIcon, Delete as DeleteIcon, Edit as EditIcon } from '@mui/icons-material';
import { format, addDays, isWeekend } from 'date-fns';
import { apiService } from '../services/api';

interface Holiday {
  id: string;
  member_id: string;
  start_date: string;
  end_date: string;
  reason?: string;
  days_taken: number;
  status: 'pending' | 'approved' | 'rejected';
  created_at: string;
}

interface HolidaySummary {
  member_id: string;
  yearly_allowance: number;
  days_used: number;
  remaining_days: number;
  holiday_count: number;
}

interface HolidayManagementProps {
  memberId: string;
  memberName: string;
  onHolidayChange?: () => void;
}

function HolidayForm({ 
  memberId, 
  holiday, 
  onClose, 
  onSubmit 
}: {
  memberId: string;
  holiday?: Holiday;
  onClose: () => void;
  onSubmit: () => void;
}) {
  const [startDate, setStartDate] = useState<Date | null>(
    holiday ? new Date(holiday.start_date) : null
  );
  const [endDate, setEndDate] = useState<Date | null>(
    holiday ? new Date(holiday.end_date) : null
  );
  const [reason, setReason] = useState(holiday?.reason || '');
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!startDate || !endDate) {
      setError('Please select both start and end dates');
      return;
    }

    if (endDate < startDate) {
      setError('End date cannot be before start date');
      return;
    }

    setLoading(true);
    try {
      const holidayData = {
        start_date: startDate.toISOString().split('T')[0],
        end_date: endDate.toISOString().split('T')[0],
        reason: reason,
        status: 'approved'
      };

      if (holiday) {
        await apiService.updateHoliday(holiday.id, holidayData);
      } else {
        await apiService.createHoliday(memberId, holidayData);
      }

      onSubmit();
      onClose();
    } catch (err: any) {
      setError(err.message || 'Failed to save holiday');
    } finally {
      setLoading(false);
    }
  };

  const calculateDays = () => {
    if (!startDate || !endDate) return 0;
    return Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;
  };

  const calculateReturnDate = () => {
    if (!endDate) return null;
    let returnDate = addDays(endDate, 1);
    while (isWeekend(returnDate)) {
      returnDate = addDays(returnDate, 1);
    }
    return returnDate;
  };

  return (
    <Dialog open={true} onClose={onClose} maxWidth="sm" fullWidth>
      <Box component="form" onSubmit={handleSubmit}>
        <DialogTitle>{holiday ? 'Edit Holiday' : 'Add New Holiday'}</DialogTitle>
        <DialogContent>
          {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <Box sx={{ mt: 2 }}>
              <DatePicker
                label="Start Date"
                value={startDate}
                onChange={(newValue) => setStartDate(newValue)}
                slotProps={{ textField: { fullWidth: true, margin: 'normal' } }}
              />
            </Box>
            <Box sx={{ mt: 2 }}>
              <DatePicker
                label="End Date"
                value={endDate}
                onChange={(newValue) => setEndDate(newValue)}
                minDate={startDate || undefined}
                slotProps={{ textField: { fullWidth: true, margin: 'normal' } }}
              />
            </Box>
          </LocalizationProvider>

          <TextField
            fullWidth
            label="Reason for Leave"
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            margin="normal"
            multiline
            rows={3}
          />

          {startDate && endDate && (
            <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
              <Typography variant="body2" gutterBottom>
                <strong>Days Requested:</strong> {calculateDays()} days
              </Typography>
              <Typography variant="body2">
                <strong>Return Date:</strong> {calculateReturnDate() ? format(calculateReturnDate()!, 'dd MMMM yyyy') : 'N/A'}
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Cancel</Button>
          <Button type="submit" variant="contained" disabled={loading}>
            {loading ? 'Saving...' : holiday ? 'Update' : 'Add Holiday'}
          </Button>
        </DialogActions>
      </Box>
    </Dialog>
  );
}

export default function HolidayManagement({ memberId, memberName, onHolidayChange }: HolidayManagementProps) {
  const [holidays, setHolidays] = useState<Holiday[]>([]);
  const [summary, setSummary] = useState<HolidaySummary | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingHoliday, setEditingHoliday] = useState<Holiday | null>(null);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const [holidaysData, summaryData] = await Promise.all([
        apiService.getMemberHolidays(memberId),
        apiService.getMemberHolidaySummary(memberId)
      ]);
      setHolidays(holidaysData);
      setSummary(summaryData);
    } catch (error) {
      console.error('Error fetching holiday data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (memberId) {
      fetchData();
    }
  }, [memberId]);

  const handleHolidaySubmit = () => {
    fetchData();
    if (onHolidayChange) {
      onHolidayChange();
    }
  };

  const handleDeleteHoliday = async (holidayId: string) => {
    if (window.confirm('Are you sure you want to delete this holiday?')) {
      try {
        await apiService.deleteHoliday(holidayId);
        fetchData();
        if (onHolidayChange) {
          onHolidayChange();
        }
      } catch (error) {
        console.error('Error deleting holiday:', error);
      }
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Holiday Management</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setShowAddForm(true)}
          size="small"
        >
          Add Holiday
        </Button>
      </Box>

      {/* Summary */}
      {summary && (
        <Box sx={{ mb: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
          <Typography variant="subtitle2" gutterBottom>Holiday Summary for {new Date().getFullYear()}</Typography>
          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <Chip label={`Allowance: ${summary.yearly_allowance || 0} days`} color="primary" variant="outlined" />
            <Chip label={`Used: ${summary.days_used || 0} days`} color="info" variant="outlined" />
            <Chip 
              label={`Remaining: ${summary.remaining_days || 0} days`} 
              color={summary.remaining_days < 5 ? 'error' : summary.remaining_days < 10 ? 'warning' : 'success'} 
            />
          </Box>
        </Box>
      )}

      {/* Holidays Table */}
      <TableContainer component={Paper} sx={{ maxHeight: 300 }}>
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell>Start Date</TableCell>
              <TableCell>End Date</TableCell>
              <TableCell>Days</TableCell>
              <TableCell>Reason</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {holidays.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} align="center">
                  No holidays recorded
                </TableCell>
              </TableRow>
            ) : (
              holidays.map((holiday) => (
                <TableRow key={holiday.id}>
                  <TableCell>{format(new Date(holiday.start_date), 'dd MMM yyyy')}</TableCell>
                  <TableCell>{format(new Date(holiday.end_date), 'dd MMM yyyy')}</TableCell>
                  <TableCell>{holiday.days_taken}</TableCell>
                  <TableCell>{holiday.reason || 'N/A'}</TableCell>
                  <TableCell>
                    <Chip 
                      label={holiday.status} 
                      size="small"
                      color={holiday.status === 'approved' ? 'success' : holiday.status === 'rejected' ? 'error' : 'warning'}
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton size="small" onClick={() => setEditingHoliday(holiday)}>
                      <EditIcon fontSize="small" />
                    </IconButton>
                    <IconButton size="small" onClick={() => handleDeleteHoliday(holiday.id)} color="error">
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Add/Edit Holiday Form */}
      {(showAddForm || editingHoliday) && (
        <HolidayForm
          memberId={memberId}
          holiday={editingHoliday || undefined}
          onClose={() => {
            setShowAddForm(false);
            setEditingHoliday(null);
          }}
          onSubmit={handleHolidaySubmit}
        />
      )}
    </Box>
  );
}
