import {
  Dialog,
  DialogTitle,
  DialogContent,
  List,
  ListItem,
  ListItemText,
  Typography,
  Link,
  Box,
  IconButton,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { PolicyVersion } from '../../utils/types/policy';

interface PolicyVersionHistoryProps {
  open: boolean;
  onClose: () => void;
  versions: PolicyVersion[];
  currentVersion: string;
}

function PolicyVersionHistory({ open, onClose, versions, currentVersion }: PolicyVersionHistoryProps) {
  const sortedVersions = [...versions].sort((a, b) => 
    b.created_at.localeCompare(a.created_at)
  );

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          Version History
          <IconButton onClick={onClose}>
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent>
        <List>
          <ListItem>
            <ListItemText
              primary={
                <Typography variant="subtitle1" color="primary">
                  Current Version: {currentVersion}
                </Typography>
              }
            />
          </ListItem>
          {sortedVersions.map((version) => (
            <ListItem key={version.id} divider>
              <ListItemText
                primary={
                  <Typography variant="subtitle1">
                    Version {version.version}
                  </Typography>
                }
                secondary={
                  <>
                    <Typography variant="body2" color="text.secondary">
                      Changes: {version.changes_description}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Author: {version.author}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Created: {new Date(version.created_at).toLocaleString()}
                    </Typography>
                    {version.document_link && (
                      <Link href={version.document_link} target="_blank" rel="noopener">
                        View Document
                      </Link>
                    )}
                  </>
                }
              />
            </ListItem>
          ))}
        </List>
      </DialogContent>
    </Dialog>
  );
}

export default PolicyVersionHistory;
