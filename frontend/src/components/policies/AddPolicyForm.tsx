import { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  Button,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
} from '@mui/material';
import { supabase } from '../../utils/supabase/client';

interface AddPolicyFormProps {
  onClose: () => void;
  onSubmit: () => void;
  existingPolicy?: Policy | null;
  editMode?: 'new_version' | 'current_version';
}

interface Employee {
  id: string;
  name: string;  // Changed from first_name/last_name to match table structure
}

function incrementVersion(currentVersion: string | undefined): string {
  if (!currentVersion) return '1.0.0';
  
  try {
    const [major, minor, patch] = currentVersion.split('.').map(Number);
    if (isNaN(major) || isNaN(minor) || isNaN(patch)) {
      console.warn('Invalid version format:', currentVersion);
      return '1.0.0';
    }
    return `${major}.${minor}.${patch + 1}`;
  } catch (error) {
    console.warn('Error parsing version:', error);
    return '1.0.0';
  }
}

function AddPolicyForm({ onClose, onSubmit, existingPolicy, editMode = 'new_version' }: AddPolicyFormProps) {
  const [formData, setFormData] = useState({
    name: existingPolicy?.name || '',
    description: '',  // Will be populated from policy_versions if editing
    link: existingPolicy?.link || '',
    author: '',  // Will be populated from current author ID
    tags: existingPolicy?.tags?.join(', ') || '',
    last_review: existingPolicy?.last_review || new Date().toISOString().split('T')[0],
    current_version: existingPolicy ? 
      (editMode === 'new_version' ? incrementVersion(existingPolicy.current_version) : existingPolicy.current_version) 
      : '1.0.0',
    editMode: editMode
  });
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [error, setError] = useState<string | null>(null);

  const fetchEmployees = async () => {
    try {
      console.log('Fetching employees...');
      const { data, error } = await supabase
        .from('members')
        .select('id, name')
        .eq('status', 'active')
        .order('name');
      
      if (error) {
        console.error('Error fetching employees:', error);
        throw error;
      }

      console.log('Fetched employees:', data);
      
      if (data) {
        setEmployees(data);
        console.log('Employees state updated:', data);
      }
    } catch (err) {
      console.error('Error in fetchEmployees:', err);
      setError('Failed to fetch employees');
    }
  };

  useEffect(() => {
    fetchEmployees();
  }, [existingPolicy]);

  useEffect(() => {
    console.log('Current employees state:', employees);
  }, [employees]);

  // Add new useEffect to fetch current version data when editing
  useEffect(() => {
    const fetchCurrentVersionData = async () => {
      if (existingPolicy && editMode === 'current_version') {
        try {
          // Fetch the current version's data
          const { data: versionData, error: versionError } = await supabase
            .from('policy_versions')
            .select('*')
            .eq('policy_id', existingPolicy.id)
            .eq('version', existingPolicy.current_version)
            .single();

          if (versionError) throw versionError;

          if (versionData) {
            // Find the employee ID based on the author name
            const employeeMatch = employees.find(emp => emp.name === versionData.author);
            
            setFormData(prev => ({
              ...prev,
              description: versionData.changes_description,
              link: versionData.document_link,
              author: employeeMatch?.id || ''  // Set the employee ID, not the name
            }));
          }
        } catch (err) {
          console.error('Error fetching current version data:', err);
          setError('Failed to fetch current version data');
        }
      }
    };

    fetchCurrentVersionData();
  }, [existingPolicy, editMode, employees]);  // Add employees to dependencies

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    
    try {
      // Health check
      const { error: healthCheckError } = await supabase
        .from('policies')
        .select('count')
        .limit(1);

      if (healthCheckError) {
        throw new Error(`Connection error: ${healthCheckError.message}`);
      }

      const nextReview = new Date(formData.last_review);
      nextReview.setFullYear(nextReview.getFullYear() + 1);

      // Find the selected employee
      const selectedEmployee = employees.find(emp => emp.id === formData.author);
      if (!selectedEmployee) {
        throw new Error('Selected author not found');
      }

      const authorName = selectedEmployee.name;

      if (existingPolicy) {
        if (editMode === 'current_version') {  // Use the prop directly instead of formData.editMode
          // Update the current version
          const { error: updateVersionError } = await supabase
            .from('policy_versions')
            .update({
              version: formData.current_version,  // Add this line to update version number
              changes_description: formData.description,
              document_link: formData.link,
              author: authorName
            })
            .eq('policy_id', existingPolicy.id)
            .eq('version', existingPolicy.current_version);  // Match the old version number

          if (updateVersionError) {
            console.error('Error updating policy version:', updateVersionError);
            throw updateVersionError;
          }

          // Then update the policies table
          const { error: policyUpdateError } = await supabase
            .from('policies')
            .update({
              current_version: formData.current_version,  // Add this line to update version number
              link: formData.link,
              description: formData.description,
              last_review: formData.last_review,
              next_review: nextReview.toISOString().split('T')[0],
              author: authorName,
              updated_at: new Date().toISOString()
            })
            .eq('id', existingPolicy.id);

          if (policyUpdateError) {
            console.error('Error updating policy:', policyUpdateError);
            throw policyUpdateError;
          }
        } else {
          // Create new version
          const { error: versionError } = await supabase
            .from('policy_versions')
            .insert([{
              policy_id: existingPolicy.id,
              version: formData.current_version,
              changes_description: formData.description,
              author: authorName,
              document_link: formData.link,
              created_at: formData.last_review // Use the last_review date as creation date
            }]);

          if (versionError) throw versionError;

          // Update existing policy
          const { error: updateError } = await supabase
            .from('policies')
            .update({
              current_version: formData.current_version,
              link: formData.link,
              last_review: formData.last_review,
              next_review: nextReview.toISOString().split('T')[0],
              author: authorName,
              updated_at: new Date().toISOString()
            })
            .eq('id', existingPolicy.id);

          if (updateError) throw updateError;
        }
      } else {
        // Create new policy
        const { data: newPolicy, error: insertError } = await supabase
          .from('policies')
          .insert([{
            name: formData.name,
            description: formData.description,
            link: formData.link,
            author: authorName,
            tags: formData.tags.split(',').map(tag => tag.trim()),
            last_review: formData.last_review,
            next_review: nextReview.toISOString().split('T')[0],
            current_version: '1.0.0',
            created_at: formData.last_review // Use the last_review date as creation date
          }])
          .select()
          .single();

        if (insertError) throw insertError;

        // Add initial version
        if (newPolicy) {
          const { error: versionError } = await supabase
            .from('policy_versions')
            .insert([{
              policy_id: newPolicy.id,
              version: '1.0.0',
              changes_description: formData.description,
              author: authorName,
              document_link: formData.link,
              created_at: formData.last_review // Use the last_review date as creation date
            }]);

          if (versionError) throw versionError;
        }
      }

      onSubmit();
      onClose();
    } catch (err) {
      console.error('Full error details:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(`Failed to save policy: ${errorMessage}`);
    }
  };

  return (
    <Box component="form" onSubmit={handleSubmit} id="policy-form">
      <DialogTitle>
        {existingPolicy 
          ? (editMode === 'current_version' ? 'Edit Current Version' : 'Create New Version')
          : 'Create New Policy'}
      </DialogTitle>
      <DialogContent>
        {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
        
        {/* Basic Policy Information - only editable for new policies */}
        {!existingPolicy && (
          <>
            <TextField
              fullWidth
              margin="normal"
              label="Name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              required
            />
            <TextField
              fullWidth
              margin="normal"
              label="Description"
              multiline
              rows={3}
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            />
            <TextField
              fullWidth
              margin="normal"
              label="Document Link"
              value={formData.link}
              onChange={(e) => setFormData({ ...formData, link: e.target.value })}
              required
              helperText="Link to the policy document"
            />
            <TextField
              fullWidth
              margin="normal"
              label="Tags (comma-separated)"
              value={formData.tags}
              onChange={(e) => setFormData({ ...formData, tags: e.target.value })}
            />
            {/* Add Author Selection for new policies */}
            <FormControl fullWidth margin="normal">
              <InputLabel id="author-label">Author</InputLabel>
              <Select
                labelId="author-label"
                value={formData.author}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  author: e.target.value
                }))}
                required
                label="Author"
              >
                {employees.map((employee) => (
                  <MenuItem 
                    key={employee.id} 
                    value={employee.id}
                  >
                    {employee.name}
                  </MenuItem>
                ))}
              </Select>
              <FormHelperText>Select the author of this policy</FormHelperText>
            </FormControl>
          </>
        )}

        {/* Version Update Section - shown for existing policies */}
        {existingPolicy && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="subtitle2" sx={{ mb: 2 }}>
              Current Version: {existingPolicy.current_version}
            </Typography>
            
            <TextField
              fullWidth
              margin="normal"
              label="New Version Number"
              value={formData.current_version}
              onChange={(e) => setFormData({ ...formData, current_version: e.target.value })}
              required
              helperText="Format: X.Y.Z (e.g., 1.0.1)"
            />
            
            <TextField
              fullWidth
              margin="normal"
              label="Changes Description"
              multiline
              rows={3}
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              required
              helperText="Please describe the changes made in this version"
            />

            <FormControl fullWidth margin="normal">
              <InputLabel id="author-label">Author</InputLabel>
              <Select
                labelId="author-label"
                value={formData.author}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  author: e.target.value
                }))}
                required
                label="Author"
              >
                {employees.map((employee) => (
                  <MenuItem 
                    key={employee.id} 
                    value={employee.id}
                  >
                    {employee.name}
                  </MenuItem>
                ))}
              </Select>
              <FormHelperText>Select the author of these changes</FormHelperText>
            </FormControl>

            <TextField
              fullWidth
              margin="normal"
              label="Document Link"
              value={formData.link}
              onChange={(e) => setFormData({ ...formData, link: e.target.value })}
              helperText="Link to the new version of the document"
            />
          </Box>
        )}

        <TextField
          fullWidth
          margin="normal"
          label="Last Review Date"
          type="date"
          value={formData.last_review}
          onChange={(e) => setFormData({ ...formData, last_review: e.target.value })}
          InputLabelProps={{ shrink: true }}
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button type="submit" variant="contained">
          {existingPolicy 
            ? (editMode === 'current_version' ? 'Save Changes' : 'Create New Version')
            : 'Create Policy'}
        </Button>
      </DialogActions>
    </Box>
  );
}

export default AddPolicyForm; 
