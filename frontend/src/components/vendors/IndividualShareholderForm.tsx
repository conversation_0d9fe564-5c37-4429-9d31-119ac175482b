import React, { useState, useEffect } from 'react';
import {
  Grid,
  TextField,
  Button,
  CircularProgress,
  DialogActions
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { parseISO } from 'date-fns';

interface IndividualShareholderData {
  shareholding_percentage: number;
  name: string;
  date_of_birth?: string;
  address?: string;
  nationality?: string;
  country_of_residency?: string;
  occupation?: string;
  email?: string;
  phone_number?: string;
  notes?: string;
}

interface IndividualShareholderFormProps {
  onSave: (data: IndividualShareholderData) => void;
  onCancel: () => void;
  initialData?: any;
  loading?: boolean;
}

const IndividualShareholderForm: React.FC<IndividualShareholderFormProps> = ({
  onSave,
  onCancel,
  initialData,
  loading = false
}) => {
  const [formData, setFormData] = useState<IndividualShareholderData>({
    shareholding_percentage: 0,
    name: '',
    date_of_birth: '',
    address: '',
    nationality: '',
    country_of_residency: '',
    occupation: '',
    email: '',
    phone_number: '',
    notes: ''
  });

  useEffect(() => {
    if (initialData) {
      setFormData({
        shareholding_percentage: initialData.shareholding_percentage || 0,
        name: initialData.individual_name || '',
        date_of_birth: initialData.individual_dob || '',
        address: initialData.individual_address || '',
        nationality: initialData.individual_nationality || '',
        country_of_residency: initialData.individual_country_residency || '',
        occupation: initialData.individual_occupation || '',
        email: initialData.individual_email || '',
        phone_number: initialData.individual_phone || '',
        notes: initialData.individual_notes || ''
      });
    }
  }, [initialData]);

  const handleChange = (field: keyof IndividualShareholderData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleDateChange = (date: Date | null) => {
    if (date) {
      const formattedDate = date.toISOString().split('T')[0];
      setFormData(prev => ({ ...prev, date_of_birth: formattedDate }));
    } else {
      setFormData(prev => ({ ...prev, date_of_birth: '' }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <form onSubmit={handleSubmit}>
        <Grid container spacing={3} sx={{ mt: 1 }}>
          <Grid item xs={12} sm={6}>
            <TextField
              label="Shareholding Percentage (%)"
              type="number"
              value={formData.shareholding_percentage}
              onChange={(e) => handleChange('shareholding_percentage', Number(e.target.value))}
              fullWidth
              required
              inputProps={{ min: 0, max: 100, step: 0.01 }}
              helperText="Enter ownership percentage (0-100%)"
            />
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <TextField
              label="Full Name"
              value={formData.name}
              onChange={(e) => handleChange('name', e.target.value)}
              fullWidth
              required
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <DatePicker
              label="Date of Birth"
              value={formData.date_of_birth ? parseISO(formData.date_of_birth) : null}
              onChange={handleDateChange}
              slotProps={{
                textField: {
                  fullWidth: true
                }
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              label="Nationality"
              value={formData.nationality}
              onChange={(e) => handleChange('nationality', e.target.value)}
              fullWidth
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              label="Address"
              value={formData.address}
              onChange={(e) => handleChange('address', e.target.value)}
              fullWidth
              multiline
              rows={2}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              label="Country of Residency"
              value={formData.country_of_residency}
              onChange={(e) => handleChange('country_of_residency', e.target.value)}
              fullWidth
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              label="Occupation"
              value={formData.occupation}
              onChange={(e) => handleChange('occupation', e.target.value)}
              fullWidth
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              label="Email"
              type="email"
              value={formData.email}
              onChange={(e) => handleChange('email', e.target.value)}
              fullWidth
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              label="Phone Number"
              value={formData.phone_number}
              onChange={(e) => handleChange('phone_number', e.target.value)}
              fullWidth
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              label="Notes"
              value={formData.notes}
              onChange={(e) => handleChange('notes', e.target.value)}
              fullWidth
              multiline
              rows={3}
              placeholder="Additional notes about this shareholder..."
            />
          </Grid>
        </Grid>

        <DialogActions sx={{ mt: 3, px: 0 }}>
          <Button onClick={onCancel} disabled={loading}>
            Cancel
          </Button>
          <Button 
            type="submit" 
            variant="contained" 
            disabled={loading || !formData.name || formData.shareholding_percentage <= 0}
          >
            {loading ? <CircularProgress size={20} /> : 'Save Shareholder'}
          </Button>
        </DialogActions>
      </form>
    </LocalizationProvider>
  );
};

export default IndividualShareholderForm; 