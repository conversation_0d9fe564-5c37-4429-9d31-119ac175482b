import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Card,
  CardContent,
  CardActions,
  Grid,
  Chip,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ExpandMore as ExpandMoreIcon,
  Upload as UploadIcon
} from '@mui/icons-material';
import IndividualShareholderForm from './IndividualShareholderForm';
import CorporateShareholderForm from './CorporateShareholderForm';
import SeniorManagementForm from './SeniorManagementForm';
import DocumentUpload from './DocumentUpload';
import CorporateDocumentUpload from './CorporateDocumentUpload';

interface Shareholder {
  shareholder_id: number;
  vendor_id: number;
  shareholder_type: 'individual' | 'corporate';
  shareholding_percentage: number;
  // Individual fields
  individual_name?: string;
  individual_dob?: string;
  individual_address?: string;
  individual_nationality?: string;
  individual_country_residency?: string;
  individual_occupation?: string;
  individual_email?: string;
  individual_phone?: string;
  individual_notes?: string;
  // Corporate fields
  corporate_legal_name?: string;
  corporate_brand_name?: string;
  corporate_registration?: string;
  corporate_country_registration?: string;
  corporate_legal_address?: string;
  corporate_address?: string;
  corporate_business_type?: string;
  corporate_email?: string;
  corporate_phone?: string;
  corporate_notes?: string;
  created_at: string;
  updated_at: string;
}

interface SeniorManagement {
  senior_management_id: number;
  corporate_shareholder_id: number;
  name: string;
  role: string;
  date_of_birth?: string;
  address?: string;
  nationality?: string;
  country_of_residency?: string;
  occupation?: string;
  email?: string;
  phone_number?: string;
  notes?: string;
  corporate_name: string;
}

interface EDDSectionProps {
  vendorId: string | null;
  eddRequired: boolean;
}

const EDDSection: React.FC<EDDSectionProps> = ({ vendorId, eddRequired }) => {
  const [shareholders, setShareholders] = useState<Shareholder[]>([]);
  const [seniorManagement, setSeniorManagement] = useState<SeniorManagement[]>([]);
  const [addShareholderDialog, setAddShareholderDialog] = useState(false);
  const [shareholderTypeDialog, setShareholderTypeDialog] = useState(false);
  const [selectedShareholderType, setSelectedShareholderType] = useState<'individual' | 'corporate'>('individual');
  const [editingShareholder, setEditingShareholder] = useState<Shareholder | null>(null);
  const [addSeniorManagementDialog, setAddSeniorManagementDialog] = useState(false);
  const [selectedCorporateShareholderId, setSelectedCorporateShareholderId] = useState<number | null>(null);
  const [documentUploadDialog, setDocumentUploadDialog] = useState(false);
  const [selectedShareholderForDocs, setSelectedShareholderForDocs] = useState<number | null>(null);
  const [corporateDocumentDialog, setCorporateDocumentDialog] = useState(false);
  const [selectedCorporateShareholderForDocs, setSelectedCorporateShareholderForDocs] = useState<number | null>(null);
  const [selectedDocumentType, setSelectedDocumentType] = useState<string>('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (eddRequired && vendorId) {
      fetchShareholders();
      fetchSeniorManagement();
    }
  }, [eddRequired, vendorId]);

  const fetchShareholders = async () => {
    try {
      const response = await fetch(`http://localhost:3030/api/vendors/${vendorId}/shareholders`);
      if (response.ok) {
        const data = await response.json();
        setShareholders(data);
      }
    } catch (error) {
      console.error('Error fetching shareholders:', error);
    }
  };

  const fetchSeniorManagement = async () => {
    try {
      // Fetch senior management for all corporate shareholders
      const corporateShareholders = shareholders.filter(s => s.shareholder_type === 'corporate');
      const allSeniorManagement: SeniorManagement[] = [];
      
      for (const corporate of corporateShareholders) {
        const response = await fetch(`http://localhost:3030/api/vendors/${vendorId}/shareholders/${corporate.shareholder_id}/senior-management`);
        if (response.ok) {
          const data = await response.json();
          allSeniorManagement.push(...data);
        }
      }
      
      setSeniorManagement(allSeniorManagement);
    } catch (error) {
      console.error('Error fetching senior management:', error);
    }
  };

  const handleAddShareholder = () => {
    setShareholderTypeDialog(true);
  };

  const handleShareholderTypeSelect = (type: 'individual' | 'corporate') => {
    setSelectedShareholderType(type);
    setShareholderTypeDialog(false);
    setAddShareholderDialog(true);
  };

  const handleShareholderSave = async (shareholderData: any) => {
    setLoading(true);
    try {
      const url = editingShareholder
        ? `http://localhost:3030/api/vendors/${vendorId}/shareholders/${editingShareholder.shareholder_id}`
        : `http://localhost:3030/api/vendors/${vendorId}/shareholders`;
      
      const method = editingShareholder ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          shareholder_type: selectedShareholderType,
          ...shareholderData
        })
      });

      if (response.ok) {
        await fetchShareholders();
        setAddShareholderDialog(false);
        setEditingShareholder(null);
      }
    } catch (error) {
      console.error('Error saving shareholder:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleEditShareholder = (shareholder: Shareholder) => {
    setEditingShareholder(shareholder);
    setSelectedShareholderType(shareholder.shareholder_type);
    setAddShareholderDialog(true);
  };

  const handleDeleteShareholder = async (shareholderId: number) => {
    if (window.confirm('Are you sure you want to delete this shareholder?')) {
      try {
        const response = await fetch(`http://localhost:3030/api/vendors/${vendorId}/shareholders/${shareholderId}`, {
          method: 'DELETE'
        });

        if (response.ok) {
          await fetchShareholders();
          await fetchSeniorManagement();
        }
      } catch (error) {
        console.error('Error deleting shareholder:', error);
      }
    }
  };

  const handleAddSeniorManagement = (corporateShareholderId: number) => {
    setSelectedCorporateShareholderId(corporateShareholderId);
    setAddSeniorManagementDialog(true);
  };

  const handleSeniorManagementSave = async (managementData: any) => {
    if (!selectedCorporateShareholderId) return;
    
    setLoading(true);
    try {
      const response = await fetch(`http://localhost:3030/api/vendors/${vendorId}/shareholders/${selectedCorporateShareholderId}/senior-management`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(managementData)
      });

      if (response.ok) {
        await fetchSeniorManagement();
        setAddSeniorManagementDialog(false);
        setSelectedCorporateShareholderId(null);
      }
    } catch (error) {
      console.error('Error saving senior management:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDocumentUpload = (shareholderId: number) => {
    setSelectedShareholderForDocs(shareholderId);
    setDocumentUploadDialog(true);
  };

  const handleCorporateDocumentUpload = (shareholderId: number, documentType: string) => {
    setSelectedCorporateShareholderForDocs(shareholderId);
    setSelectedDocumentType(documentType);
    setCorporateDocumentDialog(true);
  };

  if (!eddRequired) {
    return null;
  }

  return (
    <Box sx={{ mt: 3 }}>
      <Typography variant="h6" gutterBottom>
        Enhanced Due Diligence (EDD)
      </Typography>
      
      {!vendorId ? (
        <Box sx={{ 
          p: 2, 
          bgcolor: 'info.light', 
          borderRadius: 1, 
          mb: 2,
          border: '1px solid',
          borderColor: 'info.main'
        }}>
          <Typography variant="body2" color="info.dark">
            💡 Please save the vendor first to add shareholder information and EDD documentation.
          </Typography>
        </Box>
      ) : (
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddShareholder}
          sx={{ mb: 2 }}
        >
          Add Shareholder
        </Button>
      )}

      {vendorId && shareholders.length > 0 && (
        <Box sx={{ mt: 2 }}>
          {shareholders.map((shareholder) => (
            <Card key={shareholder.shareholder_id} sx={{ mb: 2 }}>
              <CardContent>
                <Grid container spacing={2} alignItems="center">
                  <Grid item>
                    {shareholder.shareholder_type === 'individual' ? (
                      <PersonIcon color="primary" />
                    ) : (
                      <BusinessIcon color="secondary" />
                    )}
                  </Grid>
                  <Grid item xs>
                    <Typography variant="h6">
                      {shareholder.shareholder_type === 'individual' 
                        ? shareholder.individual_name 
                        : shareholder.corporate_legal_name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {shareholder.shareholding_percentage}% ownership • {' '}
                      <Chip 
                        label={shareholder.shareholder_type === 'individual' ? 'Individual' : 'Corporate'} 
                        size="small"
                        color={shareholder.shareholder_type === 'individual' ? 'primary' : 'secondary'}
                      />
                    </Typography>
                  </Grid>
                  <Grid item>
                    <IconButton onClick={() => handleEditShareholder(shareholder)}>
                      <EditIcon />
                    </IconButton>
                    <IconButton onClick={() => handleDeleteShareholder(shareholder.shareholder_id)}>
                      <DeleteIcon />
                    </IconButton>
                    <IconButton onClick={() => handleDocumentUpload(shareholder.shareholder_id)}>
                      <UploadIcon />
                    </IconButton>
                  </Grid>
                </Grid>

                {shareholder.shareholder_type === 'corporate' && (
                  <Box sx={{ mt: 2 }}>
                    <Divider sx={{ my: 2 }} />
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="subtitle1">Senior Management</Typography>
                      <Button
                        size="small"
                        startIcon={<AddIcon />}
                        onClick={() => handleAddSeniorManagement(shareholder.shareholder_id)}
                      >
                        Add Senior Management
                      </Button>
                    </Box>
                    
                    {seniorManagement
                      .filter(sm => sm.shareholder_id === shareholder.shareholder_id)
                      .map((management) => (
                        <Card key={management.senior_management_id} variant="outlined" sx={{ mt: 1 }}>
                          <CardContent sx={{ py: 1 }}>
                            <Typography variant="body1">{management.name}</Typography>
                            <Typography variant="body2" color="text.secondary">
                              {management.role}
                            </Typography>
                          </CardContent>
                        </Card>
                      ))
                    }

                    <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      <Button 
                        size="small" 
                        variant="outlined"
                        onClick={() => handleCorporateDocumentUpload(shareholder.shareholder_id, 'corporate_registry')}
                      >
                        Upload Corporate Registry
                      </Button>
                      <Button 
                        size="small" 
                        variant="outlined"
                        onClick={() => handleCorporateDocumentUpload(shareholder.shareholder_id, 'share_registry')}
                      >
                        Upload Share Registry
                      </Button>
                      <Button 
                        size="small" 
                        variant="outlined"
                        onClick={() => handleCorporateDocumentUpload(shareholder.shareholder_id, 'director_registry')}
                      >
                        Upload Director Registry
                      </Button>
                    </Box>
                  </Box>
                )}
              </CardContent>
            </Card>
          ))}
        </Box>
      )}

      {/* Shareholder Type Selection Dialog */}
      <Dialog open={shareholderTypeDialog} onClose={() => setShareholderTypeDialog(false)}>
        <DialogTitle>Select Shareholder Type</DialogTitle>
        <DialogContent>
          <Typography gutterBottom>
            Please select whether this is an individual or corporate shareholder:
          </Typography>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={6}>
              <Card 
                sx={{ cursor: 'pointer', border: '2px solid transparent' }}
                onClick={() => handleShareholderTypeSelect('individual')}
              >
                <CardContent sx={{ textAlign: 'center' }}>
                  <PersonIcon sx={{ fontSize: 48, color: 'primary.main', mb: 1 }} />
                  <Typography variant="h6">Individual</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Personal shareholder
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={6}>
              <Card 
                sx={{ cursor: 'pointer', border: '2px solid transparent' }}
                onClick={() => handleShareholderTypeSelect('corporate')}
              >
                <CardContent sx={{ textAlign: 'center' }}>
                  <BusinessIcon sx={{ fontSize: 48, color: 'secondary.main', mb: 1 }} />
                  <Typography variant="h6">Corporate</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Company or organization
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShareholderTypeDialog(false)}>Cancel</Button>
        </DialogActions>
      </Dialog>

      {/* Add/Edit Shareholder Dialog */}
      <Dialog 
        open={addShareholderDialog} 
        onClose={() => {
          setAddShareholderDialog(false);
          setEditingShareholder(null);
        }}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {editingShareholder ? 'Edit' : 'Add'} {selectedShareholderType === 'individual' ? 'Individual' : 'Corporate'} Shareholder
        </DialogTitle>
        <DialogContent>
          {selectedShareholderType === 'individual' ? (
            <IndividualShareholderForm
              onSave={handleShareholderSave}
              onCancel={() => {
                setAddShareholderDialog(false);
                setEditingShareholder(null);
              }}
              initialData={editingShareholder}
              loading={loading}
            />
          ) : (
            <CorporateShareholderForm
              onSave={handleShareholderSave}
              onCancel={() => {
                setAddShareholderDialog(false);
                setEditingShareholder(null);
              }}
              initialData={editingShareholder}
              loading={loading}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Add Senior Management Dialog */}
      <Dialog 
        open={addSeniorManagementDialog} 
        onClose={() => {
          setAddSeniorManagementDialog(false);
          setSelectedCorporateShareholderId(null);
        }}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Add Senior Management</DialogTitle>
        <DialogContent>
          <SeniorManagementForm
            onSave={handleSeniorManagementSave}
            onCancel={() => {
              setAddSeniorManagementDialog(false);
              setSelectedCorporateShareholderId(null);
            }}
            loading={loading}
          />
        </DialogContent>
      </Dialog>

      {/* Document Upload Dialog */}
      <Dialog 
        open={documentUploadDialog} 
        onClose={() => {
          setDocumentUploadDialog(false);
          setSelectedShareholderForDocs(null);
        }}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Upload Documents</DialogTitle>
        <DialogContent>
          {selectedShareholderForDocs && (
            <DocumentUpload
              vendorId={vendorId}
              shareholderId={selectedShareholderForDocs}
              onClose={() => {
                setDocumentUploadDialog(false);
                setSelectedShareholderForDocs(null);
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Corporate Document Upload Dialog */}
      <Dialog 
        open={corporateDocumentDialog} 
        onClose={() => {
          setCorporateDocumentDialog(false);
          setSelectedCorporateShareholderForDocs(null);
          setSelectedDocumentType('');
        }}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Upload {selectedDocumentType === 'corporate_registry' ? 'Corporate Registry' : 
                 selectedDocumentType === 'share_registry' ? 'Share Registry' : 
                 selectedDocumentType === 'director_registry' ? 'Director Registry' : 'Document'}
        </DialogTitle>
        <DialogContent>
          {selectedCorporateShareholderForDocs && selectedDocumentType && (
            <CorporateDocumentUpload
              vendorId={vendorId}
              shareholderId={selectedCorporateShareholderForDocs}
              documentType={selectedDocumentType as 'corporate_registry' | 'share_registry' | 'director_registry'}
              onClose={() => {
                setCorporateDocumentDialog(false);
                setSelectedCorporateShareholderForDocs(null);
                setSelectedDocumentType('');
              }}
            />
          )}
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default EDDSection; 