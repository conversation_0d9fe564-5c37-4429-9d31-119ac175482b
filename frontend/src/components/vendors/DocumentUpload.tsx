import React, { useState } from 'react';
import {
  Box,
  Typo<PERSON>,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Description as DocumentIcon,
  Image as ImageIcon,
  PictureAsPdf as PdfIcon
} from '@mui/icons-material';

interface DocumentUploadProps {
  vendorId: string;
  shareholderId: number;
  seniorManagementId?: number;
  preselectedDocumentType?: string;
  onClose: () => void;
}

interface UploadedDocument {
  document_id: number;
  doc_type: string;
  document_name: string;
  uploaded_at: string;
  notes?: string;
}

const DocumentUpload: React.FC<DocumentUploadProps> = ({
  vendorId,
  shareholderId,
  seniorManagementId,
  preselectedDocumentType,
  onClose
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [documentName, setDocumentName] = useState('');
  const [documentType, setDocumentType] = useState(preselectedDocumentType || 'id_document');
  const [notes, setNotes] = useState('');
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [documents, setDocuments] = useState<UploadedDocument[]>([]);

  React.useEffect(() => {
    fetchDocuments();
  }, []);

  const fetchDocuments = async () => {
    try {
      const response = await fetch(`http://localhost:3030/api/vendors/${vendorId}/shareholders/${shareholderId}/documents`);
      if (response.ok) {
        const data = await response.json();
        setDocuments(data);
      }
    } catch (error) {
      console.error('Error fetching documents:', error);
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      if (!documentName) {
        setDocumentName(file.name);
      }
      setError('');
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      setError('Please select a file to upload');
      return;
    }

    if (!documentName) {
      setError('Please enter a document name');
      return;
    }

    setUploading(true);
    setError('');

    try {
      const formData = new FormData();
      formData.append('document', selectedFile);
      formData.append('documentName', documentName);
      formData.append('doc_type', documentType);
      formData.append('notes', notes);
      if (seniorManagementId) {
        formData.append('senior_management_id', seniorManagementId.toString());
      }

      const response = await fetch(`http://localhost:3030/api/vendors/${vendorId}/shareholders/${shareholderId}/documents`, {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        setSuccess(true);
        setSelectedFile(null);
        setDocumentName('');
        setNotes('');
        await fetchDocuments();
        setTimeout(() => setSuccess(false), 3000);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to upload document');
      }
    } catch (error) {
      console.error('Upload error:', error);
      setError('Failed to upload document');
    } finally {
      setUploading(false);
    }
  };

  const getDocumentIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return <PdfIcon color="error" />;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return <ImageIcon color="primary" />;
      default:
        return <DocumentIcon color="action" />;
    }
  };

  const formatDocumentType = (type: string) => {
    const typeMap: { [key: string]: string } = {
      'id_document': 'ID Document',
      'proof_of_address': 'Proof of Address',
      'corporate_registry': 'Corporate Registry',
      'share_registry': 'Share Registry',
      'director_registry': 'Director Registry',
      'other': 'Other'
    };
    return typeMap[type] || type;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <Box sx={{ p: 2 }}>
      {/* Upload Section */}
      <Typography variant="h6" gutterBottom>
        Upload Document
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }}>
          Document uploaded successfully!
        </Alert>
      )}

      <Box sx={{ mb: 3 }}>
        <FormControl fullWidth sx={{ mb: 2 }}>
          <InputLabel>Document Type</InputLabel>
          <Select
            value={documentType}
            onChange={(e) => setDocumentType(e.target.value)}
            label="Document Type"
            disabled={!!preselectedDocumentType}
          >
            <MenuItem value="id_document">ID Document</MenuItem>
            <MenuItem value="proof_of_address">Proof of Address</MenuItem>
            <MenuItem value="corporate_registry">Corporate Registry</MenuItem>
            <MenuItem value="share_registry">Share Registry</MenuItem>
            <MenuItem value="director_registry">Director Registry</MenuItem>
            <MenuItem value="other">Other</MenuItem>
          </Select>
        </FormControl>

        <TextField
          label="Document Name"
          value={documentName}
          onChange={(e) => setDocumentName(e.target.value)}
          fullWidth
          sx={{ mb: 2 }}
          required
        />

        <TextField
          label="Notes (Optional)"
          value={notes}
          onChange={(e) => setNotes(e.target.value)}
          fullWidth
          multiline
          rows={2}
          sx={{ mb: 2 }}
        />

        <Button
          variant="outlined"
          component="label"
          startIcon={<UploadIcon />}
          fullWidth
          sx={{ mb: 2 }}
        >
          Select File
          <input
            type="file"
            hidden
            onChange={handleFileSelect}
            accept=".pdf,.jpg,.jpeg,.png,.gif,.doc,.docx"
          />
        </Button>

        {selectedFile && (
          <Typography variant="body2" sx={{ mb: 2, color: 'text.secondary' }}>
            Selected: {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
          </Typography>
        )}

        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="contained"
            onClick={handleUpload}
            disabled={!selectedFile || !documentName || uploading}
            startIcon={uploading ? <CircularProgress size={20} /> : <UploadIcon />}
          >
            {uploading ? 'Uploading...' : 'Upload Document'}
          </Button>
          <Button variant="outlined" onClick={onClose}>
            Close
          </Button>
        </Box>
      </Box>

      {/* Existing Documents */}
      {documents.length > 0 && (
        <>
          <Divider sx={{ my: 2 }} />
          <Typography variant="h6" gutterBottom>
            Existing Documents
          </Typography>
          <List>
            {documents.map((doc) => (
              <ListItem key={doc.document_id}>
                <ListItemIcon>
                  {getDocumentIcon(doc.document_name)}
                </ListItemIcon>
                <ListItemText
                  primary={doc.document_name}
                  secondary={
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Type: {formatDocumentType(doc.doc_type)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Uploaded: {formatDate(doc.uploaded_at)}
                      </Typography>
                      {doc.notes && (
                        <Typography variant="body2" color="text.secondary">
                          Notes: {doc.notes}
                        </Typography>
                      )}
                    </Box>
                  }
                />
              </ListItem>
            ))}
          </List>
        </>
      )}
    </Box>
  );
};

export default DocumentUpload; 