import React, { useState } from 'react';
import {
  Grid,
  TextField,
  Button,
  CircularProgress,
  DialogActions
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';

interface SeniorManagementData {
  name: string;
  role: string;
  date_of_birth?: string;
  address?: string;
  nationality?: string;
  country_of_residency?: string;
  occupation?: string;
  email?: string;
  phone_number?: string;
  notes?: string;
}

interface SeniorManagementFormProps {
  onSave: (data: SeniorManagementData) => void;
  onCancel: () => void;
  loading?: boolean;
}

const SeniorManagementForm: React.FC<SeniorManagementFormProps> = ({
  onSave,
  onCancel,
  loading = false
}) => {
  const [formData, setFormData] = useState<SeniorManagementData>({
    name: '',
    role: '',
    date_of_birth: '',
    address: '',
    nationality: '',
    country_of_residency: '',
    occupation: '',
    email: '',
    phone_number: '',
    notes: ''
  });

  const handleChange = (field: keyof SeniorManagementData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleDateChange = (date: Date | null) => {
    if (date) {
      const formattedDate = date.toISOString().split('T')[0];
      setFormData(prev => ({ ...prev, date_of_birth: formattedDate }));
    } else {
      setFormData(prev => ({ ...prev, date_of_birth: '' }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <form onSubmit={handleSubmit}>
        <Grid container spacing={3} sx={{ mt: 1 }}>
          <Grid item xs={12} sm={6}>
            <TextField
              label="Full Name"
              value={formData.name}
              onChange={(e) => handleChange('name', e.target.value)}
              fullWidth
              required
            />
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <TextField
              label="Role/Position"
              value={formData.role}
              onChange={(e) => handleChange('role', e.target.value)}
              fullWidth
              required
              helperText="e.g., CEO, CFO, Director, Managing Director"
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <DatePicker
              label="Date of Birth"
              value={formData.date_of_birth ? new Date(formData.date_of_birth) : null}
              onChange={handleDateChange}
              slotProps={{
                textField: {
                  fullWidth: true
                }
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              label="Nationality"
              value={formData.nationality}
              onChange={(e) => handleChange('nationality', e.target.value)}
              fullWidth
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              label="Address"
              value={formData.address}
              onChange={(e) => handleChange('address', e.target.value)}
              fullWidth
              multiline
              rows={2}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              label="Country of Residency"
              value={formData.country_of_residency}
              onChange={(e) => handleChange('country_of_residency', e.target.value)}
              fullWidth
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              label="Occupation"
              value={formData.occupation}
              onChange={(e) => handleChange('occupation', e.target.value)}
              fullWidth
              helperText="Professional background or other occupations"
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              label="Email"
              type="email"
              value={formData.email}
              onChange={(e) => handleChange('email', e.target.value)}
              fullWidth
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              label="Phone Number"
              value={formData.phone_number}
              onChange={(e) => handleChange('phone_number', e.target.value)}
              fullWidth
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              label="Notes"
              value={formData.notes}
              onChange={(e) => handleChange('notes', e.target.value)}
              fullWidth
              multiline
              rows={3}
              placeholder="Additional notes about this senior management member..."
            />
          </Grid>
        </Grid>

        <DialogActions sx={{ mt: 3, px: 0 }}>
          <Button onClick={onCancel} disabled={loading}>
            Cancel
          </Button>
          <Button 
            type="submit" 
            variant="contained" 
            disabled={loading || !formData.name || !formData.role}
          >
            {loading ? <CircularProgress size={20} /> : 'Add Senior Management'}
          </Button>
        </DialogActions>
      </form>
    </LocalizationProvider>
  );
};

export default SeniorManagementForm; 