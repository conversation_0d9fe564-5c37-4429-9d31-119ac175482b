import React, { useState, useEffect } from 'react';
import {
  Grid,
  TextField,
  Button,
  CircularProgress,
  DialogActions
} from '@mui/material';

interface CorporateShareholderData {
  shareholding_percentage: number;
  legal_name: string;
  brand_name?: string;
  registration_number?: string;
  country_of_registration?: string;
  legal_address?: string;
  corporate_address?: string;
  business_type?: string;
  email?: string;
  phone_number?: string;
  notes?: string;
}

interface CorporateShareholderFormProps {
  onSave: (data: CorporateShareholderData) => void;
  onCancel: () => void;
  initialData?: any;
  loading?: boolean;
}

const CorporateShareholderForm: React.FC<CorporateShareholderFormProps> = ({
  onSave,
  onCancel,
  initialData,
  loading = false
}) => {
  const [formData, setFormData] = useState<CorporateShareholderData>({
    shareholding_percentage: 0,
    legal_name: '',
    brand_name: '',
    registration_number: '',
    country_of_registration: '',
    legal_address: '',
    corporate_address: '',
    business_type: '',
    email: '',
    phone_number: '',
    notes: ''
  });

  useEffect(() => {
    if (initialData) {
      setFormData({
        shareholding_percentage: initialData.shareholding_percentage || 0,
        legal_name: initialData.corporate_legal_name || '',
        brand_name: initialData.corporate_brand_name || '',
        registration_number: initialData.corporate_registration || '',
        country_of_registration: initialData.corporate_country_registration || '',
        legal_address: initialData.corporate_legal_address || '',
        corporate_address: initialData.corporate_address || '',
        business_type: initialData.corporate_business_type || '',
        email: initialData.corporate_email || '',
        phone_number: initialData.corporate_phone || '',
        notes: initialData.corporate_notes || ''
      });
    }
  }, [initialData]);

  const handleChange = (field: keyof CorporateShareholderData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit}>
      <Grid container spacing={3} sx={{ mt: 1 }}>
        <Grid item xs={12} sm={6}>
          <TextField
            label="Shareholding Percentage (%)"
            type="number"
            value={formData.shareholding_percentage}
            onChange={(e) => handleChange('shareholding_percentage', Number(e.target.value))}
            fullWidth
            required
            inputProps={{ min: 0, max: 100, step: 0.01 }}
            helperText="Enter ownership percentage (0-100%)"
          />
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <TextField
            label="Legal Name"
            value={formData.legal_name}
            onChange={(e) => handleChange('legal_name', e.target.value)}
            fullWidth
            required
            helperText="Official registered name of the company"
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            label="Brand Name"
            value={formData.brand_name}
            onChange={(e) => handleChange('brand_name', e.target.value)}
            fullWidth
            helperText="Trading or brand name (if different from legal name)"
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            label="Registration Number"
            value={formData.registration_number}
            onChange={(e) => handleChange('registration_number', e.target.value)}
            fullWidth
            helperText="Company registration or incorporation number"
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            label="Country of Registration"
            value={formData.country_of_registration}
            onChange={(e) => handleChange('country_of_registration', e.target.value)}
            fullWidth
            helperText="Country where the company is incorporated"
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            label="Business Type"
            value={formData.business_type}
            onChange={(e) => handleChange('business_type', e.target.value)}
            fullWidth
            helperText="Type of business (e.g., Limited Company, Corporation, LLC)"
          />
        </Grid>

        <Grid item xs={12}>
          <TextField
            label="Legal Address"
            value={formData.legal_address}
            onChange={(e) => handleChange('legal_address', e.target.value)}
            fullWidth
            multiline
            rows={2}
            helperText="Official registered address of the company"
          />
        </Grid>

        <Grid item xs={12}>
          <TextField
            label="Corporate Address"
            value={formData.corporate_address}
            onChange={(e) => handleChange('corporate_address', e.target.value)}
            fullWidth
            multiline
            rows={2}
            helperText="Business operations address (if different from legal address)"
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            label="Email"
            type="email"
            value={formData.email}
            onChange={(e) => handleChange('email', e.target.value)}
            fullWidth
            helperText="Primary contact email"
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            label="Phone Number"
            value={formData.phone_number}
            onChange={(e) => handleChange('phone_number', e.target.value)}
            fullWidth
            helperText="Primary contact phone number"
          />
        </Grid>

        <Grid item xs={12}>
          <TextField
            label="Notes"
            value={formData.notes}
            onChange={(e) => handleChange('notes', e.target.value)}
            fullWidth
            multiline
            rows={3}
            placeholder="Additional notes about this corporate shareholder..."
          />
        </Grid>
      </Grid>

      <DialogActions sx={{ mt: 3, px: 0 }}>
        <Button onClick={onCancel} disabled={loading}>
          Cancel
        </Button>
        <Button 
          type="submit" 
          variant="contained" 
          disabled={loading || !formData.legal_name || formData.shareholding_percentage <= 0}
        >
          {loading ? <CircularProgress size={20} /> : 'Save Corporate Shareholder'}
        </Button>
      </DialogActions>
    </form>
  );
};

export default CorporateShareholderForm; 