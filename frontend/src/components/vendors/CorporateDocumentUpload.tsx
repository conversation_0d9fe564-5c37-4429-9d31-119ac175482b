import React, { useState } from 'react';
import {
  Box,
  Typography,
  Button,
  TextField,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Paper
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Description as DocumentIcon,
  Image as ImageIcon,
  PictureAsPdf as PdfIcon,
  Business as BusinessIcon
} from '@mui/icons-material';

interface CorporateDocumentUploadProps {
  vendorId: string;
  shareholderId: number;
  documentType: 'corporate_registry' | 'share_registry' | 'director_registry';
  onClose: () => void;
}

interface UploadedDocument {
  document_id: number;
  doc_type: string;
  document_name: string;
  uploaded_at: string;
  notes?: string;
}

const CorporateDocumentUpload: React.FC<CorporateDocumentUploadProps> = ({
  vendorId,
  shareholderId,
  documentType,
  onClose
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [documentName, setDocumentName] = useState('');
  const [notes, setNotes] = useState('');
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [documents, setDocuments] = useState<UploadedDocument[]>([]);

  React.useEffect(() => {
    fetchDocuments();
  }, []);

  const fetchDocuments = async () => {
    try {
      const response = await fetch(`http://localhost:3030/api/vendors/${vendorId}/shareholders/${shareholderId}/documents`);
      if (response.ok) {
        const data = await response.json();
        // Filter to show only documents of the specific type
        setDocuments(data.filter((doc: UploadedDocument) => doc.doc_type === documentType));
      }
    } catch (error) {
      console.error('Error fetching documents:', error);
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      if (!documentName) {
        // Create a default name based on document type
        const typeLabel = getDocumentTypeLabel(documentType);
        setDocumentName(`${typeLabel} - ${file.name}`);
      }
      setError('');
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      setError('Please select a file to upload');
      return;
    }

    if (!documentName) {
      setError('Please enter a document name');
      return;
    }

    setUploading(true);
    setError('');

    try {
      const formData = new FormData();
      formData.append('document', selectedFile);
      formData.append('documentName', documentName);
      formData.append('doc_type', documentType);
      formData.append('notes', notes);

      const response = await fetch(`http://localhost:3030/api/vendors/${vendorId}/shareholders/${shareholderId}/documents`, {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        setSuccess(true);
        setSelectedFile(null);
        setDocumentName('');
        setNotes('');
        await fetchDocuments();
        setTimeout(() => setSuccess(false), 3000);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to upload document');
      }
    } catch (error) {
      console.error('Upload error:', error);
      setError('Failed to upload document');
    } finally {
      setUploading(false);
    }
  };

  const getDocumentIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return <PdfIcon color="error" />;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return <ImageIcon color="primary" />;
      default:
        return <DocumentIcon color="action" />;
    }
  };

  const getDocumentTypeLabel = (type: string) => {
    const typeMap: { [key: string]: string } = {
      'corporate_registry': 'Corporate Registry',
      'share_registry': 'Share Registry',
      'director_registry': 'Director Registry'
    };
    return typeMap[type] || type;
  };

  const getDocumentTypeDescription = (type: string) => {
    const descriptionMap: { [key: string]: string } = {
      'corporate_registry': 'Official company registration documents from the relevant authorities',
      'share_registry': 'Current share registry showing all shareholders and their holdings',
      'director_registry': 'Registry of company directors and their details'
    };
    return descriptionMap[type] || '';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const typeLabel = getDocumentTypeLabel(documentType);
  const typeDescription = getDocumentTypeDescription(documentType);

  return (
    <Box sx={{ p: 2 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <BusinessIcon sx={{ mr: 1, color: 'primary.main' }} />
        <Box>
          <Typography variant="h6">
            Upload {typeLabel}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {typeDescription}
          </Typography>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }}>
          {typeLabel} uploaded successfully!
        </Alert>
      )}

      {/* Upload Section */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <TextField
          label="Document Name"
          value={documentName}
          onChange={(e) => setDocumentName(e.target.value)}
          fullWidth
          sx={{ mb: 2 }}
          required
          placeholder={`e.g., ${typeLabel} - 2024`}
        />

        <TextField
          label="Notes (Optional)"
          value={notes}
          onChange={(e) => setNotes(e.target.value)}
          fullWidth
          multiline
          rows={2}
          sx={{ mb: 2 }}
          placeholder="Any additional notes about this document..."
        />

        <Button
          variant="outlined"
          component="label"
          startIcon={<UploadIcon />}
          fullWidth
          sx={{ mb: 2 }}
        >
          Select {typeLabel} File
          <input
            type="file"
            hidden
            onChange={handleFileSelect}
            accept=".pdf,.jpg,.jpeg,.png,.gif,.doc,.docx"
          />
        </Button>

        {selectedFile && (
          <Typography variant="body2" sx={{ mb: 2, color: 'text.secondary' }}>
            Selected: {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
          </Typography>
        )}

        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="contained"
            onClick={handleUpload}
            disabled={!selectedFile || !documentName || uploading}
            startIcon={uploading ? <CircularProgress size={20} /> : <UploadIcon />}
          >
            {uploading ? 'Uploading...' : `Upload ${typeLabel}`}
          </Button>
          <Button variant="outlined" onClick={onClose}>
            Close
          </Button>
        </Box>
      </Paper>

      {/* Existing Documents */}
      {documents.length > 0 && (
        <>
          <Typography variant="h6" gutterBottom>
            Existing {typeLabel} Documents
          </Typography>
          <List>
            {documents.map((doc) => (
              <ListItem key={doc.document_id}>
                <ListItemIcon>
                  {getDocumentIcon(doc.document_name)}
                </ListItemIcon>
                <ListItemText
                  primary={doc.document_name}
                  secondary={
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Uploaded: {formatDate(doc.uploaded_at)}
                      </Typography>
                      {doc.notes && (
                        <Typography variant="body2" color="text.secondary">
                          Notes: {doc.notes}
                        </Typography>
                      )}
                    </Box>
                  }
                />
              </ListItem>
            ))}
          </List>
        </>
      )}
    </Box>
  );
};

export default CorporateDocumentUpload; 