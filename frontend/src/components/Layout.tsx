import { Box } from '@mui/material';
import Sidebar from './Sidebar';
import { Outlet } from 'react-router-dom';

function Layout() {
  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      <Sidebar />
      <Box 
        component="main" 
        sx={{ 
          flexGrow: 1, 
          p: 4, 
          background: 'linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%)',
          minHeight: '100vh',
          position: 'relative',
          overflow: 'auto'
        }}
      >
        <Outlet />
      </Box>
    </Box>
  );
}

export default Layout; 