import {
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Box,
  Typography,
  Divider,
  Avatar,
} from '@mui/material';
import { useNavigate, useLocation } from 'react-router-dom';
import DashboardIcon from '@mui/icons-material/Dashboard';
import PolicyIcon from '@mui/icons-material/Policy';
import GroupsIcon from '@mui/icons-material/Groups';
import SchoolIcon from '@mui/icons-material/School';
import BusinessIcon from '@mui/icons-material/Business';
import BeachAccessIcon from '@mui/icons-material/BeachAccess';
import BadgeIcon from '@mui/icons-material/Badge';

const drawerWidth = 280;

const menuItems = [
  { path: '/', label: 'Dashboard', icon: DashboardIcon },
  { path: '/policies', label: 'Policies', icon: PolicyIcon },
  { path: '/committees', label: 'Committees', icon: GroupsIcon },
  { path: '/training', label: 'Training', icon: SchoolIcon },
  { path: '/vendors', label: 'Vendors', icon: BusinessIcon },
  { path: '/holidays', label: 'Holidays', icon: BeachAccessIcon },
  { path: '/employees', label: 'Employees', icon: BadgeIcon },
];

function Sidebar() {
  const navigate = useNavigate();
  const location = useLocation();

  return (
    <Drawer
      variant="permanent"
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
          background: '#000000',
          color: '#FFFFFF',
          borderRight: 'none',
          overflow: 'hidden',
        },
      }}
    >
      {/* Header Section */}
      <Box 
        sx={{ 
          p: 3,
          pb: 2,
          background: 'linear-gradient(135deg, #000000 0%, #1a1a1a 100%)',
          position: 'relative',
          '&::after': {
            content: '""',
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            height: '1px',
            background: 'linear-gradient(90deg, transparent 0%, #28E7C5 50%, transparent 100%)',
          }
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Box 
            sx={{ 
              width: 40, 
              height: 40, 
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <img 
              src="/bumba-logo.png" 
              alt="Bumba Logo" 
              style={{ 
                width: '40px', 
                height: '40px', 
                objectFit: 'contain'
              }} 
            />
          </Box>
          <Box>
            <Typography 
              variant="h6" 
              sx={{ 
                color: '#FFFFFF',
                fontWeight: 700,
                letterSpacing: '-0.025em',
                lineHeight: 1.2
              }}
            >
              Bumba Compliance
            </Typography>
            <Typography 
              variant="caption" 
              sx={{ 
                color: '#28E7C5',
                fontSize: '0.75rem',
                fontWeight: 500
              }}
            >
              Management Portal
            </Typography>
          </Box>
        </Box>
      </Box>

      {/* Navigation Menu */}
      <Box sx={{ px: 2, py: 1, flex: 1 }}>
        <Typography 
          variant="caption" 
          sx={{ 
            color: '#28E7C5',
            fontWeight: 600,
            textTransform: 'uppercase',
            letterSpacing: '0.1em',
            px: 2,
            py: 1,
            display: 'block'
          }}
        >
          Navigation
        </Typography>
        
        <List sx={{ p: 0 }}>
          {menuItems.map((item) => {
            const isActive = location.pathname === item.path;
            
            return (
              <ListItem
                button
                key={item.path}
                onClick={() => navigate(item.path)}
                sx={{
                  backgroundColor: isActive 
                    ? 'rgba(40, 231, 197, 0.15)' 
                    : 'transparent',
                  borderRadius: 2,
                  mx: 1,
                  my: 0.5,
                  transition: 'all 0.2s ease-in-out',
                  position: 'relative',
                  overflow: 'hidden',
                  '&:hover': {
                    backgroundColor: isActive 
                      ? 'rgba(40, 231, 197, 0.25)' 
                      : 'rgba(40, 231, 197, 0.1)',
                    transform: 'translateX(4px)',
                  },
                  '&:before': isActive ? {
                    content: '""',
                    position: 'absolute',
                    left: 0,
                    top: '50%',
                    transform: 'translateY(-50%)',
                    width: '3px',
                    height: '60%',
                    background: '#28E7C5',
                    borderRadius: '0 4px 4px 0',
                  } : {},
                  '&:after': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    background: isActive 
                      ? 'rgba(40, 231, 197, 0.08)'
                      : 'transparent',
                    pointerEvents: 'none',
                  }
                }}
              >
                <ListItemIcon 
                  sx={{ 
                    color: isActive ? '#28E7C5' : '#FFFFFF',
                    minWidth: 40,
                    transition: 'color 0.2s ease-in-out'
                  }}
                >
                  <item.icon />
                </ListItemIcon>
                <ListItemText 
                  primary={item.label}
                  sx={{
                    '& .MuiTypography-root': {
                      fontWeight: isActive ? 600 : 500,
                      color: isActive ? '#28E7C5' : '#FFFFFF',
                      fontSize: '0.95rem'
                    }
                  }}
                />
              </ListItem>
            );
          })}
        </List>
      </Box>

      {/* Footer Section */}
      <Box 
        sx={{ 
          p: 2,
          borderTop: '1px solid rgba(40, 231, 197, 0.2)',
          background: 'rgba(40, 231, 197, 0.05)'
        }}
      >
        <Typography 
          variant="caption" 
          sx={{ 
            color: '#28E7C5',
            textAlign: 'center',
            display: 'block',
            fontSize: '0.7rem'
          }}
        >
          © 2024 Governance Portal
        </Typography>
      </Box>
    </Drawer>
  );
}

export default Sidebar; 