// Check if we're running in Tauri (desktop app) or web browser
// Use multiple methods to detect Tauri environment
const isTauri = typeof window !== 'undefined' && (
  (window as any).__TAURI__ !== undefined ||
  (window as any).__TAURI_INTERNALS__ !== undefined ||
  navigator.userAgent.includes('tauri')
);

// Force localhost for desktop app - try multiple approaches
const isDesktop = isTauri || window.location.protocol === 'tauri:';

// For now, always use localhost - we can make this smarter later
const API_BASE_URL = 'http://localhost:3030/api';

// Debug logging
console.log('🔍 API Service Debug:', {
  isTauri,
  isDesktop,
  nodeEnv: process.env.NODE_ENV,
  API_BASE_URL,
  windowTauri: typeof window !== 'undefined' ? !!(window as any).__TAURI__ : 'undefined',
  windowTauriInternals: typeof window !== 'undefined' ? !!(window as any).__TAURI_INTERNALS__ : 'undefined',
  userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'undefined',
  protocol: typeof window !== 'undefined' ? window.location.protocol : 'undefined'
});

interface ApiResponse<T> {
  data?: T;
  error?: string;
}

class ApiService {
  private async request<T>(endpoint: string, options?: RequestInit): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    
    console.log(`🌐 API Request: ${options?.method || 'GET'} ${url}`);
    
    try {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options?.headers,
        },
        ...options,
      });

      console.log(`📡 API Response: ${response.status} ${response.statusText} for ${endpoint}`);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error(`❌ API Error Response:`, errorData);
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log(`✅ API Success for ${endpoint}:`, Array.isArray(data) ? `${data.length} items` : 'data received');
      return data;
    } catch (error) {
      console.error(`💥 API request failed for ${endpoint}:`, error);
      throw error;
    }
  }

  // Health check
  async healthCheck() {
    return this.request<{status: string, database: string}>('/health');
  }

  // Policies
  async getPolicies() {
    return this.request<any[]>('/policies');
  }

  async createPolicy(policy: any) {
    return this.request<any>('/policies', {
      method: 'POST',
      body: JSON.stringify(policy),
    });
  }

  async updatePolicy(id: string, policy: any) {
    return this.request<any>(`/policies/${id}`, {
      method: 'PUT',
      body: JSON.stringify(policy),
    });
  }

  // Members  
  async getMembers() {
    return this.request<any[]>('/members');
  }

  async updateMember(id: string, updates: any) {
    return this.request<any>(`/members/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  // Departments
  async getDepartments() {
    return this.request<any[]>('/departments');
  }

  // Committees
  async getCommittees() {
    return this.request<any[]>('/committees');
  }

  async createCommittee(committee: any) {
    return this.request<any>('/committees', {
      method: 'POST',
      body: JSON.stringify(committee),
    });
  }

  // Meetings
  async getMeetings() {
    return this.request<any[]>('/meetings');
  }

  async createMeeting(meeting: any) {
    return this.request<any>('/meetings', {
      method: 'POST',
      body: JSON.stringify(meeting),
    });
  }

  // Trainings
  async getTrainings() {
    return this.request<any[]>('/trainings');
  }

  async createTraining(training: any) {
    return this.request<any>('/trainings', {
      method: 'POST',
      body: JSON.stringify(training),
    });
  }

  // Vendors
  async getVendors() {
    return this.request<any[]>('/vendors');
  }

  async createVendor(vendor: any) {
    return this.request<any>('/vendors', {
      method: 'POST',
      body: JSON.stringify(vendor),
    });
  }

  // Contract Documents
  async uploadContractDocument(vendorId: string, file: File, documentName?: string, notes?: string) {
    const formData = new FormData();
    formData.append('contract', file);
    if (documentName) formData.append('documentName', documentName);
    if (notes) formData.append('notes', notes);

    const url = `${API_BASE_URL}/vendors/${vendorId}/documents`;
    console.log(`🌐 API Request: POST ${url}`);

    const response = await fetch(url, {
      method: 'POST',
      body: formData,
    });

    console.log(`📡 API Response: ${response.status} ${response.statusText} for upload`);

    if (!response.ok) {
      const error = await response.json().catch(() => ({}));
      console.error(`❌ API Error Response:`, error);
      throw new Error(error.error || 'Failed to upload document');
    }

    const data = await response.json();
    console.log(`✅ API Success for upload:`, data);
    return data;
  }

  async getVendorDocuments(vendorId: string) {
    return this.request<any[]>(`/vendors/${vendorId}/documents`);
  }

  async deleteVendorDocument(vendorId: string, documentId: string) {
    return this.request<any>(`/vendors/${vendorId}/documents/${documentId}`, {
      method: 'DELETE',
    });
  }

  async downloadVendorDocument(vendorId: string, documentId: string) {
    const url = `${API_BASE_URL}/vendors/${vendorId}/documents/${documentId}/download`;
    console.log(`🌐 API Request: GET ${url}`);

    const response = await fetch(url);
    
    console.log(`📡 API Response: ${response.status} ${response.statusText} for download`);

    if (!response.ok) {
      console.error(`❌ API Error: Failed to download document`);
      throw new Error('Failed to download document');
    }

    const blob = await response.blob();
    console.log(`✅ API Success for download: blob received`);
    return blob;
  }

  // Performance Reports
  async getPerformanceReports(memberId: string) {
    return this.request<any[]>(`/members/${memberId}/performance-reports`);
  }

  async createPerformanceReport(memberId: string, report: any) {
    return this.request<any>(`/members/${memberId}/performance-reports`, {
      method: 'POST',
      body: JSON.stringify(report),
    });
  }

  async updatePerformanceReport(memberId: string, reportId: string, report: any) {
    return this.request<any>(`/members/${memberId}/performance-reports/${reportId}`, {
      method: 'PUT',
      body: JSON.stringify(report),
    });
  }

  // Test connection
  async testConnection() {
    return this.request<{message: string}>('/test');
  }
}

  const baseApiService = new ApiService();
  
  // Add missing API methods
  export const apiService = Object.assign(baseApiService, {
    getCommitteeMemberships: () => fetch(`${API_BASE_URL}/committee-memberships`).then(res => res.json()),
    getTrainingSessions: () => fetch(`${API_BASE_URL}/training-sessions`).then(res => res.json()),
    getAttendance: () => fetch(`${API_BASE_URL}/attendance`).then(res => res.json()),
    getHolidays: () => fetch(`${API_BASE_URL}/holidays`).then(res => res.json()),
  });

// Query builder class for chaining
class SupabaseQueryBuilder {
  constructor(private table: string, private columns: string = '*') {}

  private filters: any[] = [];
  private _order: { column: string; ascending: boolean } | null = null;
  private _limit: number | null = null;

  select(columns: string = '*') {
    return new SupabaseQueryBuilder(this.table, columns);
  }

  eq(column: string, value: any) {
    this.filters.push({ type: 'eq', column, value });
    return this;
  }

  order(column: string, options?: { ascending?: boolean }) {
    this._order = { column, ascending: options?.ascending ?? true };
    return this;
  }

  limit(count: number) {
    this._limit = count;
    return this;
  }

  async then(callback: any) {
    try {
      let data = await this.getData();
      
      // Apply filters
      data = this.applyFilters(data);
      
      // Apply ordering
      if (this._order) {
        data = this.applyOrder(data);
      }
      
      // Apply limit
      if (this._limit) {
        data = data.slice(0, this._limit);
      }
      
      return callback({ data, error: null });
    } catch (error) {
      return callback({ data: null, error });
    }
  }

     private async getData() {
     switch(this.table) {
       case 'policies': return apiService.getPolicies();
       case 'members': return apiService.getMembers();
       case 'committees': return apiService.getCommittees();
       case 'trainings': return apiService.getTrainings();
       case 'vendor': return apiService.getVendors();
       case 'meetings': return apiService.getMeetings();
       case 'departments': return apiService.getDepartments();
       case 'committee_memberships': return apiService.getCommitteeMemberships();
       case 'training_sessions': return apiService.getTrainingSessions();
       case 'attendance': return apiService.getAttendance();
       case 'holidays': return apiService.getHolidays();
       default: return [];
     }
   }

  private applyFilters(data: any[]) {
    return data.filter(item => {
      return this.filters.every(filter => {
        switch(filter.type) {
          case 'eq':
            return item[filter.column] === filter.value;
          default:
            return true;
        }
      });
    });
  }

  private applyOrder(data: any[]) {
    if (!this._order) return data;
    
    return [...data].sort((a, b) => {
      const aVal = a[this._order!.column];
      const bVal = b[this._order!.column];
      
      if (aVal < bVal) return this._order!.ascending ? -1 : 1;
      if (aVal > bVal) return this._order!.ascending ? 1 : -1;
      return 0;
    });
  }
}

// Compatibility layer for easier migration from Supabase
export const supabaseCompat = {
  from: (table: string) => ({
    select: (columns = '*') => new SupabaseQueryBuilder(table, columns),
    insert: (data: any) => {
      const insertMethod = async () => {
        switch(table) {
          case 'policies': return apiService.createPolicy(data);
          case 'committees': return apiService.createCommittee(data);
          case 'trainings': return apiService.createTraining(data);
          case 'vendor': return apiService.createVendor(data);
          case 'meetings': return apiService.createMeeting(data);
          default: throw new Error(`Insert for ${table} not supported`);
        }
      };

      return {
        then: async (callback: any) => {
          try {
            const data = await insertMethod();
            return callback({ data, error: null });
          } catch (error) {
            return callback({ data: null, error });
          }
        }
      };
    },
    update: (data: any) => ({
      eq: (column: string, value: any) => {
        const updateMethod = async () => {
          switch(table) {
            case 'members': return apiService.updateMember(value, data);
            case 'policies': return apiService.updatePolicy(value, data);
            default: throw new Error(`Update for ${table} not supported`);
          }
        };

        return {
          then: async (callback: any) => {
            try {
              const data = await updateMethod();
              return callback({ data, error: null });
            } catch (error) {
              return callback({ data: null, error });
            }
          }
        };
      }
    }),
         upsert: (data: any) => {
       return supabaseCompat.from(table).insert(data); // For now, treat upsert as insert
     }
  })
}; 