import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function checkTableExists(tableName: string) {
  const { data, error } = await supabase
    .from(tableName)
    .select('*')
    .limit(1)

  return {
    exists: !error && data !== null,
    error: error?.message
  }
}

async function runDiagnostics() {
  console.log('=== Supabase Structure Diagnostic ===\n')
  
  const requiredTables = [
    'policies', 
    'policy_versions',
    'committees',
    'committee_members',
    'meetings'
  ]

  for (const table of requiredTables) {
    const { exists, error } = await checkTableExists(table)
    console.log(`${table}: ${exists ? '✅ Found' : '❌ Missing'}`)
    if (error) console.log(`   Error: ${error}`)
  }
}

runDiagnostics() 