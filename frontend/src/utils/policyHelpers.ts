export function calculateDaysLeft(nextReview: string): number {
  const nextReviewDate = new Date(nextReview);
  const today = new Date();
  const daysLeft = Math.ceil((nextReviewDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
  return daysLeft;
}

export function getStatusColor(daysLeft: number): string {
  if (daysLeft > 90) return '#4caf50';  // Green for more than 90 days
  if (daysLeft > 15) return '#ff9800';  // Orange for 15-90 days
  if (daysLeft > 0) return '#f44336';   // Red for less than 15 days
  return '#000000';                      // Black for overdue
}

export function getStatusText(daysLeft: number): string {
  if (daysLeft > 90) return `${daysLeft} days left`;
  if (daysLeft > 15) return `${daysLeft} days left (Review soon)`;
  if (daysLeft > 0) return `${daysLeft} days left (Urgent)`;
  return `${Math.abs(daysLeft)} days overdue`;
} 