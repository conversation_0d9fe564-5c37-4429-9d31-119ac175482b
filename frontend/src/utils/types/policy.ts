// Main policy table
/*
create table policies (
  id uuid default uuid_generate_v4() primary key,
  name text not null,
  description text,
  link text,
  author text not null,
  tags text[],
  last_review date not null,
  next_review date not null,
  current_version text not null default '1.0.0',
  created_at timestamp with time zone default now(),
  updated_at timestamp with time zone default now()
);

-- Policy versions table to track version history
create table policy_versions (
  id uuid default uuid_generate_v4() primary key,
  policy_id uuid references policies(id) on delete cascade,
  version text not null,
  changes_description text not null,
  author text not null,
  document_link text,
  created_at timestamp with time zone default now(),
  
  -- Ensure unique versions per policy
  unique(policy_id, version)
);

-- Trigger to update the updated_at timestamp
create trigger set_timestamp
  before update on policies
  for each row
  execute procedure trigger_set_timestamp();
*/

export interface Policy {
  id: string;
  name: string;
  description?: string;
  link?: string;
  author: string;
  tags?: string[];
  last_review: string;
  next_review: string;
  current_version: string;  // Changed from version to current_version
  created_at: string;
  updated_at: string;
}

export interface PolicyVersion {
  id: string;
  policy_id: string;
  version: string;
  changes_description: string;
  author: string;
  document_link?: string;
  created_at: string;
}
