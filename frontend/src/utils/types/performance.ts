export interface PerformanceReport {
  id: number;
  member_id: number;
  review_date: string;
  next_review_date: string;
  kpi_data: KPI[];
  review_notes: string;
  report_document_url: string;
  status: 'pending' | 'completed' | 'overdue';
  created_at: string;
  updated_at: string;
}

export interface KPI {
  name: string;
  description: string;
  target: number;
  actual: number;
  weight: number;
  score: number;
}

export interface MemberPerformanceSummary {
  id: number;
  name: string;
  joining_date: string;
  last_performance_review: string;
  next_performance_review: string;
  performance_review_count: number;
  department_name: string;
  status: 'overdue' | 'due_soon' | 'on_track';
}

export interface PerformanceAlert {
  id: string;
  type: string;
  member_id: number;
  member_name: string;
  department_name: string;
  due_date: string;
  days_remaining: number;
  alert_type: string;
  review_count: number;
  last_review: string;
}