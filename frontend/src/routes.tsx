import { createBrowserRouter } from 'react-router-dom';
import Layout from './components/Layout';
import Committees from './pages/Committees';
import Employees from './pages/Employees';
import Trainings from './pages/Trainings';
import Dashboard from './pages/Dashboard';
import Policies from './pages/Policies';
import Vendors from './pages/Vendors';
import SchoolIcon from '@mui/icons-material/School';
import PersonIcon from '@mui/icons-material/Person';
import GroupsIcon from '@mui/icons-material/Groups';
import DashboardIcon from '@mui/icons-material/Dashboard';
import PolicyIcon from '@mui/icons-material/Policy';
import StorefrontIcon from '@mui/icons-material/Storefront';

import Training from './pages/Training';

export const navigationItems = [
  {
    path: '/',
    label: 'Dashboard',
    icon: DashboardIcon,
    element: <Dashboard />,
  },
  {
    path: '/employees',
    label: 'Employees',
    icon: PersonIcon,
    element: <Employees />,
  },
  {
    path: '/committees',
    label: 'Committees',
    icon: GroupsIcon,
    element: <Committees />,
  },
  {
    path: '/training',
    label: 'Training',
    icon: SchoolIcon,
    element: <Training />,
  },
  {
    path: '/policies',
    label: 'Policies',
    icon: PolicyIcon,
    element: <Policies />,
  },
  {
    path: '/vendors',
    label: 'Vendors',
    icon: StorefrontIcon,
    element: <Vendors />,
  }
];

export const router = createBrowserRouter([
  {
    path: '/',
    element: <Layout />,
    children: [
      {
        path: '',
        element: <Dashboard />,
      },
      {
        path: 'policies',
        element: <Policies />,
      },
      {
        path: 'committees',
        element: <Committees />,
      },
      {
        path: 'training',
        element: <Training />,
      },
      {
        path: 'vendors',
        element: <Vendors />,
      },
      { // Add route for Employees page
        path: 'employees',
        element: <Employees />,
      },
    ],
  },
]); 