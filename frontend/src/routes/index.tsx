import { Routes, Route } from 'react-router-dom';
import Dashboard from '../pages/Dashboard';
import Policies from '../pages/Policies';
import Committees from '../pages/Committees';
import Training from '../pages/Training';
import Vendors from '../pages/Vendors';

function AppRoutes() {
  return (
    <Routes>
      <Route path="/" element={<Dashboard />} />
      <Route path="/policies" element={<Policies />} />
      <Route path="/committees/*" element={<Committees />} />
      <Route path="/training" element={<Training />} />
      <Route path="/vendors" element={<Vendors />} />
    </Routes>
  );
}

export default AppRoutes; 