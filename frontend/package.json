{"name": "frontend", "version": "0.1.0", "private": true, "type": "module", "main": "main.js", "dependencies": {"@date-io/date-fns": "^2.17.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^5.15.6", "@mui/material": "^5.16.14", "@mui/x-date-pickers": "^6.20.2", "@supabase/supabase-js": "^2.39.3", "date-fns": "^2.28.0", "pdfjs-dist": "^5.3.93", "react": "^18.2.0", "react-dom": "^18.2.0", "react-pdf": "^10.0.1", "react-router-dom": "^6.21.3", "web-vitals": "^2.1.4"}, "devDependencies": {"@tauri-apps/cli": "^2.0.0-beta.19", "@vitejs/plugin-react": "^4.3.4", "vite": "^6.0.11", "vite-tsconfig-paths": "^5.1.4"}, "scripts": {"start": "vite", "build": "vite build", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build"}}