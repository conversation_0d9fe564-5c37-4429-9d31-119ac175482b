import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react(), (await import('vite-tsconfig-paths')).default()],
  server: {
    port: 3001,
    strictPort: true,
    host: true,
  },
  clearScreen: false,
  envPrefix: ['VITE_', 'TAURI_'],
  root: './',
  build: {
    outDir: 'dist'
  },
  optimizeDeps: {
    include: ['pdfjs-dist']
  }
})