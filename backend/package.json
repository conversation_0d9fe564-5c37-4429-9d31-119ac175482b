{"name": "governance-app-backend", "version": "1.0.0", "type": "module", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "nodemon --exec node --loader ts-node/esm src/server.ts", "dev:simple": "nodemon --exec node --loader ts-node/esm src/simple-server.ts", "build": "tsc", "db:reset": "docker-compose down database && docker-compose up database -d", "db:seed": "node scripts/seed.js"}, "dependencies": {"@types/multer": "^2.0.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "multer": "^2.0.1", "pg": "^8.16.3"}, "devDependencies": {"@types/cors": "^2.8.15", "@types/express": "^4.17.20", "@types/node": "^20.8.7", "@types/pg": "^8.15.4", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}}