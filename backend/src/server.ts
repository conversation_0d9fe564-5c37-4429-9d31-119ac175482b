import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { query, getClient } from './database.js';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

dotenv.config({ path: join(__dirname, '../.env') });

const app = express();
const port = process.env.PORT || 3030;

app.use(cors({
  origin: true, // Allow all origins in development
  credentials: false,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json());

// Create uploads directory if it doesn't exist
const uploadsDir = join(__dirname, '../uploads/contracts');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadsDir);
  },
  filename: (req, file, cb) => {
    // Create unique filename with timestamp and original name
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    const baseName = path.basename(file.originalname, ext);
    cb(null, `${baseName}-${uniqueSuffix}${ext}`);
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Only allow PDF files
    if (file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new Error('Only PDF files are allowed'));
    }
  }
});

// Serve uploaded files statically with proper headers for PDF viewing
app.use('/api/contracts/files', (req, res, next) => {
  // Set headers for PDF viewing in browser
  res.setHeader('Content-Type', 'application/pdf');
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  res.setHeader('Cache-Control', 'public, max-age=86400'); // Cache for 1 day
  next();
}, express.static(uploadsDir));

// Health check endpoint
app.get('/api/health', async (req, res) => {
  try {
    await query('SELECT 1');
    res.json({ status: 'healthy', database: 'connected' });
  } catch (error) {
    res.status(500).json({ status: 'unhealthy', database: 'disconnected' });
  }
});

// Debug endpoint to check database schema
app.get('/api/debug/tables', async (req, res) => {
  try {
    const tables = await query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);
    
    const views = await query(`
      SELECT table_name 
      FROM information_schema.views 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);
    
    res.json({
      tables: tables.rows.map((r: any) => r.table_name),
      views: views.rows.map((r: any) => r.table_name),
      database_connected: true
    });
  } catch (error: any) {
    console.error('Database debug error:', error);
    res.json({
      error: error.message,
      database_connected: false
    });
  }
});

// POLICIES ROUTES
app.get('/api/policies', async (req, res) => {
  try {
    const result = await query(`
      SELECT 
        id,
        policy_name as name,
        current_version,
        link,
        last_review,
        next_review,
        author,
        created_at,
        updated_at
      FROM policies 
      ORDER BY created_at DESC
    `);
    res.json(result.rows);
  } catch (err) {
    console.error('Error fetching policies:', err);
    res.status(500).json({ error: 'Failed to fetch policies' });
  }
});

app.post('/api/policies', async (req, res) => {
  try {
    const { policy_name, current_version, link, last_review, next_review, author } = req.body;
    const result = await query(`
      INSERT INTO policies (policy_name, current_version, link, last_review, next_review, author)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `, [policy_name, current_version, link, last_review, next_review, author]);
    
    res.status(201).json(result.rows[0]);
  } catch (err) {
    console.error('Error creating policy:', err);
    res.status(500).json({ error: 'Failed to create policy' });
  }
});

app.put('/api/policies/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { policy_name, current_version, link, last_review, next_review, author } = req.body;
    
    const result = await query(`
      UPDATE policies 
      SET policy_name = $1, current_version = $2, link = $3, last_review = $4, next_review = $5, author = $6
      WHERE id = $7 
      RETURNING *
    `, [policy_name, current_version, link, last_review, next_review, author, id]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Policy not found' });
    }
    
    res.json(result.rows[0]);
  } catch (err) {
    console.error('Error updating policy:', err);
    res.status(500).json({ error: 'Failed to update policy' });
  }
});

app.delete('/api/policies/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const result = await query(`
      DELETE FROM policies 
      WHERE id = $1 
      RETURNING *
    `, [id]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Policy not found' });
    }
    
    res.json({ message: 'Policy deleted successfully' });
  } catch (err) {
    console.error('Error deleting policy:', err);
    res.status(500).json({ error: 'Failed to delete policy' });
  }
});

// MEMBERS ROUTES
app.get('/api/members', async (req, res) => {
  try {
    const result = await query(`
      SELECT m.*, d.name as department_name 
      FROM members m 
      LEFT JOIN departments d ON m.department_id = d.id 
      ORDER BY m.name
    `);
    res.json(result.rows);
  } catch (err) {
    console.error('Error fetching members:', err);
    res.status(500).json({ error: 'Failed to fetch members' });
  }
});

app.put('/api/members/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;
    
    // Build dynamic SET clause
    const updateFields = Object.keys(updates).filter(key => updates[key] !== undefined);
    const setClause = updateFields.map((key, index) => `${key} = $${index + 2}`).join(', ');
    const values = [id, ...updateFields.map(key => updates[key])];
    
    if (updateFields.length === 0) {
      return res.status(400).json({ error: 'No fields to update' });
    }
    
    const result = await query(`
      UPDATE members 
      SET ${setClause}
      WHERE id = $1 
      RETURNING *
    `, values);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Member not found' });
    }
    
    res.json(result.rows[0]);
  } catch (err) {
    console.error('Error updating member:', err);
    res.status(500).json({ error: 'Failed to update member' });
  }
});

// DEPARTMENTS ROUTES
app.get('/api/departments', async (req, res) => {
  try {
    const result = await query('SELECT * FROM departments ORDER BY name');
    res.json(result.rows);
  } catch (err) {
    console.error('Error fetching departments:', err);
    res.status(500).json({ error: 'Failed to fetch departments' });
  }
});

// COMMITTEES ROUTES
app.get('/api/committees', async (req, res) => {
  try {
    const result = await query('SELECT * FROM committees ORDER BY committee_name');
    res.json(result.rows);
  } catch (err) {
    console.error('Error fetching committees:', err);
    res.status(500).json({ error: 'Failed to fetch committees' });
  }
});

app.post('/api/committees', async (req, res) => {
  try {
    const { committee_name, description } = req.body;
    const result = await query(`
      INSERT INTO committees (committee_name, description)
      VALUES ($1, $2)
      RETURNING *
    `, [committee_name, description]);
    
    res.status(201).json(result.rows[0]);
  } catch (err) {
    console.error('Error creating committee:', err);
    res.status(500).json({ error: 'Failed to create committee' });
  }
});

// MEETINGS ROUTES
app.get('/api/meetings', async (req, res) => {
  try {
    const result = await query(`
      SELECT m.*, c.committee_name 
      FROM meetings m 
      LEFT JOIN committees c ON m.committee_id = c.id 
      ORDER BY m.date DESC
    `);
    res.json(result.rows);
  } catch (err) {
    console.error('Error fetching meetings:', err);
    res.status(500).json({ error: 'Failed to fetch meetings' });
  }
});

app.post('/api/meetings', async (req, res) => {
  try {
    const { committee_id, date, meeting_type, topics_discussed, agenda_link, minutes_link, attendees, guests } = req.body;
    const result = await query(`
      INSERT INTO meetings (committee_id, date, attendees, guests, meeting_type, agenda_link, minutes_link, topics_discussed)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `, [
      committee_id, 
      date, 
      JSON.stringify(attendees || []), 
      JSON.stringify(guests || []), 
      meeting_type, 
      agenda_link, 
      minutes_link, 
      topics_discussed
    ]);
    
    res.status(201).json(result.rows[0]);
  } catch (err) {
    console.error('Error creating meeting:', err);
    res.status(500).json({ error: 'Failed to create meeting' });
  }
});

// TRAININGS ROUTES
app.get('/api/trainings', async (req, res) => {
  try {
    const result = await query('SELECT * FROM trainings ORDER BY name');
    res.json(result.rows);
  } catch (err) {
    console.error('Error fetching trainings:', err);
    res.status(500).json({ error: 'Failed to fetch trainings' });
  }
});

app.post('/api/trainings', async (req, res) => {
  try {
    const { name, description, requires_refresh, refresh_frequency_months, is_mandatory, assigned_departments } = req.body;
    const result = await query(`
      INSERT INTO trainings (name, description, requires_refresh, refresh_frequency_months, is_mandatory, assigned_departments)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `, [name, description, requires_refresh, refresh_frequency_months, is_mandatory, JSON.stringify(assigned_departments)]);
    
    res.status(201).json(result.rows[0]);
  } catch (err) {
    console.error('Error creating training:', err);
    res.status(500).json({ error: 'Failed to create training' });
  }
});

// COMMITTEE MEMBERSHIPS ROUTES
app.get('/api/committee-memberships', async (req, res) => {
  try {
    const result = await query(`
      SELECT 
        cm.id,
        cm.member_id,
        cm.committee_id,
        cm.role,
        cm.committee_member_since,
        cm.member_until,
        cm.reason_for_leaving,
        cm.status,
        cm.created_at,
        cm.updated_at,
        c.committee_name,
        json_build_object(
          'id', m.id,
          'name', m.name,
          'email', m.email,
          'phone', m.phone,
          'created_at', m.created_at
        ) as members
      FROM committee_memberships cm 
      LEFT JOIN committees c ON cm.committee_id = c.id 
      LEFT JOIN members m ON cm.member_id = m.id 
      ORDER BY c.committee_name, m.name
    `);
    res.json(result.rows);
  } catch (err) {
    console.error('Error fetching committee memberships:', err);
    res.status(500).json({ error: 'Failed to fetch committee memberships' });
  }
});

app.post('/api/committee-memberships', async (req, res) => {
  try {
    const { committee_id, member_id, role, committee_member_since } = req.body;
    const result = await query(`
      INSERT INTO committee_memberships (committee_id, member_id, role, committee_member_since)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    `, [committee_id, member_id, role, committee_member_since]);
    
    res.status(201).json(result.rows[0]);
  } catch (err) {
    console.error('Error creating committee membership:', err);
    res.status(500).json({ error: 'Failed to create committee membership' });
  }
});

// TRAINING SESSIONS ROUTES
app.get('/api/training-sessions', async (req, res) => {
  try {
    const result = await query(`
      SELECT ts.*, t.name as training_name
      FROM training_sessions ts 
      LEFT JOIN trainings t ON ts.training_id = t.id 
      ORDER BY ts.session_date DESC
    `);
    res.json(result.rows);
  } catch (err) {
    console.error('Error fetching training sessions:', err);
    res.status(500).json({ error: 'Failed to fetch training sessions' });
  }
});

// ATTENDANCE ROUTES
app.get('/api/attendance', async (req, res) => {
  try {
    const result = await query(`
      SELECT a.*, m.name as member_name, ts.session_date, t.name as training_name
      FROM attendance a 
      LEFT JOIN members m ON a.member_id = m.id 
      LEFT JOIN training_sessions ts ON a.training_session_id = ts.id
      LEFT JOIN trainings t ON ts.training_id = t.id
      ORDER BY ts.session_date DESC
    `);
    res.json(result.rows);
  } catch (err) {
    console.error('Error fetching attendance:', err);
    res.status(500).json({ error: 'Failed to fetch attendance' });
  }
});

// VENDORS ROUTES
app.get('/api/vendors', async (req, res) => {
  try {
    // First try the new normalized schema
    try {
      const result = await query(`
        SELECT 
          vendor_id as id,
          company_name,
          website,
          vendor_type,
          service_type,
          lead_manager,
          lead_manager_email,
          countries_of_operation,
          incorporation_date,
          registration_number,
          edd_required,
          vendor_active as active,
          annual_cost,
          account_manager_assigned,
          account_manager_name,
          account_manager_email,
          account_manager_phone,
          contract_id,
          contract_active,
          contract_start_date,
          contract_renewal_date,
          contract_end_date,
          contract_notes,
          contract_continuous,
          contract_status,
          review_id,
          last_review_date,
          next_review_date,
          review_notes,
          review_completed,
          created_at,
          updated_at
        FROM vw_vendor_overview 
        ORDER BY company_name
      `);
      res.json(result.rows);
      return;
    } catch (newSchemaError) {
      console.log('New schema not available, trying old schema...');
      
      // Fallback to old vendor table if it exists
      try {
        const result = await query('SELECT * FROM vendor ORDER BY name');
        // Transform old schema to match new expected format
        const transformedRows = result.rows.map((row: any) => ({
          id: row.id,
          company_name: row.name || row.company_name,
          website: row.website,
          vendor_type: row.category || row.vendor_type || 'other',
          service_type: row.service_type || 'other',
          lead_manager: row.contact_person || row.lead_manager,
          lead_manager_email: row.email || row.lead_manager_email,
          countries_of_operation: [],
          incorporation_date: null,
          registration_number: null,
          edd_required: false,
          active: true,
          contract_id: null,
          contract_active: false,
          contract_start_date: row.contract_start,
          contract_renewal_date: null,
          contract_end_date: row.contract_end,
          contract_notes: row.notes,
          contract_status: 'inactive',
          created_at: row.created_at,
          updated_at: row.updated_at
        }));
        res.json(transformedRows);
        return;
      } catch (oldSchemaError) {
        console.log('Old schema also not available, returning empty array');
        // If no vendor table exists at all, return empty array
        res.json([]);
        return;
      }
    }
  } catch (err) {
    console.error('Error fetching vendors:', err);
    res.status(500).json({ error: 'Failed to fetch vendors' });
  }
});

app.get('/api/vendors/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await query(`
      SELECT 
        vendor_id as id,
        company_name,
        website,
        vendor_type,
        service_type,
        lead_manager,
        lead_manager_email,
        countries_of_operation,
        incorporation_date,
        registration_number,
        edd_required,
        vendor_active as active,
        annual_cost,
        account_manager_assigned,
        account_manager_name,
        account_manager_email,
        account_manager_phone,
        contract_id,
        contract_active,
        contract_start_date,
        contract_renewal_date,
        contract_end_date,
        contract_notes,
        contract_continuous,
        contract_status,
        review_id,
        last_review_date,
        next_review_date,
        review_notes,
        review_completed,
        created_at,
        updated_at
      FROM vw_vendor_overview 
      WHERE vendor_id = $1
    `, [id]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Vendor not found' });
    }
    
    res.json(result.rows[0]);
  } catch (err) {
    console.error('Error fetching vendor:', err);
    res.status(500).json({ error: 'Failed to fetch vendor' });
  }
});

app.post('/api/vendors', async (req, res) => {
  try {
    const { 
      company_name, 
      website, 
      vendor_type, 
      service_type, 
      lead_manager,
      annual_cost,
      // Account Manager fields
      account_manager_assigned,
      account_manager_name,
      account_manager_email,
      account_manager_phone,
      contract_start_date,
      contract_end_date,
      contract_notes
    } = req.body;

    // Try new schema first
    try {
      const client = await getClient();
      await client.query('BEGIN');
      
      // Get or create lead manager
      let lead_manager_id = null;
      if (lead_manager) {
        const managerResult = await client.query(`
          INSERT INTO lead_managers (manager_name, email, department) 
          VALUES ($1, $2, 'Unknown') 
          ON CONFLICT (manager_name) DO UPDATE SET manager_name = EXCLUDED.manager_name
          RETURNING manager_id
        `, [lead_manager, `${lead_manager.toLowerCase().replace(' ', '.')}@company.com`]);
        lead_manager_id = managerResult.rows[0].manager_id;
      }

      // Insert vendor
      const vendorResult = await client.query(`
        INSERT INTO vendors (
          company_name, website, vendor_type, service_type, 
          lead_manager_id, edd_required, annual_cost,
          account_manager_assigned, account_manager_name, 
          account_manager_email, account_manager_phone
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
        RETURNING vendor_id
      `, [
        company_name, 
        website, 
        vendor_type || 'low', 
        service_type || 'it_infrastructure',
        lead_manager_id,
        false,
        annual_cost || null,
        account_manager_assigned ?? true,
        account_manager_name || null,
        account_manager_email || null,
        account_manager_phone || null
      ]);
      
      const vendor_id = vendorResult.rows[0].vendor_id;

      // Insert contract if provided
      if (contract_start_date || contract_end_date) {
        await client.query(`
          INSERT INTO contracts (vendor_id, active, start_date, end_date, notes)
          VALUES ($1, $2, $3, $4, $5)
        `, [vendor_id, true, contract_start_date, contract_end_date, contract_notes]);
      }

      await client.query('COMMIT');
      client.release();
      
      res.status(201).json({ id: vendor_id, company_name, message: 'Vendor created successfully' });
    } catch (newSchemaError) {
      console.log('New schema not available, trying old schema...');
      
      // Fallback to old vendor table
      const result = await query(`
        INSERT INTO vendor (name, category, contact_person, email, notes, contract_start, contract_end)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING *
      `, [
        company_name, 
        vendor_type || 'Other', 
        lead_manager || 'Unknown',
        lead_manager + '@company.com',
        contract_notes,
        contract_start_date,
        contract_end_date
      ]);
      
      res.status(201).json(result.rows[0]);
    }
  } catch (err) {
    console.error('Error creating vendor:', err);
    res.status(500).json({ error: 'Failed to create vendor' });
  }
});

app.put('/api/vendors/:id', async (req, res) => {
  const client = await getClient();
  try {
    await client.query('BEGIN');
    
    const { id } = req.params;
    const { 
      company_name, 
      website, 
      vendor_type, 
      service_type, 
      lead_manager,
      countries_of_operation,
      incorporation_date,
      registration_number,
      edd_required,
      active,
      annual_cost,
      // Account Manager fields
      account_manager_assigned,
      account_manager_name,
      account_manager_email,
      account_manager_phone,
      // Contract fields
      contract_active,
      contract_start_date,
      contract_renewal_date,
      contract_end_date,
      contract_notes,
      contract_continuous
    } = req.body;

    // Get or create lead manager
    let lead_manager_id = null;
    if (lead_manager) {
      const managerResult = await client.query(`
        INSERT INTO lead_managers (manager_name, email, department) 
        VALUES ($1, $2, 'Unknown') 
        ON CONFLICT (manager_name) DO UPDATE SET manager_name = EXCLUDED.manager_name
        RETURNING manager_id
      `, [lead_manager, `${lead_manager.toLowerCase().replace(' ', '.')}@company.com`]);
      lead_manager_id = managerResult.rows[0].manager_id;
    }

    // Update vendor
    const result = await client.query(`
      UPDATE vendors 
      SET company_name = $1, website = $2, vendor_type = $3, service_type = $4,
          lead_manager_id = $5, countries_of_operation = $6, incorporation_date = $7,
          registration_number = $8, edd_required = $9, active = $10, annual_cost = $11,
          account_manager_assigned = $12, account_manager_name = $13, 
          account_manager_email = $14, account_manager_phone = $15, updated_at = NOW()
      WHERE vendor_id = $16 
      RETURNING vendor_id
    `, [
      company_name, website, vendor_type, service_type,
      lead_manager_id, countries_of_operation, incorporation_date,
      registration_number, edd_required, active, annual_cost,
      account_manager_assigned ?? true, account_manager_name || null,
      account_manager_email || null, account_manager_phone || null, id
    ]);
    
    if (result.rows.length === 0) {
      await client.query('ROLLBACK');
      return res.status(404).json({ error: 'Vendor not found' });
    }

    // Update or create contract if contract data is provided
    if (contract_start_date || contract_end_date || contract_renewal_date || contract_active !== undefined || contract_continuous !== undefined) {
      // Check if contract exists
      const existingContract = await client.query(`
        SELECT contract_id FROM contracts WHERE vendor_id = $1 ORDER BY created_at DESC LIMIT 1
      `, [id]);

      if (existingContract.rows.length > 0) {
        // Update existing contract
        await client.query(`
          UPDATE contracts 
          SET active = $1, start_date = $2, renewal_date = $3, end_date = $4, notes = $5, continuous = $6, updated_at = NOW()
          WHERE contract_id = $7
        `, [
          contract_active ?? true,
          contract_start_date || null,
          contract_renewal_date || null,
          contract_continuous ? null : (contract_end_date || null), // Clear end date for continuous contracts
          contract_notes || null,
          contract_continuous ?? false,
          existingContract.rows[0].contract_id
        ]);
      } else {
        // Create new contract
        await client.query(`
          INSERT INTO contracts (vendor_id, active, start_date, renewal_date, end_date, notes, continuous)
          VALUES ($1, $2, $3, $4, $5, $6, $7)
        `, [
          id,
          contract_active ?? true,
          contract_start_date || null,
          contract_renewal_date || null,
          contract_continuous ? null : (contract_end_date || null), // Clear end date for continuous contracts
          contract_notes || null,
          contract_continuous ?? false
        ]);
      }
    }

    await client.query('COMMIT');
    
    // Return updated vendor using the view
    const updatedResult = await query(`
      SELECT 
        vendor_id as id,
        company_name,
        website,
        vendor_type,
        service_type,
        lead_manager,
        lead_manager_email,
        countries_of_operation,
        incorporation_date,
        registration_number,
        edd_required,
        vendor_active as active,
        annual_cost,
        account_manager_assigned,
        account_manager_name,
        account_manager_email,
        account_manager_phone,
        contract_id,
        contract_active,
        contract_start_date,
        contract_renewal_date,
        contract_end_date,
        contract_notes,
        contract_continuous,
        contract_status,
        review_id,
        last_review_date,
        next_review_date,
        review_notes,
        review_completed,
        created_at,
        updated_at
      FROM vw_vendor_overview 
      WHERE vendor_id = $1
    `, [id]);
    
    res.json(updatedResult.rows[0]);
  } catch (err) {
    await client.query('ROLLBACK');
    console.error('Error updating vendor:', err);
    res.status(500).json({ error: 'Failed to update vendor' });
  } finally {
    client.release();
  }
});

app.delete('/api/vendors/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await query('DELETE FROM vendors WHERE vendor_id = $1 RETURNING vendor_id', [id]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Vendor not found' });
    }
    
    res.json({ message: 'Vendor deleted successfully' });
  } catch (err) {
    console.error('Error deleting vendor:', err);
    res.status(500).json({ error: 'Failed to delete vendor' });
  }
});

// Additional vendor endpoints for the new schema
app.get('/api/vendors/:id/risk', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await query(`
      SELECT * FROM vw_vendor_risk_summary 
      WHERE vendor_id = $1
    `, [id]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Vendor risk data not found' });
    }
    
    res.json(result.rows[0]);
  } catch (err) {
    console.error('Error fetching vendor risk:', err);
    res.status(500).json({ error: 'Failed to fetch vendor risk data' });
  }
});

app.get('/api/vendors/:id/ownership', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await query(`
      SELECT * FROM vw_vendor_ownership 
      WHERE vendor_id = $1
    `, [id]);
    
    res.json(result.rows);
  } catch (err) {
    console.error('Error fetching vendor ownership:', err);
    res.status(500).json({ error: 'Failed to fetch vendor ownership data' });
  }
});

app.get('/api/vendor-alerts', async (req, res) => {
  try {
    // Get all vendors with contract information
    const vendorsResult = await query(`
      SELECT 
        vendor_id as id,
        company_name,
        vendor_type,
        contract_active,
        contract_end_date,
        contract_renewal_date,
        contract_continuous,
        contract_status
      FROM vw_vendor_overview 
      WHERE contract_end_date IS NOT NULL OR contract_renewal_date IS NOT NULL
      ORDER BY company_name
    `);

    const vendors = vendorsResult.rows;
    const notifications: any[] = [];
    const today = new Date();

    vendors.forEach((vendor: any) => {
      try {
        // Only check contract end dates for non-continuous contracts
        if (!vendor.contract_continuous && vendor.contract_end_date) {
          const contractEndDate = new Date(vendor.contract_end_date);
          const daysUntilContractEnd = Math.ceil(
            (contractEndDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
          );
          
          if (daysUntilContractEnd <= 0) {
            // Overdue contracts - Black
            notifications.push({
              id: `overdue-${vendor.id}`,
              type: 'renewal',
              vendor_id: vendor.id,
              vendor_name: vendor.company_name,
              due_date: vendor.contract_end_date,
              days_remaining: daysUntilContractEnd,
              severity: 'error',
              alert_type: 'overdue',
              risk_level: vendor.vendor_type,
              contract_active: vendor.contract_active,
              days_overdue: Math.abs(daysUntilContractEnd)
            });
          } else if (daysUntilContractEnd <= 30) {
            // 1 month or less - Red
            notifications.push({
              id: `urgent-${vendor.id}`,
              type: 'renewal',
              vendor_id: vendor.id,
              vendor_name: vendor.company_name,
              due_date: vendor.contract_end_date,
              days_remaining: daysUntilContractEnd,
              severity: 'error',
              alert_type: 'urgent',
              risk_level: vendor.vendor_type,
              contract_active: vendor.contract_active
            });
          } else if (daysUntilContractEnd <= 90) {
            // 3 months or less - Yellow/Orange
            notifications.push({
              id: `warning-${vendor.id}`,
              type: 'renewal',
              vendor_id: vendor.id,
              vendor_name: vendor.company_name,
              due_date: vendor.contract_end_date,
              days_remaining: daysUntilContractEnd,
              severity: 'warning',
              alert_type: 'warning',
              risk_level: vendor.vendor_type,
              contract_active: vendor.contract_active
            });
          }
        }
      } catch (e) {
        console.error(`Error processing dates for vendor ${vendor.company_name}:`, e);
      }
    });

    // Sort notifications by days remaining (most urgent first)
    const sortedNotifications = notifications.sort((a: any, b: any) => {
      return a.days_remaining - b.days_remaining;
    });

    // Calculate summary statistics
    const summary = {
      total_alerts: sortedNotifications.length,
      overdue_contracts: sortedNotifications.filter(n => n.alert_type === 'overdue').length,
      urgent_contracts: sortedNotifications.filter(n => n.alert_type === 'urgent').length,
      warning_contracts: sortedNotifications.filter(n => n.alert_type === 'warning').length,
      contracts_expiring_30_days: sortedNotifications.filter(n => n.days_remaining <= 30 && n.days_remaining > 0).length,
      contracts_expiring_90_days: sortedNotifications.filter(n => n.days_remaining <= 90 && n.days_remaining > 0).length,
      high_risk_vendors_affected: sortedNotifications.filter(n => n.risk_level === 'critical' || n.risk_level === 'high').length
    };
    
    res.json({
      alerts: sortedNotifications,
      summary: summary,
      generated_at: new Date().toISOString()
    });
  } catch (err) {
    console.error('Error fetching vendor alerts:', err);
    res.status(500).json({ error: 'Failed to fetch vendor alerts' });
  }
});

// CONTRACT DOCUMENTS ROUTES
// Upload contract document for a vendor
app.post('/api/vendors/:id/documents', upload.single('contract'), async (req, res) => {
  try {
    const { id } = req.params;
    const { documentName, notes } = req.body;
    
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    // Check if vendor exists
    const vendorCheck = await query('SELECT vendor_id FROM vendors WHERE vendor_id = $1', [id]);
    if (vendorCheck.rows.length === 0) {
      // Clean up uploaded file if vendor doesn't exist
      fs.unlinkSync(req.file.path);
      return res.status(404).json({ error: 'Vendor not found' });
    }

    // Insert document record
    const result = await query(`
      INSERT INTO documents (vendor_id, doc_type, document_name, file_path, notes)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `, [
      id,
      'contract',
      documentName || req.file.originalname,
      req.file.filename, // Store just the filename, not full path
      notes || null
    ]);

    res.status(201).json({
      document: result.rows[0],
      message: 'Contract document uploaded successfully'
    });
  } catch (err) {
    console.error('Error uploading contract document:', err);
    // Clean up uploaded file on error
    if (req.file) {
      try {
        fs.unlinkSync(req.file.path);
      } catch (unlinkErr) {
        console.error('Error deleting uploaded file:', unlinkErr);
      }
    }
    res.status(500).json({ error: 'Failed to upload contract document' });
  }
});

// Get all documents for a vendor
app.get('/api/vendors/:id/documents', async (req, res) => {
  try {
    const { id } = req.params;
    
    const result = await query(`
      SELECT 
        document_id,
        doc_type,
        document_name,
        file_path,
        url,
        expiry_date,
        uploaded_at,
        notes
      FROM documents 
      WHERE vendor_id = $1 
      ORDER BY uploaded_at DESC
    `, [id]);

    res.json(result.rows);
  } catch (err) {
    console.error('Error fetching vendor documents:', err);
    res.status(500).json({ error: 'Failed to fetch vendor documents' });
  }
});

// Delete a contract document
app.delete('/api/vendors/:vendorId/documents/:documentId', async (req, res) => {
  try {
    const { vendorId, documentId } = req.params;
    
    // Get document info before deleting
    const documentResult = await query(`
      SELECT file_path FROM documents 
      WHERE document_id = $1 AND vendor_id = $2
    `, [documentId, vendorId]);
    
    if (documentResult.rows.length === 0) {
      return res.status(404).json({ error: 'Document not found' });
    }
    
    const filePath = documentResult.rows[0].file_path;
    
    // Delete from database
    await query(`
      DELETE FROM documents 
      WHERE document_id = $1 AND vendor_id = $2
    `, [documentId, vendorId]);
    
    // Delete physical file if it exists
    if (filePath) {
      const fullFilePath = join(uploadsDir, filePath);
      try {
        if (fs.existsSync(fullFilePath)) {
          fs.unlinkSync(fullFilePath);
        }
      } catch (fileErr) {
        console.error('Error deleting physical file:', fileErr);
        // Don't fail the request if file deletion fails
      }
    }
    
    res.json({ message: 'Document deleted successfully' });
  } catch (err) {
    console.error('Error deleting contract document:', err);
    res.status(500).json({ error: 'Failed to delete contract document' });
  }
});

// Download a contract document
app.get('/api/vendors/:vendorId/documents/:documentId/download', async (req, res) => {
  try {
    const { vendorId, documentId } = req.params;
    
    const result = await query(`
      SELECT file_path, document_name FROM documents 
      WHERE document_id = $1 AND vendor_id = $2
    `, [documentId, vendorId]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Document not found' });
    }
    
    const { file_path, document_name } = result.rows[0];
    
    if (!file_path) {
      return res.status(404).json({ error: 'File not found' });
    }
    
    const fullFilePath = join(uploadsDir, file_path);
    
    if (!fs.existsSync(fullFilePath)) {
      return res.status(404).json({ error: 'Physical file not found' });
    }
    
    res.download(fullFilePath, document_name);
  } catch (err) {
    console.error('Error downloading document:', err);
    res.status(500).json({ error: 'Failed to download document' });
  }
});

// VENDOR REVIEW ROUTES

// Get vendor review information
app.get('/api/vendors/:id/review', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Get the latest review for the vendor
    const result = await query(`
      SELECT review_id, vendor_id, last_review_date, next_review_date, review_notes, completed, created_at, updated_at
      FROM reviews 
      WHERE vendor_id = $1 
      ORDER BY created_at DESC 
      LIMIT 1
    `, [id]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'No review found for this vendor' });
    }
    
    res.json(result.rows[0]);
  } catch (err) {
    console.error('Error fetching vendor review:', err);
    res.status(500).json({ error: 'Failed to fetch vendor review' });
  }
});

// Create or update vendor review
app.post('/api/vendors/:id/review', async (req, res) => {
  try {
    const { id } = req.params;
    const { last_review_date, next_review_date, review_notes, completed } = req.body;
    
    // Check if vendor exists
    const vendorCheck = await query('SELECT vendor_id FROM vendors WHERE vendor_id = $1', [id]);
    if (vendorCheck.rows.length === 0) {
      return res.status(404).json({ error: 'Vendor not found' });
    }
    
    // Calculate next review date if not provided (default to 1 year from last review)
    let calculatedNextReviewDate = next_review_date;
    if (last_review_date && !next_review_date) {
      const lastReview = new Date(last_review_date);
      lastReview.setFullYear(lastReview.getFullYear() + 1);
      calculatedNextReviewDate = lastReview.toISOString().split('T')[0];
    }
    
    // Insert new review record
    const result = await query(`
      INSERT INTO reviews (vendor_id, last_review_date, next_review_date, review_notes, completed)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `, [
      id,
      last_review_date || null,
      calculatedNextReviewDate || null,
      review_notes || null,
      completed ?? false
    ]);
    
    res.status(201).json({
      review: result.rows[0],
      message: 'Vendor review created successfully'
    });
  } catch (err) {
    console.error('Error creating vendor review:', err);
    res.status(500).json({ error: 'Failed to create vendor review' });
  }
});

// Update vendor review
app.put('/api/vendors/:id/review/:reviewId', async (req, res) => {
  try {
    const { id, reviewId } = req.params;
    const { last_review_date, next_review_date, review_notes, completed } = req.body;
    
    // Calculate next review date if not provided (default to 1 year from last review)
    let calculatedNextReviewDate = next_review_date;
    if (last_review_date && !next_review_date) {
      const lastReview = new Date(last_review_date);
      lastReview.setFullYear(lastReview.getFullYear() + 1);
      calculatedNextReviewDate = lastReview.toISOString().split('T')[0];
    }
    
    const result = await query(`
      UPDATE reviews 
      SET last_review_date = $1, next_review_date = $2, review_notes = $3, completed = $4, updated_at = NOW()
      WHERE review_id = $5 AND vendor_id = $6
      RETURNING *
    `, [
      last_review_date || null,
      calculatedNextReviewDate || null,
      review_notes || null,
      completed ?? false,
      reviewId,
      id
    ]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Review not found' });
    }
    
    res.json({
      review: result.rows[0],
      message: 'Vendor review updated successfully'
    });
  } catch (err) {
    console.error('Error updating vendor review:', err);
    res.status(500).json({ error: 'Failed to update vendor review' });
  }
});

// EDD SHAREHOLDERS ROUTES

// Get all shareholders for a vendor
app.get('/api/vendors/:id/shareholders', async (req, res) => {
  try {
    const { id } = req.params;
    
    const result = await query(`
      SELECT * FROM vw_complete_shareholders 
      WHERE vendor_id = $1 
      ORDER BY created_at DESC
    `, [id]);
    
    res.json(result.rows);
  } catch (err) {
    console.error('Error fetching vendor shareholders:', err);
    res.status(500).json({ error: 'Failed to fetch vendor shareholders' });
  }
});

// Create a new shareholder (individual or corporate)
app.post('/api/vendors/:id/shareholders', async (req, res) => {
  const client = await getClient();
  try {
    await client.query('BEGIN');
    
    const { id } = req.params;
    const { shareholder_type, shareholding_percentage, ...shareholderData } = req.body;
    
    // Check if vendor exists and has EDD required
    const vendorCheck = await client.query(
      'SELECT vendor_id, edd_required FROM vendors WHERE vendor_id = $1', 
      [id]
    );
    if (vendorCheck.rows.length === 0) {
      await client.query('ROLLBACK');
      return res.status(404).json({ error: 'Vendor not found' });
    }
    if (!vendorCheck.rows[0].edd_required) {
      await client.query('ROLLBACK');
      return res.status(400).json({ error: 'EDD is not required for this vendor' });
    }
    
    // Create base shareholder record
    const shareholderResult = await client.query(`
      INSERT INTO shareholders (vendor_id, shareholder_type, shareholding_percentage)
      VALUES ($1, $2, $3)
      RETURNING shareholder_id
    `, [id, shareholder_type, shareholding_percentage]);
    
    const shareholder_id = shareholderResult.rows[0].shareholder_id;
    
    if (shareholder_type === 'individual') {
      // Create individual shareholder
      await client.query(`
        INSERT INTO individual_shareholders (
          shareholder_id, name, date_of_birth, address, nationality, 
          country_of_residency, occupation, email, phone_number, notes
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      `, [
        shareholder_id,
        shareholderData.name,
        shareholderData.date_of_birth || null,
        shareholderData.address || null,
        shareholderData.nationality || null,
        shareholderData.country_of_residency || null,
        shareholderData.occupation || null,
        shareholderData.email || null,
        shareholderData.phone_number || null,
        shareholderData.notes || null
      ]);
    } else if (shareholder_type === 'corporate') {
      // Create corporate shareholder
      await client.query(`
        INSERT INTO corporate_shareholders (
          shareholder_id, legal_name, brand_name, registration_number, 
          country_of_registration, legal_address, corporate_address, 
          business_type, email, phone_number, notes
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      `, [
        shareholder_id,
        shareholderData.legal_name,
        shareholderData.brand_name || null,
        shareholderData.registration_number || null,
        shareholderData.country_of_registration || null,
        shareholderData.legal_address || null,
        shareholderData.corporate_address || null,
        shareholderData.business_type || null,
        shareholderData.email || null,
        shareholderData.phone_number || null,
        shareholderData.notes || null
      ]);
    }
    
    await client.query('COMMIT');
    
    res.status(201).json({
      shareholder_id,
      message: 'Shareholder created successfully'
    });
  } catch (err) {
    await client.query('ROLLBACK');
    console.error('Error creating shareholder:', err);
    res.status(500).json({ error: 'Failed to create shareholder' });
  } finally {
    client.release();
  }
});

// Update a shareholder
app.put('/api/vendors/:id/shareholders/:shareholderId', async (req, res) => {
  const client = await getClient();
  try {
    await client.query('BEGIN');
    
    const { id, shareholderId } = req.params;
    const { shareholder_type, shareholding_percentage, ...shareholderData } = req.body;
    
    // Update base shareholder record
    const shareholderResult = await client.query(`
      UPDATE shareholders 
      SET shareholding_percentage = $1, updated_at = NOW()
      WHERE shareholder_id = $2 AND vendor_id = $3
      RETURNING shareholder_type
    `, [shareholding_percentage, shareholderId, id]);
    
    if (shareholderResult.rows.length === 0) {
      await client.query('ROLLBACK');
      return res.status(404).json({ error: 'Shareholder not found' });
    }
    
    const currentType = shareholderResult.rows[0].shareholder_type;
    
    if (currentType === 'individual' && shareholder_type === 'individual') {
      // Update individual shareholder
      await client.query(`
        UPDATE individual_shareholders 
        SET name = $1, date_of_birth = $2, address = $3, nationality = $4, 
            country_of_residency = $5, occupation = $6, email = $7, 
            phone_number = $8, notes = $9, updated_at = NOW()
        WHERE shareholder_id = $10
      `, [
        shareholderData.name,
        shareholderData.date_of_birth || null,
        shareholderData.address || null,
        shareholderData.nationality || null,
        shareholderData.country_of_residency || null,
        shareholderData.occupation || null,
        shareholderData.email || null,
        shareholderData.phone_number || null,
        shareholderData.notes || null,
        shareholderId
      ]);
    } else if (currentType === 'corporate' && shareholder_type === 'corporate') {
      // Update corporate shareholder
      await client.query(`
        UPDATE corporate_shareholders 
        SET legal_name = $1, brand_name = $2, registration_number = $3, 
            country_of_registration = $4, legal_address = $5, corporate_address = $6, 
            business_type = $7, email = $8, phone_number = $9, notes = $10, updated_at = NOW()
        WHERE shareholder_id = $11
      `, [
        shareholderData.legal_name,
        shareholderData.brand_name || null,
        shareholderData.registration_number || null,
        shareholderData.country_of_registration || null,
        shareholderData.legal_address || null,
        shareholderData.corporate_address || null,
        shareholderData.business_type || null,
        shareholderData.email || null,
        shareholderData.phone_number || null,
        shareholderData.notes || null,
        shareholderId
      ]);
    } else {
      await client.query('ROLLBACK');
      return res.status(400).json({ error: 'Cannot change shareholder type' });
    }
    
    await client.query('COMMIT');
    
    res.json({ message: 'Shareholder updated successfully' });
  } catch (err) {
    await client.query('ROLLBACK');
    console.error('Error updating shareholder:', err);
    res.status(500).json({ error: 'Failed to update shareholder' });
  } finally {
    client.release();
  }
});

// Delete a shareholder
app.delete('/api/vendors/:id/shareholders/:shareholderId', async (req, res) => {
  try {
    const { id, shareholderId } = req.params;
    
    const result = await query(`
      DELETE FROM shareholders 
      WHERE shareholder_id = $1 AND vendor_id = $2
      RETURNING shareholder_id
    `, [shareholderId, id]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Shareholder not found' });
    }
    
    res.json({ message: 'Shareholder deleted successfully' });
  } catch (err) {
    console.error('Error deleting shareholder:', err);
    res.status(500).json({ error: 'Failed to delete shareholder' });
  }
});

// Get senior management for a corporate shareholder
app.get('/api/vendors/:id/shareholders/:shareholderId/senior-management', async (req, res) => {
  try {
    const { id, shareholderId } = req.params;
    
    const result = await query(`
      SELECT sm.*, cs.legal_name as corporate_name
      FROM senior_management sm
      JOIN corporate_shareholders cs ON sm.corporate_shareholder_id = cs.corporate_shareholder_id
      JOIN shareholders s ON cs.shareholder_id = s.shareholder_id
      WHERE s.shareholder_id = $1 AND s.vendor_id = $2
      ORDER BY sm.created_at DESC
    `, [shareholderId, id]);
    
    res.json(result.rows);
  } catch (err) {
    console.error('Error fetching senior management:', err);
    res.status(500).json({ error: 'Failed to fetch senior management' });
  }
});

// Create senior management for a corporate shareholder
app.post('/api/vendors/:id/shareholders/:shareholderId/senior-management', async (req, res) => {
  try {
    const { id, shareholderId } = req.params;
    const { 
      name, role, date_of_birth, address, nationality, 
      country_of_residency, occupation, email, phone_number, notes 
    } = req.body;
    
    // Get corporate shareholder ID
    const corporateResult = await query(`
      SELECT cs.corporate_shareholder_id
      FROM corporate_shareholders cs
      JOIN shareholders s ON cs.shareholder_id = s.shareholder_id
      WHERE s.shareholder_id = $1 AND s.vendor_id = $2 AND s.shareholder_type = 'corporate'
    `, [shareholderId, id]);
    
    if (corporateResult.rows.length === 0) {
      return res.status(404).json({ error: 'Corporate shareholder not found' });
    }
    
    const corporate_shareholder_id = corporateResult.rows[0].corporate_shareholder_id;
    
    const result = await query(`
      INSERT INTO senior_management (
        corporate_shareholder_id, name, role, date_of_birth, address, 
        nationality, country_of_residency, occupation, email, phone_number, notes
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING senior_management_id
    `, [
      corporate_shareholder_id, name, role, 
      date_of_birth || null, address || null, nationality || null,
      country_of_residency || null, occupation || null, 
      email || null, phone_number || null, notes || null
    ]);
    
    res.status(201).json({
      senior_management_id: result.rows[0].senior_management_id,
      message: 'Senior management created successfully'
    });
  } catch (err) {
    console.error('Error creating senior management:', err);
    res.status(500).json({ error: 'Failed to create senior management' });
  }
});

// Upload document for shareholder or senior management
app.post('/api/vendors/:id/shareholders/:shareholderId/documents', upload.single('document'), async (req, res) => {
  try {
    const { id, shareholderId } = req.params;
    const { documentName, notes, doc_type, senior_management_id } = req.body;
    
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }
    
    // Check if shareholder exists
    const shareholderCheck = await query(`
      SELECT shareholder_id FROM shareholders 
      WHERE shareholder_id = $1 AND vendor_id = $2
    `, [shareholderId, id]);
    
    if (shareholderCheck.rows.length === 0) {
      fs.unlinkSync(req.file.path);
      return res.status(404).json({ error: 'Shareholder not found' });
    }
    
    // Insert document record
    const result = await query(`
      INSERT INTO shareholder_documents (
        vendor_id, shareholder_id, senior_management_id, doc_type, 
        document_name, file_path, notes
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `, [
      id,
      shareholderId,
      senior_management_id || null,
      doc_type || 'other',
      documentName || req.file.originalname,
      req.file.filename,
      notes || null
    ]);
    
    res.status(201).json({
      document: result.rows[0],
      message: 'Document uploaded successfully'
    });
  } catch (err) {
    console.error('Error uploading shareholder document:', err);
    if (req.file) {
      try {
        fs.unlinkSync(req.file.path);
      } catch (unlinkErr) {
        console.error('Error deleting uploaded file:', unlinkErr);
      }
    }
    res.status(500).json({ error: 'Failed to upload document' });
  }
});

// Get documents for a shareholder
app.get('/api/vendors/:id/shareholders/:shareholderId/documents', async (req, res) => {
  try {
    const { id, shareholderId } = req.params;
    
    const result = await query(`
      SELECT 
        document_id,
        doc_type,
        document_name,
        file_path,
        url,
        uploaded_at,
        notes,
        senior_management_id
      FROM shareholder_documents 
      WHERE vendor_id = $1 AND shareholder_id = $2
      ORDER BY uploaded_at DESC
    `, [id, shareholderId]);
    
    res.json(result.rows);
  } catch (err) {
    console.error('Error fetching shareholder documents:', err);
    res.status(500).json({ error: 'Failed to fetch documents' });
  }
});

// Get vendor review alerts
app.get('/api/vendor-review-alerts', async (req, res) => {
  try {
    const result = await query(`
      SELECT 
        v.vendor_id as id,
        v.company_name,
        v.vendor_type,
        r.review_id,
        r.last_review_date,
        r.next_review_date,
        r.review_notes,
        r.completed
      FROM vw_vendor_overview v
      INNER JOIN reviews r ON v.vendor_id = r.vendor_id
      WHERE r.next_review_date IS NOT NULL 
        AND r.completed = false
        AND v.vendor_active = true
      ORDER BY r.next_review_date ASC
    `);

    const vendors = result.rows;
    const notifications: any[] = [];
    const today = new Date();

    vendors.forEach((vendor: any) => {
      try {
        if (vendor.next_review_date) {
          const reviewDate = new Date(vendor.next_review_date);
          const daysUntilReview = Math.ceil(
            (reviewDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
          );
          
          if (daysUntilReview <= 0) {
            // Overdue reviews
            notifications.push({
              id: `review-overdue-${vendor.id}`,
              type: 'review',
              vendor_id: vendor.id,
              vendor_name: vendor.company_name,
              due_date: vendor.next_review_date,
              days_remaining: daysUntilReview,
              severity: 'error',
              alert_type: 'overdue',
              risk_level: vendor.vendor_type,
              days_overdue: Math.abs(daysUntilReview)
            });
          } else if (daysUntilReview <= 30) {
            // 1 month or less - urgent
            notifications.push({
              id: `review-urgent-${vendor.id}`,
              type: 'review',
              vendor_id: vendor.id,
              vendor_name: vendor.company_name,
              due_date: vendor.next_review_date,
              days_remaining: daysUntilReview,
              severity: 'warning',
              alert_type: 'urgent',
              risk_level: vendor.vendor_type
            });
          } else if (daysUntilReview <= 90) {
            // 3 months or less - warning
            notifications.push({
              id: `review-warning-${vendor.id}`,
              type: 'review',
              vendor_id: vendor.id,
              vendor_name: vendor.company_name,
              due_date: vendor.next_review_date,
              days_remaining: daysUntilReview,
              severity: 'info',
              alert_type: 'warning',
              risk_level: vendor.vendor_type
            });
          }
        }
      } catch (e) {
        console.error(`Error processing review dates for vendor ${vendor.company_name}:`, e);
      }
    });

    // Sort notifications by days remaining (most urgent first)
    const sortedNotifications = notifications.sort((a: any, b: any) => {
      return a.days_remaining - b.days_remaining;
    });

    // Calculate summary statistics
    const summary = {
      total_review_alerts: sortedNotifications.length,
      overdue_reviews: sortedNotifications.filter(n => n.alert_type === 'overdue').length,
      urgent_reviews: sortedNotifications.filter(n => n.alert_type === 'urgent' && n.days_remaining <= 30).length,
      warning_reviews: sortedNotifications.filter(n => n.alert_type === 'warning' && n.days_remaining <= 90).length,
      high_risk_vendors_affected: sortedNotifications.filter(n => n.risk_level === 'critical' || n.risk_level === 'high').length
    };
    
    res.json({
      alerts: sortedNotifications,
      summary: summary,
      generated_at: new Date().toISOString()
    });
  } catch (err) {
    console.error('Error fetching vendor review alerts:', err);
    res.status(500).json({ error: 'Failed to fetch vendor review alerts' });
  }
});

// HOLIDAYS ROUTES  
app.get('/api/holidays', async (req, res) => {
  try {
    // For now, return empty array since holidays table doesn't exist yet
    // This prevents the frontend from crashing
    res.json([]);
  } catch (err) {
    console.error('Error fetching holidays:', err);
    res.status(500).json({ error: 'Failed to fetch holidays' });
  }
});

// Test endpoint
app.get('/api/test', (req, res) => {
  res.json({ message: 'Backend is working with PostgreSQL!' });
});

// Serve static files in production
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(join(__dirname, '../../frontend/dist')));
  
  app.get('*', (req, res) => {
    if (!req.path.startsWith('/api')) {
      res.sendFile(join(__dirname, '../../frontend/dist/index.html'));
    }
  });
}

// PERFORMANCE REPORTS API ENDPOINTS

// Get performance reports for a member
app.get('/api/members/:id/performance-reports', async (req, res) => {
  try {
    const memberId = req.params.id;
    const result = await query(
      'SELECT * FROM performance_reports WHERE member_id = $1 ORDER BY review_date DESC',
      [memberId]
    );
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching performance reports:', error);
    res.status(500).json({ error: 'Failed to fetch performance reports' });
  }
});

// Create a new performance report
app.post('/api/members/:id/performance-reports', async (req, res) => {
  try {
    const memberId = req.params.id;
    const { review_date, kpi_data, review_notes, report_document_url } = req.body;
    
    // Calculate next review date (6 months from review date)
    const reviewDate = new Date(review_date);
    const nextReviewDate = new Date(reviewDate);
    nextReviewDate.setMonth(nextReviewDate.getMonth() + 6);
    
    const result = await query(`
      INSERT INTO performance_reports (member_id, review_date, next_review_date, kpi_data, review_notes, report_document_url, status)
      VALUES ($1, $2, $3, $4, $5, $6, 'completed')
      RETURNING *
    `, [memberId, review_date, nextReviewDate.toISOString().split('T')[0], JSON.stringify(kpi_data), review_notes, report_document_url]);
    
    res.status(201).json({
      report: result.rows[0],
      message: 'Performance report created successfully'
    });
  } catch (error) {
    console.error('Error creating performance report:', error);
    res.status(500).json({ error: 'Failed to create performance report' });
  }
});

// Update performance report
app.put('/api/members/:id/performance-reports/:reportId', async (req, res) => {
  try {
    const { reportId } = req.params;
    const { review_date, kpi_data, review_notes, report_document_url, status } = req.body;
    
    let nextReviewDate = null;
    if (review_date) {
      const reviewDate = new Date(review_date);
      nextReviewDate = new Date(reviewDate);
      nextReviewDate.setMonth(nextReviewDate.getMonth() + 6);
    }
    
    const result = await query(`
      UPDATE performance_reports 
      SET review_date = COALESCE($1, review_date),
          next_review_date = COALESCE($2, next_review_date),
          kpi_data = COALESCE($3, kpi_data),
          review_notes = COALESCE($4, review_notes),
          report_document_url = COALESCE($5, report_document_url),
          status = COALESCE($6, status),
          updated_at = NOW()
      WHERE id = $7
      RETURNING *
    `, [review_date, nextReviewDate?.toISOString().split('T')[0], JSON.stringify(kpi_data), review_notes, report_document_url, status, reportId]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Performance report not found' });
    }
    
    res.json({
      report: result.rows[0],
      message: 'Performance report updated successfully'
    });
  } catch (error) {
    console.error('Error updating performance report:', error);
    res.status(500).json({ error: 'Failed to update performance report' });
  }
});

// Get member performance summary
app.get('/api/members/:id/performance-summary', async (req, res) => {
  try {
    const memberId = req.params.id;
    
    const result = await query(`
      SELECT 
        m.id,
        m.name,
        m.joining_date,
        m.last_performance_review,
        m.next_performance_review,
        m.performance_review_count,
        CASE 
          WHEN m.next_performance_review < CURRENT_DATE THEN 'overdue'
          WHEN m.next_performance_review <= CURRENT_DATE + INTERVAL '30 days' THEN 'due_soon'
          ELSE 'on_track'
        END as status
      FROM members m
      WHERE m.id = $1
    `, [memberId]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Member not found' });
    }
    
    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error fetching performance summary:', error);
    res.status(500).json({ error: 'Failed to fetch performance summary' });
  }
});

// Upload performance report document
app.post('/api/members/:id/performance-reports/:reportId/documents', upload.single('document'), async (req, res) => {
  try {
    const { reportId } = req.params;
    const { documentName, notes } = req.body;
    
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }
    
    // Update the performance report with document URL
    const result = await query(`
      UPDATE performance_reports 
      SET report_document_url = $1,
          updated_at = NOW()
      WHERE id = $2
      RETURNING *
    `, [req.file.filename, reportId]);
    
    if (result.rows.length === 0) {
      // Clean up uploaded file if report not found
      fs.unlinkSync(req.file.path);
      return res.status(404).json({ error: 'Performance report not found' });
    }
    
    res.status(201).json({
      report: result.rows[0],
      message: 'Document uploaded successfully'
    });
  } catch (error) {
    console.error('Error uploading performance report document:', error);
    if (req.file) {
      try {
        fs.unlinkSync(req.file.path);
      } catch (unlinkErr) {
        console.error('Error deleting uploaded file:', unlinkErr);
      }
    }
    res.status(500).json({ error: 'Failed to upload document' });
  }
});

// Get performance review alerts
app.get('/api/performance-review-alerts', async (req, res) => {
  try {
    const result = await query(`
      SELECT 
        m.id,
        m.name,
        m.joining_date,
        m.last_performance_review,
        m.next_performance_review,
        m.performance_review_count,
        d.name as department_name,
        CASE 
          WHEN m.next_performance_review < CURRENT_DATE THEN 'overdue'
          WHEN m.next_performance_review <= CURRENT_DATE + INTERVAL '30 days' THEN 'due_soon'
          ELSE 'upcoming'
        END as alert_type
      FROM members m
      LEFT JOIN departments d ON m.department_id = d.id
      WHERE m.next_performance_review IS NOT NULL
        AND m.active = true
      ORDER BY m.next_performance_review ASC
    `);
    
    const members = result.rows;
    const notifications: any[] = [];
    const today = new Date();

    members.forEach((member: any) => {
      try {
        if (member.next_performance_review) {
          const reviewDate = new Date(member.next_performance_review);
          const daysUntilReview = Math.ceil(
            (reviewDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
          );
          
          notifications.push({
            id: `performance-${member.id}`,
            type: 'performance_review',
            member_id: member.id,
            member_name: member.name,
            department_name: member.department_name,
            due_date: member.next_performance_review,
            days_remaining: daysUntilReview,
            alert_type: member.alert_type,
            review_count: member.performance_review_count,
            last_review: member.last_performance_review
          });
        }
      } catch (e) {
        console.error(`Error processing dates for member ${member.name}:`, e);
      }
    });

    const sortedNotifications = notifications.sort((a: any, b: any) => {
      return a.days_remaining - b.days_remaining;
    });

    res.json({
      alerts: sortedNotifications,
      generated_at: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error fetching performance review alerts:', error);
    res.status(500).json({ error: 'Failed to fetch performance review alerts' });
  }
});

// 404 handler
app.use((req, res) => {
  console.log(`404: ${req.method} ${req.url}`);
  res.status(404).json({ error: 'Not Found' });
});

// Error handler
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Error:', err);
  res.status(500).json({ error: 'Internal Server Error' });
});

app.listen(port, () => {
  console.log(`🚀 Server running on port ${port}`);
  console.log(`📊 Health check: http://localhost:${port}/api/health`);
}); 