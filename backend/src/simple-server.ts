import express from 'express';
import cors from 'cors';

const app = express();
const port = 3030;

app.use(cors());
app.use(express.json());

// In-memory vendor data for now
let vendors: any[] = [
  {
    id: '1',
    company_name: 'Sample Vendor 1',
    website: 'https://example1.com',
    vendor_type: 'technology_provider',
    service_type: 'it_services',
    lead_manager: '<PERSON>',
    lead_manager_email: '<EMAIL>',
    countries_of_operation: ['USA', 'UK'],
    incorporation_date: '2020-01-01',
    registration_number: '12345',
    edd_required: false,
    active: true,
    contract_id: 1,
    contract_active: true,
    contract_start_date: '2023-01-01',
    contract_renewal_date: '2024-01-01',
    contract_end_date: '2024-12-31',
    contract_notes: 'Sample contract',
    contract_status: 'active',
    created_at: '2023-01-01T00:00:00.000Z',
    updated_at: '2023-01-01T00:00:00.000Z'
  },
  {
    id: '2',
    company_name: 'Sample Vendor 2',
    website: 'https://example2.com',
    vendor_type: 'consulting',
    service_type: 'financial_advisory',
    lead_manager: '<PERSON>',
    lead_manager_email: '<EMAIL>',
    countries_of_operation: ['Canada'],
    incorporation_date: '2019-06-15',
    registration_number: '67890',
    edd_required: true,
    active: true,
    contract_id: 2,
    contract_active: false,
    contract_start_date: '2022-06-01',
    contract_renewal_date: '2023-06-01',
    contract_end_date: '2023-05-31',
    contract_notes: 'Expired contract',
    contract_status: 'expired',
    created_at: '2022-06-01T00:00:00.000Z',
    updated_at: '2023-06-01T00:00:00.000Z'
  }
];

// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'healthy', database: 'connected' });
});

// Vendors endpoints
app.get('/api/vendors', (req, res) => {
  console.log('📊 Fetching vendors:', vendors.length);
  res.json(vendors);
});

app.get('/api/vendors/:id', (req, res) => {
  const vendor = vendors.find(v => v.id === req.params.id);
  if (vendor) {
    res.json(vendor);
  } else {
    res.status(404).json({ error: 'Vendor not found' });
  }
});

app.post('/api/vendors', (req, res) => {
  const newVendor = {
    id: String(Date.now()),
    company_name: req.body.company_name || 'New Vendor',
    website: req.body.website || '',
    vendor_type: req.body.vendor_type || 'other',
    service_type: req.body.service_type || 'other',
    lead_manager: req.body.lead_manager || '',
    lead_manager_email: req.body.lead_manager_email || '',
    countries_of_operation: req.body.countries_of_operation || [],
    incorporation_date: req.body.incorporation_date || null,
    registration_number: req.body.registration_number || '',
    edd_required: req.body.edd_required || false,
    active: true,
    contract_id: null,
    contract_active: req.body.contract_active || false,
    contract_start_date: req.body.contract_start_date || null,
    contract_renewal_date: req.body.contract_renewal_date || null,
    contract_end_date: req.body.contract_end_date || null,
    contract_notes: req.body.contract_notes || '',
    contract_status: req.body.contract_active ? 'active' : 'inactive',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
  
  vendors.push(newVendor);
  console.log('➕ Added vendor:', newVendor.company_name);
  res.status(201).json(newVendor);
});

app.put('/api/vendors/:id', (req, res) => {
  const index = vendors.findIndex(v => v.id === req.params.id);
  if (index !== -1) {
    vendors[index] = { ...vendors[index], ...req.body, updated_at: new Date().toISOString() };
    console.log('✏️ Updated vendor:', vendors[index].company_name);
    res.json(vendors[index]);
  } else {
    res.status(404).json({ error: 'Vendor not found' });
  }
});

app.delete('/api/vendors/:id', (req, res) => {
  const index = vendors.findIndex(v => v.id === req.params.id);
  if (index !== -1) {
    const deleted = vendors.splice(index, 1)[0];
    console.log('🗑️ Deleted vendor:', deleted.company_name);
    res.json({ message: 'Vendor deleted successfully' });
  } else {
    res.status(404).json({ error: 'Vendor not found' });
  }
});

// Other required endpoints (simplified)
app.get('/api/members', (req, res) => {
  res.json([]);
});

app.get('/api/committees', (req, res) => {
  res.json([]);
});

app.get('/api/trainings', (req, res) => {
  res.json([]);
});

app.get('/api/policies', (req, res) => {
  res.json([]);
});

app.get('/api/meetings', (req, res) => {
  res.json([]);
});

app.get('/api/departments', (req, res) => {
  res.json([]);
});

app.get('/api/committee-memberships', (req, res) => {
  res.json([]);
});

app.get('/api/training-sessions', (req, res) => {
  res.json([]);
});

app.get('/api/attendance', (req, res) => {
  res.json([]);
});

app.get('/api/holidays', (req, res) => {
  res.json([]);
});

// Catch all
app.use((req, res) => {
  res.status(404).json({ error: 'Not Found' });
});

app.listen(port, () => {
  console.log(`🚀 Simple server running on port ${port}`);
  console.log(`📊 Health check: http://localhost:${port}/api/health`);
  console.log(`📝 Vendors: http://localhost:${port}/api/vendors`);
}); 