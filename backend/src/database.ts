import { Pool, PoolClient } from 'pg';
import dotenv from 'dotenv';

dotenv.config();

// Default to a simple database URL if none provided
const defaultDatabaseUrl = 'postgresql://governance_user:governance_password@localhost:5433/governance_app';
const databaseUrl = process.env.DATABASE_URL || defaultDatabaseUrl;

const pool = new Pool({
  connectionString: databaseUrl,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

// Test connection on startup but don't crash if it fails
pool.connect((err, client, release) => {
  if (err) {
    console.error('❌ Error connecting to database:', err.message);
    console.log('⚠️  Database not available, some features may not work');
    console.log(`🔧 Trying to connect to: ${databaseUrl}`);
  } else {
    console.log('✅ Database connected successfully');
    release();
  }
});

// Helper function to execute queries
export const query = async (text: string, params?: any[]): Promise<any> => {
  const start = Date.now();
  try {
    const res = await pool.query(text, params);
    const duration = Date.now() - start;
    console.log('🔍 Executed query', { text: text.substring(0, 50) + '...', duration, rows: res.rowCount });
    return res;
  } catch (error: any) {
    console.error('❌ Database query error:', error.message);
    console.error('🔍 Failed query:', text.substring(0, 100) + '...');
    throw error;
  }
};

// Helper function to get a client for transactions
export const getClient = async (): Promise<PoolClient> => {
  try {
    return await pool.connect();
  } catch (error: any) {
    console.error('❌ Failed to get database client:', error.message);
    throw error;
  }
};

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('📤 Shutting down database connection...');
  await pool.end();
  process.exit(0);
});

export { pool }; 